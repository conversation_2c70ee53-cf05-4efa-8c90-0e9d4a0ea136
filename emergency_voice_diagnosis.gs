// == 緊急語音問題診斷 ==
// 🚨 診斷為什麼音頻檔案是 0 秒且 Cloudinary 未啟用

/**
 * 🚨 緊急診斷：語音轉換問題
 * 找出音頻為 0 秒和 Cloudinary 未啟用的原因
 */
function emergencyVoiceDiagnosis() {
  console.log('🚨 === 緊急語音問題診斷 ===');
  console.log('🎯 問題：音頻檔案 0 秒，Cloudinary 未啟用');
  console.log('');

  try {
    // 1. 檢查 Cloudinary 配置
    console.log('📋 1. 檢查 Cloudinary 配置...');
    const config = getConfig();
    
    console.log(`   Cloud Name: "${config.cloudinaryCloudName}"`);
    console.log(`   API Key: "${config.cloudinaryApiKey}"`);
    console.log(`   API Secret: "${config.cloudinaryApiSecret ? '***已設定***' : '未設定'}"`);
    
    const cloudinaryOK = config.cloudinaryCloudName && config.cloudinaryApiKey && config.cloudinaryApiSecret;
    console.log(`   🌩️ Cloudinary 狀態: ${cloudinaryOK ? '✅ 已配置' : '❌ 配置不完整'}`);

    // 2. 檢查 Gemini API 配置
    console.log('\n🤖 2. 檢查 Gemini API 配置...');
    console.log(`   API Key: ${config.geminiApiKey ? '✅ 已設定' : '❌ 未設定'}`);
    console.log(`   Drive 資料夾: ${config.folderId ? '✅ 已設定' : '❌ 未設定'}`);

    // 3. 測試 TTS 模型配置
    console.log('\n🔊 3. 檢查 TTS 模型配置...');
    testTTSModelConfig();

    // 4. 測試實際 TTS 調用（安全模式）
    console.log('\n🧪 4. 測試 TTS API 調用...');
    testTTSAPICall();

    // 5. 檢查活動日誌中的錯誤
    console.log('\n📝 5. 檢查最近的錯誤日誌...');
    checkRecentErrorLogs();

    console.log('\n📊 診斷完成！請查看上方的詳細結果');
    return '診斷完成';

  } catch (error) {
    console.error('❌ 診斷過程中發生錯誤:', error);
    return `診斷失敗: ${error.message}`;
  }
}

/**
 * 🔊 測試 TTS 模型配置
 */
function testTTSModelConfig() {
  try {
    const recommendedModel = getRecommendedModel('tts');
    console.log(`   推薦的 TTS 模型: ${recommendedModel}`);
    
    const apiVersion = getModelApiVersion(recommendedModel);
    console.log(`   API 版本: ${apiVersion}`);
    
    const config = getConfig();
    const url = `https://generativelanguage.googleapis.com/${apiVersion}/models/${recommendedModel}:generateContent?key=${config.geminiApiKey}`;
    console.log(`   API URL: ${url.replace(config.geminiApiKey, '***API_KEY***')}`);
    
    console.log('   ✅ TTS 模型配置檢查完成');
    
  } catch (error) {
    console.log(`   ❌ TTS 模型配置錯誤: ${error.message}`);
  }
}

/**
 * 🧪 測試 TTS API 調用（簡化版本）
 */
function testTTSAPICall() {
  try {
    const config = getConfig();
    
    if (!config.geminiApiKey) {
      console.log('   ❌ 缺少 Gemini API Key');
      return;
    }

    console.log('   🔄 嘗試調用 Gemini TTS API...');
    
    // 使用簡單的測試文本
    const testText = '測試';
    console.log(`   📝 測試文本: "${testText}"`);
    
    // 調用 TTS 函數
    const result = textToSpeechWithGemini(testText);
    
    console.log('   📊 TTS 調用結果:');
    console.log(`      成功: ${result.success}`);
    console.log(`      檔案名稱: ${result.fileName || '無'}`);
    console.log(`      可播放: ${result.isPlayable || false}`);
    console.log(`      Cloudinary URL: ${result.cloudinaryUrl || '無'}`);
    console.log(`      Drive URL: ${result.driveUrl || '無'}`);
    
    if (!result.success) {
      console.log(`      ❌ 錯誤: ${result.error}`);
    }
    
    if (result.cloudinaryError) {
      console.log(`      ⚠️ Cloudinary 錯誤: ${result.cloudinaryError}`);
    }

  } catch (error) {
    console.log(`   ❌ TTS API 調用測試失敗: ${error.message}`);
  }
}

/**
 * 📝 檢查最近的錯誤日誌
 */
function checkRecentErrorLogs() {
  try {
    const sheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName('活動日誌');
    
    if (!sheet) {
      console.log('   ⚠️ 找不到活動日誌工作表');
      return;
    }
    
    const data = sheet.getDataRange().getValues();
    const recentLogs = data.slice(-10); // 最近 10 條記錄
    
    console.log('   📋 最近的活動日誌:');
    recentLogs.forEach((row, index) => {
      if (row[0] && row[1]) { // 確保有時間戳和動作
        const timestamp = new Date(row[0]).toLocaleString();
        const action = row[1];
        const details = row[2] || '';
        
        if (action.includes('錯誤') || action.includes('失敗') || details.includes('錯誤')) {
          console.log(`      ❌ [${timestamp}] ${action}: ${details}`);
        } else if (action.includes('TTS') || action.includes('音頻') || action.includes('Cloudinary')) {
          console.log(`      ℹ️ [${timestamp}] ${action}: ${details}`);
        }
      }
    });
    
  } catch (error) {
    console.log(`   ⚠️ 無法檢查錯誤日誌: ${error.message}`);
  }
}

/**
 * 🔧 修復 Cloudinary 簽名問題
 * 常見的 Cloudinary 上傳失敗原因
 */
function fixCloudinarySignature() {
  console.log('🔧 === 修復 Cloudinary 簽名問題 ===');
  
  try {
    const config = getConfig();
    
    // 檢查配置
    if (!config.cloudinaryCloudName || !config.cloudinaryApiKey || !config.cloudinaryApiSecret) {
      console.log('❌ Cloudinary 配置不完整');
      return;
    }
    
    console.log('✅ Cloudinary 配置完整');
    
    // 測試簽名生成
    const testParams = {
      public_id: 'test_signature',
      timestamp: Math.floor(Date.now() / 1000)
    };
    
    console.log('🧪 測試簽名生成...');
    const signature = generateCloudinarySignature(testParams, config.cloudinaryApiSecret);
    console.log(`   簽名長度: ${signature.length}`);
    console.log(`   簽名格式: ${/^[a-f0-9]{40}$/.test(signature) ? '✅ 正確' : '❌ 錯誤'}`);
    
    // 測試 API 連接
    console.log('🌐 測試 Cloudinary API 連接...');
    testCloudinaryConnection();
    
  } catch (error) {
    console.error('❌ 修復過程錯誤:', error);
  }
}

/**
 * 🌐 測試 Cloudinary 連接
 */
function testCloudinaryConnection() {
  const config = getConfig();
  
  try {
    // 創建測試檔案
    const testBlob = Utilities.newBlob('test audio data', 'audio/wav', 'test.wav');
    
    console.log('   📁 創建測試檔案...');
    console.log(`   📏 檔案大小: ${testBlob.getBytes().length} bytes`);
    
    // 測試上傳參數
    const timestamp = Math.floor(Date.now() / 1000);
    const publicId = `test/audio_${timestamp}`;
    
    const uploadParams = {
      public_id: publicId,
      resource_type: 'video',
      format: 'm4a',
      timestamp: timestamp
    };
    
    console.log(`   🔑 Public ID: ${publicId}`);
    console.log(`   ⏰ Timestamp: ${timestamp}`);
    
    // 生成簽名
    const signature = generateCloudinarySignature(uploadParams, config.cloudinaryApiSecret);
    console.log(`   ✍️ 簽名: ${signature.substring(0, 10)}...`);
    
    console.log('   ✅ Cloudinary 連接測試準備完成');
    console.log('   💡 可以嘗試執行實際上傳測試');
    
  } catch (error) {
    console.log(`   ❌ Cloudinary 連接測試失敗: ${error.message}`);
  }
}

/**
 * 📋 顯示完整的問題解決清單
 */
function showTroubleshootingSteps() {
  console.log('📋 === 語音問題解決步驟 ===');
  console.log('');
  
  console.log('🔍 常見問題和解決方案:');
  console.log('');
  
  console.log('1. ❌ 音頻檔案 0 秒');
  console.log('   原因: Gemini TTS API 調用失敗');
  console.log('   解決: 檢查 API Key 權限，確保支援 TTS 功能');
  console.log('');
  
  console.log('2. ❌ Cloudinary 未啟用');
  console.log('   原因: API Key 配置錯誤或簽名問題');
  console.log('   解決: 重新檢查 B8、B9、B10 的設定');
  console.log('');
  
  console.log('3. ❌ Google Drive 檔案無法播放');
  console.log('   原因: 格式不相容或檔案損壞');
  console.log('   解決: 確保 Cloudinary 正常工作');
  console.log('');
  
  console.log('🔧 立即執行的診斷命令:');
  console.log('   emergencyVoiceDiagnosis()    - 完整診斷');
  console.log('   fixCloudinarySignature()     - 修復 Cloudinary');
  console.log('   testTTSAPICall()            - 測試 TTS API');
  console.log('');
  
  console.log('💡 如果問題持續，請執行上述命令並提供結果');
}

/**
 * 🚀 一鍵快速修復
 */
function quickVoiceFix() {
  console.log('🚀 === 一鍵快速修復 ===');
  
  try {
    // 1. 檢查配置
    const config = getConfig();
    const hasCloudinary = config.cloudinaryCloudName && config.cloudinaryApiKey && config.cloudinaryApiSecret;
    
    if (!hasCloudinary) {
      console.log('❌ Cloudinary 配置不完整，請檢查 B8、B9、B10');
      showCloudinaryConfig();
      return '配置不完整';
    }
    
    console.log('✅ Cloudinary 配置檢查通過');
    
    // 2. 測試簡單的 TTS
    console.log('🔊 測試 TTS 功能...');
    const result = textToSpeechWithGemini('測試');
    
    if (result.success) {
      console.log('✅ TTS 生成成功');
      console.log(`   可播放: ${result.isPlayable}`);
      console.log(`   Cloudinary: ${result.cloudinaryUrl ? '✅' : '❌'}`);
      
      if (result.isPlayable) {
        console.log('🎉 修復成功！語音功能應該正常了');
        return '修復成功';
      } else {
        console.log('⚠️ TTS 成功但 Cloudinary 上傳失敗');
        console.log(`   錯誤: ${result.cloudinaryError}`);
        return '部分修復';
      }
    } else {
      console.log('❌ TTS 生成失敗');
      console.log(`   錯誤: ${result.error}`);
      return '修復失敗';
    }
    
  } catch (error) {
    console.error('❌ 快速修復失敗:', error);
    return `修復失敗: ${error.message}`;
  }
}

/**
 * 📋 顯示 Cloudinary 配置
 */
function showCloudinaryConfig() {
  const config = getConfig();
  
  console.log('📋 當前 Cloudinary 配置:');
  console.log(`   B8 - Cloud Name: "${config.cloudinaryCloudName}"`);
  console.log(`   B9 - API Key: "${config.cloudinaryApiKey}"`);
  console.log(`   B10 - API Secret: ${config.cloudinaryApiSecret ? '***已設定***' : '未設定'}`);
  console.log('');
  console.log('💡 請確認這些值與 Cloudinary Dashboard 中的完全一致');
}
