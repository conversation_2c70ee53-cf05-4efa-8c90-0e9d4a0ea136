// 🧪 測試新功能分類系統的完整驗證
function testCompleteFunctionalSystemValidation_debug() {
  console.log('=== 完整功能分類系統驗證 v3.0 ===');
  
  try {
    // 1. 測試模型配置系統
    console.log('📋 測試功能分類配置...');
    const testResult1 = testFunctionalCategoriesSystem_debug();
    console.log(`結果: ${testResult1}`);
    
    // 2. 測試 Imagen 4.0 模型是否正確包含
    console.log('\n🎨 驗證 Imagen 4.0 模型配置...');
    const imageGenModels = getModelsByCategory('image_generation');
    const hasImagen4Standard = imageGenModels.includes('imagen-4.0-generate-preview-06-06');
    const hasImagen4Ultra = imageGenModels.includes('imagen-4.0-ultra-generate-preview-06-06');
    
    console.log(`圖片生成模型總數: ${imageGenModels.length}`);
    console.log(`包含 Imagen 4.0 標準版: ${hasImagen4Standard ? '✅' : '❌'}`);
    console.log(`包含 Imagen 4.0 Ultra: ${hasImagen4Ultra ? '✅' : '❌'}`);
    console.log(`圖片生成模型清單: ${imageGenModels.join(', ')}`);
    
    // 3. 測試每個功能類別的獨立性
    console.log('\n🔍 驗證功能類別獨立性...');
    const categories = getAllFunctionalCategories();
    const independenceResults = [];
    
    categories.forEach(category => {
      const config = FUNCTIONAL_MODEL_CATEGORIES[category];
      const models = getModelsByCategory(category);
      const defaultModel = getDefaultModelForCategory(category);
      const helpText = getHelpTextForCategory(category);
      
      const result = {
        category: category,
        name: config.categoryName,
        row: config.sheetRow,
        modelCount: models.length,
        hasDefault: !!defaultModel,
        hasHelp: !!helpText,
        defaultInList: models.includes(defaultModel)
      };
      
      independenceResults.push(result);
      
      const status = result.hasDefault && result.hasHelp && result.defaultInList ? '✅' : '❌';
      console.log(`${status} ${result.name}: ${result.modelCount} 模型, 預設: ${defaultModel}`);
    });
    
    // 4. 測試行號映射
    console.log('\n📍 驗證行號映射...');
    for (let row = 14; row <= 20; row++) {
      const category = getCategoryBySheetRow(row);
      if (category) {
        const config = FUNCTIONAL_MODEL_CATEGORIES[category];
        console.log(`✅ B${row}: ${config.categoryName}`);
      } else {
        console.log(`❌ B${row}: 未映射`);
      }
    }
    
    // 5. 測試模型驗證系統
    console.log('\n🎯 測試模型驗證系統...');
    const testCases = [
      { model: 'gemini-2.5-flash', function: 'general' },
      { model: 'gemini-2.5-pro', function: 'vision' },
      { model: 'gemini-2.5-flash-preview-tts', function: 'tts' },
      { model: 'gemini-2.0-flash-preview-image-generation', function: 'image_generation' },
      { model: 'imagen-4.0-generate-preview-06-06', function: 'image_generation' },
      { model: 'gemini-embedding-exp', function: 'embedding' }
    ];
    
    testCases.forEach(testCase => {
      const recommended = getRecommendedModelForFunction(testCase.function);
      const validation = validateModelForFunction(testCase.model, testCase.function);
      
      console.log(`🧪 ${testCase.function}: ${testCase.model} → ${validation.isValid ? '✅' : '❌'}`);
    });
    
    // 6. 統計結果
    const totalCategories = categories.length;
    const validCategories = independenceResults.filter(r => r.hasDefault && r.hasHelp && r.defaultInList).length;
    const imageGenSuccess = hasImagen4Standard && hasImagen4Ultra;
    
    console.log('\n📊 系統驗證總結:');
    console.log(`功能分類: ${validCategories}/${totalCategories} 正確配置`);
    console.log(`Imagen 4.0: ${imageGenSuccess ? '✅ 已正確包含' : '❌ 配置有問題'}`);
    console.log(`圖片生成模型: ${imageGenModels.length} 個可用`);
    
    const overallSuccess = validCategories === totalCategories && imageGenSuccess;
    const resultMessage = `${overallSuccess ? '✅' : '❌'} 功能分類系統驗證${overallSuccess ? '成功' : '失敗'}`;
    
    console.log(`\n🎯 ${resultMessage}`);
    
    return {
      success: overallSuccess,
      categories: totalCategories,
      validCategories: validCategories,
      imageGenModels: imageGenModels.length,
      hasImagen4: imageGenSuccess,
      message: resultMessage
    };
    
  } catch (error) {
    console.error('❌ 系統驗證失敗:', error);
    return {
      success: false,
      error: error.message,
      message: `❌ 系統驗證失敗: ${error.message}`
    };
  }
}

// 🧪 快速檢查 Imagen 4.0 模型配置
function quickCheckImagen4Models_debug() {
  console.log('=== 快速檢查 Imagen 4.0 模型 ===');
  
  try {
    const imageGenModels = getModelsByCategory('image_generation');
    const allImageModels = getImageGenerationModels();
    
    console.log('🎨 圖片生成模型清單:');
    imageGenModels.forEach((model, index) => {
      const config = getModelConfig(model);
      const isImagen4 = model.includes('imagen-4.0');
      const prefix = isImagen4 ? '🆕' : '📸';
      console.log(`${prefix} ${index + 1}. ${model}`);
      console.log(`   說明: ${config.description}`);
    });
    
    const imagen4Models = imageGenModels.filter(model => model.includes('imagen-4.0'));
    console.log(`\n📊 Imagen 4.0 模型數量: ${imagen4Models.length}`);
    console.log(`📊 總圖片生成模型: ${imageGenModels.length}`);
    
    return {
      totalImageModels: imageGenModels.length,
      imagen4Count: imagen4Models.length,
      imagen4Models: imagen4Models,
      success: imagen4Models.length === 2
    };
    
  } catch (error) {
    console.error('❌ 檢查失敗:', error);
    return { success: false, error: error.message };
  }
}
