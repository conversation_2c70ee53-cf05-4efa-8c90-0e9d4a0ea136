// == WAV 檔案修復診斷 ==
// 🚨 診斷和修復 WAV 檔案標頭問題和 Cloudinary 上傳失敗

/**
 * 🚨 WAV 檔案標頭修復
 * 修復 Gemini TTS 生成的 WAV 檔案標頭問題
 */
function fixWAVHeaderIssue() {
  console.log('🚨 === WAV 檔案標頭修復診斷 ===');
  console.log('🎯 問題：WAV 檔案有數據但無法播放');
  console.log('');

  try {
    // 1. 檢查 TTS API 回應格式
    console.log('📋 1. 診斷 TTS API 回應格式...');
    diagnoseTTSResponse();

    // 2. 測試正確的 WAV 檔案生成
    console.log('\n🔧 2. 測試修復的 WAV 檔案生成...');
    testFixedWAVGeneration();

    // 3. 檢查 Cloudinary 配置和上傳
    console.log('\n🌩️ 3. 診斷 Cloudinary 問題...');
    diagnoseCloudinaryIssue();

    console.log('\n📊 診斷完成！');
    return '診斷完成';

  } catch (error) {
    console.error('❌ 診斷過程錯誤:', error);
    return `診斷失敗: ${error.message}`;
  }
}

/**
 * 📋 診斷 TTS API 回應格式
 */
function diagnoseTTSResponse() {
  try {
    console.log('   🔍 檢查 Gemini TTS API 設定...');
    
    const config = getConfig();
    const modelName = 'gemini-2.5-flash-preview-tts';
    const apiVersion = 'v1beta';
    
    console.log(`   模型: ${modelName}`);
    console.log(`   API 版本: ${apiVersion}`);
    
    // 檢查 API 設定
    const payload = {
      contents: [{
        parts: [{ text: '測試' }]
      }],
      generationConfig: {
        responseModalities: ["AUDIO"],
        speechConfig: {
          voiceConfig: {
            prebuiltVoiceConfig: {
              voiceName: 'Kore'
            }
          }
        }
      }
    };
    
    console.log('   ✅ TTS API 設定格式正確');
    console.log('   📝 請執行 testTTSWithDebugging() 來檢查實際 API 回應');

  } catch (error) {
    console.log(`   ❌ TTS API 設定檢查失敗: ${error.message}`);
  }
}

/**
 * 🧪 測試帶除錯的 TTS 功能
 */
function testTTSWithDebugging() {
  console.log('🧪 === 帶除錯的 TTS 測試 ===');
  
  try {
    const config = getConfig();
    if (!config.geminiApiKey) {
      console.log('❌ 缺少 Gemini API Key');
      return;
    }

    console.log('🔊 開始 TTS 測試...');
    
    const modelName = 'gemini-2.5-flash-preview-tts';
    const apiVersion = 'v1beta';
    const url = `https://generativelanguage.googleapis.com/${apiVersion}/models/${modelName}:generateContent?key=${config.geminiApiKey}`;
    
    const payload = JSON.stringify({
      contents: [{
        parts: [{ text: '你好' }]
      }],
      generationConfig: {
        responseModalities: ["AUDIO"],
        speechConfig: {
          voiceConfig: {
            prebuiltVoiceConfig: {
              voiceName: 'Kore'
            }
          }
        }
      }
    });
    
    console.log('📡 發送 API 請求...');
    
    const response = UrlFetchApp.fetch(url, {
      method: 'POST',
      contentType: 'application/json',
      payload: payload,
      muteHttpExceptions: true
    });
    
    const responseCode = response.getResponseCode();
    const responseText = response.getContentText();
    
    console.log(`📊 回應代碼: ${responseCode}`);
    
    if (responseCode !== 200) {
      console.log('❌ API 錯誤:');
      console.log(`   代碼: ${responseCode}`);
      console.log(`   訊息: ${responseText.substring(0, 200)}...`);
      return;
    }
    
    const result = JSON.parse(responseText);
    console.log('✅ API 調用成功');
    
    // 檢查回應結構
    if (!result.candidates || !result.candidates[0]) {
      console.log('❌ 回應格式錯誤：缺少 candidates');
      return;
    }
    
    const candidate = result.candidates[0];
    console.log(`📋 候選回應數量: ${result.candidates.length}`);
    console.log(`📋 內容部分數量: ${candidate.content.parts.length}`);
    
    // 尋找音頻數據
    const audioData = candidate.content.parts.find(part => part.inlineData);
    
    if (!audioData) {
      console.log('❌ 未找到音頻數據');
      console.log('📋 可用的部分類型:');
      candidate.content.parts.forEach((part, index) => {
        console.log(`   ${index}: ${Object.keys(part).join(', ')}`);
      });
      return;
    }
    
    console.log('✅ 找到音頻數據');
    console.log(`   MIME 類型: ${audioData.inlineData.mimeType}`);
    console.log(`   數據長度: ${audioData.inlineData.data.length} 字符`);
    
    // 解碼音頻數據
    try {
      const audioBytes = Utilities.base64Decode(audioData.inlineData.data);
      console.log(`   解碼後大小: ${audioBytes.length} bytes`);
      
      // 檢查是否為有效的 WAV 檔案（前 4 個字節應該是 "RIFF"）
      if (audioBytes.length >= 4) {
        const header = String.fromCharCode(audioBytes[0], audioBytes[1], audioBytes[2], audioBytes[3]);
        console.log(`   檔案標頭: "${header}"`);
        
        if (header === 'RIFF') {
          console.log('✅ 有效的 WAV 檔案標頭');
          // 檢查 WAV 檔案大小
          if (audioBytes.length >= 8) {
            const fileSize = audioBytes[4] + (audioBytes[5] << 8) + (audioBytes[6] << 16) + (audioBytes[7] << 24);
            console.log(`   標頭中的檔案大小: ${fileSize} bytes`);
            console.log(`   實際檔案大小: ${audioBytes.length} bytes`);
            
            if (Math.abs(fileSize - (audioBytes.length - 8)) > 10) {
              console.log('⚠️ 檔案大小不匹配，可能導致播放問題');
            }
          }
        } else {
          console.log('❌ 無效的 WAV 檔案標頭');
          console.log(`   前 16 bytes: ${Array.from(audioBytes.slice(0, 16)).map(b => b.toString(16).padStart(2, '0')).join(' ')}`);
        }
      }
      
      // 測試保存修復的檔案
      console.log('🔧 測試保存修復的檔案...');
      const fixedBlob = createValidWAVBlob(audioBytes, audioData.inlineData.mimeType);
      
      if (config.folderId) {
        const folder = DriveApp.getFolderById(config.folderId);
        const file = folder.createFile(fixedBlob);
        file.setName(`Fixed_TTS_${new Date().getTime()}.wav`);
        console.log(`✅ 修復的檔案已保存: ${file.getName()}`);
        console.log(`   下載連結: ${file.getUrl()}`);
      }
      
    } catch (decodeError) {
      console.log(`❌ 音頻解碼失敗: ${decodeError.message}`);
    }

  } catch (error) {
    console.error('❌ TTS 除錯測試失敗:', error);
  }
}

/**
 * 🔧 創建有效的 WAV Blob
 */
function createValidWAVBlob(audioBytes, mimeType) {
  try {
    // 如果已經是有效的 WAV 檔案，直接返回
    if (audioBytes.length >= 4) {
      const header = String.fromCharCode(audioBytes[0], audioBytes[1], audioBytes[2], audioBytes[3]);
      if (header === 'RIFF') {
        return Utilities.newBlob(audioBytes, 'audio/wav', `fixed_audio_${new Date().getTime()}.wav`);
      }
    }
    
    // 如果不是 WAV 檔案，嘗試添加 WAV 標頭
    console.log('   🔧 添加 WAV 標頭...');
    
    // 創建基本的 WAV 標頭（假設 16 位 PCM，44.1kHz，單聲道）
    const sampleRate = 22050; // Gemini TTS 通常使用的採樣率
    const bitsPerSample = 16;
    const numChannels = 1;
    const byteRate = sampleRate * numChannels * bitsPerSample / 8;
    const blockAlign = numChannels * bitsPerSample / 8;
    const dataSize = audioBytes.length;
    const fileSize = 36 + dataSize;
    
    const wavHeader = new ArrayBuffer(44);
    const view = new DataView(wavHeader);
    
    // RIFF 標頭
    view.setUint8(0, 0x52); // R
    view.setUint8(1, 0x49); // I
    view.setUint8(2, 0x46); // F
    view.setUint8(3, 0x46); // F
    view.setUint32(4, fileSize, true); // 檔案大小
    view.setUint8(8, 0x57); // W
    view.setUint8(9, 0x41); // A
    view.setUint8(10, 0x56); // V
    view.setUint8(11, 0x45); // E
    
    // fmt 子區塊
    view.setUint8(12, 0x66); // f
    view.setUint8(13, 0x6D); // m
    view.setUint8(14, 0x74); // t
    view.setUint8(15, 0x20); // space
    view.setUint32(16, 16, true); // fmt 區塊大小
    view.setUint16(20, 1, true); // 音頻格式 (1 = PCM)
    view.setUint16(22, numChannels, true); // 聲道數
    view.setUint32(24, sampleRate, true); // 採樣率
    view.setUint32(28, byteRate, true); // 位元組率
    view.setUint16(32, blockAlign, true); // 區塊對齊
    view.setUint16(34, bitsPerSample, true); // 每樣本位數
    
    // data 子區塊
    view.setUint8(36, 0x64); // d
    view.setUint8(37, 0x61); // a
    view.setUint8(38, 0x74); // t
    view.setUint8(39, 0x61); // a
    view.setUint32(40, dataSize, true); // 數據大小
    
    // 合併標頭和音頻數據
    const headerBytes = new Uint8Array(wavHeader);
    const combinedBytes = new Uint8Array(headerBytes.length + audioBytes.length);
    combinedBytes.set(headerBytes, 0);
    combinedBytes.set(audioBytes, headerBytes.length);
    
    console.log(`   ✅ WAV 標頭已添加，總大小: ${combinedBytes.length} bytes`);
    
    return Utilities.newBlob(combinedBytes, 'audio/wav', `fixed_audio_${new Date().getTime()}.wav`);
    
  } catch (error) {
    console.log(`   ❌ WAV 標頭修復失敗: ${error.message}`);
    // 返回原始數據
    return Utilities.newBlob(audioBytes, mimeType || 'audio/wav', `raw_audio_${new Date().getTime()}.wav`);
  }
}

/**
 * 🔧 測試修復的 WAV 檔案生成
 */
function testFixedWAVGeneration() {
  console.log('   🧪 測試修復的 WAV 檔案生成...');
  
  try {
    // 模擬一些音頻數據（正弦波）
    const sampleRate = 22050;
    const duration = 1; // 1 秒
    const frequency = 440; // A4 音符
    const numSamples = sampleRate * duration;
    
    console.log(`   🎵 生成測試音頻：${frequency}Hz, ${duration}秒, ${sampleRate}Hz 採樣率`);
    
    const audioData = new Int16Array(numSamples);
    for (let i = 0; i < numSamples; i++) {
      audioData[i] = Math.sin(2 * Math.PI * frequency * i / sampleRate) * 16383;
    }
    
    const audioBytes = new Uint8Array(audioData.buffer);
    const testBlob = createValidWAVBlob(audioBytes, 'audio/wav');
    
    console.log(`   ✅ 測試 WAV 檔案已生成，大小: ${testBlob.getBytes().length} bytes`);
    
    // 保存測試檔案
    const config = getConfig();
    if (config.folderId) {
      const folder = DriveApp.getFolderById(config.folderId);
      const file = folder.createFile(testBlob);
      file.setName(`Test_WAV_${new Date().getTime()}.wav`);
      console.log(`   📁 測試檔案已保存: ${file.getName()}`);
      console.log(`   🔗 下載測試: ${file.getUrl()}`);
    }
    
  } catch (error) {
    console.log(`   ❌ 測試 WAV 生成失敗: ${error.message}`);
  }
}

/**
 * 🌩️ 診斷 Cloudinary 問題
 */
function diagnoseCloudinaryIssue() {
  try {
    const config = getConfig();
    
    console.log('   📋 檢查 Cloudinary 配置...');
    console.log(`   Cloud Name: "${config.cloudinaryCloudName}"`);
    console.log(`   API Key: "${config.cloudinaryApiKey}"`);
    console.log(`   API Secret: ${config.cloudinaryApiSecret ? '***已設定***' : '未設定'}`);
    
    const hasConfig = config.cloudinaryCloudName && config.cloudinaryApiKey && config.cloudinaryApiSecret;
    
    if (!hasConfig) {
      console.log('   ❌ Cloudinary 配置不完整');
      console.log('   💡 請在 APIKEY 工作表檢查 B8、B9、B10 儲存格');
      return;
    }
    
    console.log('   ✅ Cloudinary 配置完整');
    
    // 測試簽名生成
    console.log('   🔐 測試簽名生成...');
    const testParams = {
      public_id: 'test_audio_123',
      resource_type: 'video',
      format: 'm4a',
      timestamp: Math.floor(Date.now() / 1000)
    };
    
    const signature = generateCloudinarySignature(testParams, config.cloudinaryApiSecret);
    console.log(`   簽名長度: ${signature.length}`);
    console.log(`   簽名格式: ${/^[a-f0-9]{40}$/.test(signature) ? '✅ 正確' : '❌ 錯誤'}`);
    
    if (signature.length === 40 && /^[a-f0-9]{40}$/.test(signature)) {
      console.log('   ✅ Cloudinary 簽名生成正常');
    } else {
      console.log('   ❌ Cloudinary 簽名生成異常');
    }
    
  } catch (error) {
    console.log(`   ❌ Cloudinary 診斷失敗: ${error.message}`);
  }
}

/**
 * 🚀 完整的 WAV 修復測試
 */
function fullWAVFixTest() {
  console.log('🚀 === 完整的 WAV 修復測試 ===');
  
  try {
    // 1. 測試 TTS API
    console.log('1️⃣ 測試 TTS API...');
    testTTSWithDebugging();
    
    // 2. 測試 WAV 修復
    console.log('\n2️⃣ 測試 WAV 修復...');
    testFixedWAVGeneration();
    
    // 3. 測試 Cloudinary
    console.log('\n3️⃣ 測試 Cloudinary...');
    diagnoseCloudinaryIssue();
    
    console.log('\n🎉 完整測試完成！');
    console.log('💡 請檢查 Google Drive 中的測試檔案是否可以播放');
    
    return '測試完成';
    
  } catch (error) {
    console.error('❌ 完整測試失敗:', error);
    return `測試失敗: ${error.message}`;
  }
}
