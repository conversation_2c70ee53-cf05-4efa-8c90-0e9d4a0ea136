// == Cloudinary API Key 格式修復 ==
// 🔧 修復 Google Sheets 科學記數法轉換問題

/**
 * 🔧 修復 Cloudinary API Key 科學記數法問題
 * 解決 Google Sheets 將長數字轉換為科學記數法的問題
 */
function fixCloudinaryAPIKeyFormat() {
  console.log('🔧 === 修復 Cloudinary API Key 格式問題 ===');
  console.log('🎯 問題：Google Sheets 將 API Key 轉換為科學記數法');
  console.log('');

  try {
    // 1. 檢查當前配置
    console.log('📋 1. 檢查當前配置...');
    const config = getConfig();
    
    console.log('🔍 當前配置詳情：');
    console.log(`   Cloud Name: "${config.cloudinaryCloudName}" (類型: ${typeof config.cloudinaryCloudName})`);
    console.log(`   API Key: "${config.cloudinaryApiKey}" (類型: ${typeof config.cloudinaryApiKey})`);
    console.log(`   API Secret: ${config.cloudinaryApiSecret ? '***已設定***' : '未設定'} (類型: ${typeof config.cloudinaryApiSecret})`);

    // 2. 檢測科學記數法問題
    console.log('\n🧪 2. 檢測科學記數法問題...');
    
    let apiKeyIssue = false;
    let apiKeyValue = config.cloudinaryApiKey;
    
    if (typeof apiKeyValue === 'number') {
      console.log('⚠️ API Key 是數字類型，會被轉換為科學記數法');
      apiKeyIssue = true;
      apiKeyValue = apiKeyValue.toString();
    }
    
    if (apiKeyValue && apiKeyValue.includes('E')) {
      console.log('⚠️ API Key 包含科學記數法格式');
      apiKeyIssue = true;
    }
    
    if (apiKeyIssue) {
      console.log('❌ 檢測到 API Key 格式問題');
      console.log('💡 需要在 Google Sheets 中設定為純文字格式');
    } else {
      console.log('✅ API Key 格式正常');
    }

    // 3. 提供修復指導
    console.log('\n📝 3. 修復步驟指導...');
    console.log('');
    console.log('🔧 在 Google Sheets 中修復：');
    console.log('   1. 打開 APIKEY 工作表');
    console.log('   2. 選中 B9 儲存格（API Key）');
    console.log('   3. 右鍵 → 格式化 → 數字 → 純文字');
    console.log('   4. 重新輸入 API Key：573243213616535');
    console.log('   5. 同樣檢查 B10 儲存格（API Secret）');
    console.log('');

    // 4. 測試修復後的配置
    console.log('🧪 4. 測試當前配置（如果格式正確）...');
    
    if (!apiKeyIssue && config.cloudinaryCloudName && config.cloudinaryApiKey && config.cloudinaryApiSecret) {
      console.log('✅ 配置格式正確，測試 Cloudinary 連接...');
      testCloudinaryConnection();
    } else {
      console.log('⚠️ 請先修復配置格式，然後重新執行測試');
    }

    console.log('\n📊 修復指導完成！');
    return '修復指導完成';

  } catch (error) {
    console.error('❌ 修復過程錯誤:', error);
    return `修復失敗: ${error.message}`;
  }
}

/**
 * 🧪 測試 Cloudinary 連接（格式修復後）
 */
function testCloudinaryConnection() {
  try {
    const config = getConfig();
    
    // 確保 API Key 是字符串格式
    let apiKey = config.cloudinaryApiKey;
    if (typeof apiKey === 'number') {
      apiKey = apiKey.toString();
    }
    
    console.log('   🔧 處理後的 API Key 格式:');
    console.log(`      原始值: ${config.cloudinaryApiKey}`);
    console.log(`      處理後: ${apiKey}`);
    console.log(`      長度: ${apiKey.length}`);
    console.log(`      是否包含 E: ${apiKey.includes('E')}`);
    
    if (apiKey.includes('E')) {
      console.log('   ❌ API Key 仍然包含科學記數法，請手動修復 Google Sheets');
      return;
    }
    
    // 創建簡單的測試檔案
    const testBlob = Utilities.newBlob('test', 'text/plain', 'test.txt');
    
    // 測試上傳參數
    const timestamp = Math.floor(Date.now() / 1000);
    const publicId = `test/connection_test_${timestamp}`;
    
    const uploadParams = {
      public_id: publicId,
      timestamp: timestamp
    };
    
    // 生成簽名（使用修復後的配置）
    const signature = generateCloudinarySignature(uploadParams, config.cloudinaryApiSecret);
    
    console.log('   📋 測試參數:');
    console.log(`      Cloud Name: ${config.cloudinaryCloudName}`);
    console.log(`      API Key: ${apiKey}`);
    console.log(`      Timestamp: ${timestamp}`);
    console.log(`      簽名: ${signature.substring(0, 10)}...`);
    
    // 測試上傳 URL
    const uploadUrl = `https://api.cloudinary.com/v1_1/${config.cloudinaryCloudName}/image/upload`;
    console.log(`      上傳 URL: ${uploadUrl}`);
    
    // 準備測試表單
    const formData = {
      file: testBlob,
      public_id: publicId,
      api_key: apiKey,  // 使用修復後的 API Key
      timestamp: timestamp,
      signature: signature
    };
    
    console.log('   📡 發送測試請求...');
    
    const response = UrlFetchApp.fetch(uploadUrl, {
      method: 'POST',
      payload: formData,
      muteHttpExceptions: true
    });
    
    const responseCode = response.getResponseCode();
    const responseText = response.getContentText();
    
    console.log(`   📊 測試結果: ${responseCode}`);
    
    if (responseCode === 200) {
      const result = JSON.parse(responseText);
      console.log('   🎉 Cloudinary 連接測試成功！');
      console.log(`      上傳 URL: ${result.secure_url}`);
      console.log('   ✅ API Key 格式修復成功');
    } else {
      console.log('   ❌ Cloudinary 連接測試失敗');
      console.log(`      錯誤代碼: ${responseCode}`);
      console.log(`      錯誤詳情: ${responseText}`);
      
      if (responseCode === 401) {
        console.log('   💡 401 錯誤通常表示 API Key 或簽名問題');
      }
    }

  } catch (error) {
    console.log(`   ❌ 連接測試失敗: ${error.message}`);
  }
}

/**
 * 🚀 完整的 API Key 修復驗證
 */
function verifyAPIKeyFix() {
  console.log('🚀 === 完整的 API Key 修復驗證 ===');
  
  try {
    // 1. 檢查格式
    console.log('1️⃣ 檢查 API Key 格式...');
    fixCloudinaryAPIKeyFormat();
    
    // 2. 如果格式正確，測試語音功能
    const config = getConfig();
    let apiKey = config.cloudinaryApiKey;
    if (typeof apiKey === 'number') {
      apiKey = apiKey.toString();
    }
    
    if (!apiKey.includes('E') && config.cloudinaryCloudName && config.cloudinaryApiSecret) {
      console.log('\n2️⃣ API Key 格式正確，測試語音功能...');
      const result = textToSpeechWithGemini('修復驗證測試');
      
      if (result.success) {
        console.log('✅ TTS 生成成功');
        if (result.isPlayable) {
          console.log('🎉 完全修復成功！語音可在 LINE 中播放');
          console.log(`   Cloudinary URL: ${result.cloudinaryUrl}`);
        } else {
          console.log('⚠️ TTS 正常但 Cloudinary 仍有問題');
          console.log(`   錯誤: ${result.cloudinaryError}`);
        }
      } else {
        console.log('❌ TTS 測試失敗');
        console.log(`   錯誤: ${result.error}`);
      }
    } else {
      console.log('\n⚠️ 請先修復 Google Sheets 中的 API Key 格式');
    }
    
    console.log('\n📊 驗證完成！');
    return '驗證完成';
    
  } catch (error) {
    console.error('❌ 驗證失敗:', error);
    return `驗證失敗: ${error.message}`;
  }
}

/**
 * 📋 顯示修復檢查清單
 */
function showAPIKeyFixChecklist() {
  console.log('📋 === API Key 修復檢查清單 ===');
  console.log('');
  
  console.log('🔧 Google Sheets 修復步驟：');
  console.log('   □ 1. 打開 APIKEY 工作表');
  console.log('   □ 2. 選中 B9 儲存格（API Key）');
  console.log('   □ 3. 右鍵 → 格式化 → 數字 → 純文字');
  console.log('   □ 4. 清除內容');
  console.log('   □ 5. 重新輸入：573243213616535');
  console.log('   □ 6. 確認顯示為：573243213616535（不是科學記數法）');
  console.log('   □ 7. 同樣檢查 B10（API Secret）格式');
  console.log('');
  
  console.log('🧪 修復後測試步驟：');
  console.log('   □ 1. 執行 verifyAPIKeyFix()');
  console.log('   □ 2. 確認無 401 錯誤');
  console.log('   □ 3. 測試 LINE 語音功能');
  console.log('   □ 4. 確認出現音頻播放按鈕');
  console.log('');
  
  console.log('✅ 修復成功標準：');
  console.log('   • API Key 不包含 E（科學記數法）');
  console.log('   • Cloudinary 測試返回 200');
  console.log('   • TTS 結果 isPlayable = true');
  console.log('   • LINE 顯示音頻播放按鈕');
}
