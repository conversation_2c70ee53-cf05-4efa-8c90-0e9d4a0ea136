// 🧪 意圖檢測診斷測試
// 專門用來診斷為什麼語音和圖片意圖沒有被正確識別

/**
 * 🔍 診斷意圖檢測問題
 */
function debugIntentDetection() {
  console.log('🔍 === 意圖檢測診斷測試 ===');
  
  const testCases = [
    {
      input: '你說 你好嗎',
      expected: 'text_to_speech'
    },
    {
      input: '你念給我聽：今天天氣很好',
      expected: 'text_to_speech'
    },
    {
      input: '能給我炒高麗菜的圖片嗎',
      expected: 'image_generation'
    },
    {
      input: '畫一張貓咪的圖',
      expected: 'image_generation'
    },
    {
      input: '念出來：測試語音',
      expected: 'text_to_speech'
    },
    {
      input: '幫我畫一個太陽',
      expected: 'image_generation'
    }
  ];
  
  testCases.forEach((testCase, index) => {
    console.log(`\n🧪 測試 ${index + 1}: "${testCase.input}"`);
    console.log(`預期意圖: ${testCase.expected}`);
    
    try {
      // 1. 測試 AI 意圖分析
      console.log('📋 步驟 1: AI 意圖分析');
      const aiIntent = analyzeUserIntentWithAI(testCase.input, 'group', 'debug-user');
      console.log(`AI 分析結果: ${aiIntent.primary_intent} (信心度: ${aiIntent.confidence}%)`);
      console.log(`AI 摘要: ${aiIntent.natural_language_summary}`);
      
      // 2. 測試備用檢測
      console.log('📋 步驟 2: 備用意圖檢測');
      const fallbackIntent = generateFallbackIntent(testCase.input, 'group');
      console.log(`備用檢測結果: ${fallbackIntent.primary_intent} (信心度: ${fallbackIntent.confidence}%)`);
      
      // 3. 檢查結果
      const finalIntent = aiIntent.primary_intent !== 'casual_chat' ? aiIntent.primary_intent : fallbackIntent.primary_intent;
      const isCorrect = finalIntent === testCase.expected;
      
      console.log(`🎯 最終意圖: ${finalIntent}`);
      console.log(`✅ 結果: ${isCorrect ? '正確' : '錯誤'} ${isCorrect ? '✅' : '❌'}`);
      
      if (!isCorrect) {
        console.log(`❌ 預期 ${testCase.expected}，但得到 ${finalIntent}`);
      }
      
    } catch (error) {
      console.error(`❌ 測試 ${index + 1} 失敗:`, error);
    }
  });
  
  console.log('\n🔍 === 診斷測試完成 ===');
}

/**
 * 🔍 測試提示詞系統
 */
function debugPromptSystem() {
  console.log('🔍 === 提示詞系統診斷 ===');
  
  try {
    // 測試意圖分析提示詞
    console.log('📋 測試意圖分析提示詞...');
    const intentResponse = callAIWithPrompt('INTENT_ANALYSIS', {
      userMessage: '你說 你好嗎',
      sourceType: '群組聊天'
    });
    
    console.log('AI 原始回應:', intentResponse);
    
    // 嘗試解析 JSON
    const jsonMatch = intentResponse.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      try {
        const parsed = JSON.parse(jsonMatch[0]);
        console.log('解析成功:', parsed);
      } catch (parseError) {
        console.error('JSON 解析失敗:', parseError);
        console.log('匹配到的 JSON 字符串:', jsonMatch[0]);
      }
    } else {
      console.log('❌ 沒有找到 JSON 格式的回應');
    }
    
  } catch (error) {
    console.error('提示詞系統測試失敗:', error);
  }
}

/**
 * 🔍 測試正則表達式匹配
 */
function debugRegexMatching() {
  console.log('🔍 === 正則表達式匹配診斷 ===');
  
  const testInputs = [
    '你說 你好嗎',
    '你念給我聽：今天天氣很好',
    '能給我炒高麗菜的圖片嗎',
    '畫一張貓咪的圖',
    '念出來：測試語音',
    '幫我畫一個太陽'
  ];
  
  const ttsRegex = /你說|你念|念出來|讀出來|語音|播放|tts|說出來/i;
  const imageRegex = /畫|生成圖|創建圖|圖像|畫圖|圖片|generate image|draw|create image|給我.*圖/i;
  
  testInputs.forEach((input, index) => {
    console.log(`\n測試 ${index + 1}: "${input}"`);
    
    const ttsMatch = ttsRegex.test(input);
    const imageMatch = imageRegex.test(input);
    
    console.log(`TTS 正則匹配: ${ttsMatch ? '✅' : '❌'}`);
    console.log(`圖片正則匹配: ${imageMatch ? '✅' : '❌'}`);
    
    if (ttsMatch) {
      console.log('🔊 應該識別為 text_to_speech');
    } else if (imageMatch) {
      console.log('🎨 應該識別為 image_generation');
    } else {
      console.log('❓ 沒有匹配到特定意圖');
    }
  });
}

/**
 * 🔍 完整診斷流程
 */
function runFullDiagnosis() {
  console.log('🔍 === 完整意圖檢測診斷 ===');
  
  debugRegexMatching();
  console.log('\n' + '='.repeat(50) + '\n');
  
  debugPromptSystem();
  console.log('\n' + '='.repeat(50) + '\n');
  
  debugIntentDetection();
  
  console.log('\n🎉 完整診斷完成！');
}
