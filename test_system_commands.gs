// == 系統指令測試模組 ==
// 🔧 測試新增的系統指令和模型範例功能

/**
 * 🧪 測試系統指令識別
 */
function testSystemCommandDetection() {
  console.log('=== 測試系統指令識別 ===');
  
  const testCommands = [
    '!測試',
    '測試',
    'test',
    '!help',
    'help',
    '幫助',
    '功能',
    '範例',
    '!範例',
    'examples',
    '狀態',
    'status',
    '!status'
  ];
  
  testCommands.forEach((command, index) => {
    try {
      console.log(`\n🔧 測試 ${index + 1}: "${command}"`);
      
      // 使用 AI-First 意圖分析
      const intent = analyzeUserIntentWithAI(command, 'group', 'test-user');
      
      console.log(`意圖: ${intent.primary_intent}`);
      console.log(`信心度: ${intent.confidence}%`);
      console.log(`摘要: ${intent.natural_language_summary}`);
      
      // 測試路由處理
      const response = routeByAIIntent(intent, command, 'test-user', 'group');
      console.log(`回應長度: ${response.length} 字符`);
      console.log(`回應預覽: ${response.substring(0, 100)}...`);
      
    } catch (error) {
      console.error(`測試 ${index + 1} 失敗:`, error);
    }
  });
  
  console.log('\n✅ 系統指令識別測試完成');
}

/**
 * 🧪 測試模型範例功能
 */
function testModelExamples() {
  console.log('=== 測試模型範例功能 ===');
  
  const testQueries = [
    '範例',
    '使用方法',
    '怎麼用語音功能',
    '圖片生成範例',
    'examples',
    '功能範例'
  ];
  
  testQueries.forEach((query, index) => {
    try {
      console.log(`\n📚 測試 ${index + 1}: "${query}"`);
      
      const intent = analyzeUserIntentWithAI(query, 'personal', 'test-user');
      console.log(`意圖: ${intent.primary_intent}`);
      
      if (intent.primary_intent === 'model_examples') {
        const response = generateModelExamplesResponse(intent, 'personal');
        console.log(`✅ 成功生成範例回應`);
        console.log(`回應長度: ${response.length} 字符`);
        console.log(`包含語音範例: ${response.includes('語音') ? '✅' : '❌'}`);
        console.log(`包含圖片範例: ${response.includes('圖片') ? '✅' : '❌'}`);
      }
      
    } catch (error) {
      console.error(`測試 ${index + 1} 失敗:`, error);
    }
  });
  
  console.log('\n✅ 模型範例功能測試完成');
}

/**
 * 🧪 測試完整的 AI-First 系統指令流程
 */
function testCompleteSystemCommandFlow() {
  console.log('=== 測試完整系統指令流程 ===');
  
  const testScenarios = [
    {
      input: '!測試',
      expectedIntent: 'system_command',
      description: '群組測試指令'
    },
    {
      input: 'help',
      expectedIntent: 'system_command',
      description: '個人幫助指令'
    },
    {
      input: '範例',
      expectedIntent: 'model_examples',
      description: '範例請求'
    },
    {
      input: '你說：今天天氣很好',
      expectedIntent: 'text_to_speech',
      description: 'TTS 功能測試'
    },
    {
      input: '畫一隻貓',
      expectedIntent: 'image_generation',
      description: '圖片生成測試'
    }
  ];
  
  testScenarios.forEach((scenario, index) => {
    try {
      console.log(`\n🎯 場景 ${index + 1}: ${scenario.description}`);
      console.log(`輸入: "${scenario.input}"`);
      
      // 完整流程測試
      const response = handleTextMessageAIFirst(scenario.input, null, 'test-user', 'group');
      
      console.log(`✅ 處理完成`);
      console.log(`回應類型: ${typeof response}`);
      
      if (typeof response === 'object' && response.type) {
        console.log(`特殊回應類型: ${response.type}`);
      } else {
        console.log(`文字回應長度: ${response.length} 字符`);
        console.log(`回應預覽: ${response.substring(0, 80)}...`);
      }
      
    } catch (error) {
      console.error(`場景 ${index + 1} 失敗:`, error);
    }
  });
  
  console.log('\n✅ 完整系統指令流程測試完成');
}

/**
 * 🧪 測試系統狀態回應
 */
function testSystemStatusResponse() {
  console.log('=== 測試系統狀態回應 ===');
  
  try {
    const statusResponse = generateSystemStatusResponse();
    
    console.log('✅ 系統狀態回應生成成功');
    console.log(`回應長度: ${statusResponse.length} 字符`);
    console.log(`包含版本資訊: ${statusResponse.includes('版本') ? '✅' : '❌'}`);
    console.log(`包含 AI 功能: ${statusResponse.includes('Gemini') ? '✅' : '❌'}`);
    console.log(`包含媒體功能: ${statusResponse.includes('TTS') ? '✅' : '❌'}`);
    console.log(`包含時間戳: ${statusResponse.includes('檢查時間') ? '✅' : '❌'}`);
    
    console.log('\n📋 狀態回應預覽:');
    console.log(statusResponse.substring(0, 200) + '...');
    
  } catch (error) {
    console.error('系統狀態測試失敗:', error);
  }
  
  console.log('\n✅ 系統狀態回應測試完成');
}

/**
 * 🧪 執行所有測試
 */
function runAllSystemCommandTests() {
  console.log('🚀 === 開始執行所有系統指令測試 ===');
  
  try {
    testSystemCommandDetection();
    testModelExamples();
    testSystemStatusResponse();
    testCompleteSystemCommandFlow();
    
    console.log('\n🎉 === 所有測試執行完成 ===');
    console.log('✅ 系統指令功能已就緒');
    console.log('✅ 模型範例功能已就緒');
    console.log('✅ AI-First 架構保持完整');
    
    return '✅ 所有系統指令測試通過';
    
  } catch (error) {
    console.error('❌ 測試執行失敗:', error);
    return `❌ 測試失敗: ${error.message}`;
  }
}

/**
 * 🔍 快速驗證功能
 */
function quickSystemCommandCheck() {
  console.log('🔍 === 快速系統指令驗證 ===');
  
  const quickTests = [
    { input: '!測試', expected: 'system_command' },
    { input: 'help', expected: 'system_command' },
    { input: '範例', expected: 'model_examples' }
  ];
  
  let passCount = 0;
  
  quickTests.forEach((test, index) => {
    try {
      const intent = analyzeUserIntentWithAI(test.input, 'group', 'test');
      const passed = intent.primary_intent === test.expected;
      
      console.log(`測試 ${index + 1}: "${test.input}" -> ${intent.primary_intent} ${passed ? '✅' : '❌'}`);
      
      if (passed) passCount++;
      
    } catch (error) {
      console.log(`測試 ${index + 1}: "${test.input}" -> 錯誤 ❌`);
    }
  });
  
  console.log(`\n結果: ${passCount}/${quickTests.length} 通過`);
  return passCount === quickTests.length;
}
