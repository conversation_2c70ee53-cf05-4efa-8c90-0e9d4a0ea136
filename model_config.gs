// == Gemini 模型配置管理模組 v3.0 ==
// 🚀 按功能分類的智能模型管理系統
// 統一管理所有 Gemini 模型，支援按輸出類型和功能需求分類

/**
 * 🎯 按功能分類的模型配置表
 * 每個功能類別都有獨立的模型清單和驗證規則
 */
const FUNCTIONAL_MODEL_CATEGORIES = {
  // 🤖 一般功能模型：文字對話、摘要、搜尋判斷等
  'general': {
    sheetRow: 14,
    categoryName: '一般功能模型',
    description: '🤖 文字對話、摘要、搜尋判斷等',
    models: [
      'gemini-2.5-flash',        // 平衡效能，推薦一般使用
      'gemini-2.5-pro',          // 最強分析能力
      'gemini-1.5-flash',        // 穩定快速
      'gemini-1.5-pro',          // 專業分析
      'gemini-2.0-flash',        // 新一代功能
      'gemini-2.5-flash-lite-preview-06-17' // 成本優化
    ],
    defaultModel: 'gemini-2.5-flash',
    helpText: '🤖 一般功能模型選擇：\n\n' +
      '• gemini-2.5-flash (推薦：平衡效能)\n' +
      '• gemini-2.5-pro (最強分析能力)\n' +
      '• gemini-1.5-flash (穩定快速)\n' +
      '• gemini-1.5-pro (專業分析)\n' +
      '• gemini-2.0-flash (新一代功能)\n' +
      '• gemini-2.5-flash-lite-* (成本優化)\n\n' +
      '用於：文字對話、內容摘要、搜尋判斷、一般分析'
  },

  // 🖼️ 圖片分析模型：視覺內容分析專用
  'vision': {
    sheetRow: 15,
    categoryName: '圖片分析模型',
    description: '🖼️ 專用於圖片、視覺內容分析',
    models: [
      'gemini-2.5-pro',          // 最佳視覺分析
      'gemini-2.5-flash',        // 快速視覺理解
      'gemini-1.5-pro',          // 穩定視覺分析
      'gemini-1.5-flash',        // 基礎視覺處理
      'gemini-2.0-flash'         // 新一代視覺功能
    ],
    defaultModel: 'gemini-2.5-pro',
    helpText: '🖼️ 圖片分析模型選擇：\n\n' +
      '• gemini-2.5-pro (推薦：最佳視覺分析)\n' +
      '• gemini-2.5-flash (快速視覺理解)\n' +
      '• gemini-1.5-pro (穩定視覺分析)\n' +
      '• gemini-1.5-flash (基礎視覺處理)\n' +
      '• gemini-2.0-flash (新一代視覺功能)\n\n' +
      '用於：圖片內容分析、視覺問答、OCR文字識別、圖像理解'
  },

  // 🔊 語音合成模型：TTS 文字轉語音
  'tts': {
    sheetRow: 16,
    categoryName: '語音合成模型',
    description: '🔊 TTS 文字轉語音（鸚鵡模式）',
    models: [
      'gemini-2.5-flash-preview-tts',    // 快速 TTS
      'gemini-2.5-pro-preview-tts'       // 高品質 TTS
    ],
    defaultModel: 'gemini-2.5-flash-preview-tts',
    helpText: '🔊 語音合成模型選擇：\n\n' +
      '• gemini-2.5-flash-preview-tts (推薦：快速合成)\n' +
      '• gemini-2.5-pro-preview-tts (高品質合成)\n\n' +
      '用於：文字轉語音、語音通知、多語言朗讀\n' +
      '特點：低延遲、可控制語調、支援多說話者'
  },

  // 🎙️ 對話音頻模型：智能對話音頻
  'audio_dialog': {
    sheetRow: 17,
    categoryName: '對話音頻模型',
    description: '🎙️ 智能對話音頻（大腦模式）',
    models: [
      'gemini-2.5-flash-preview-native-audio-dialog',           // 標準對話音頻
      'gemini-2.5-flash-exp-native-audio-thinking-dialog',      // 思考對話音頻
      'gemini-live-2.5-flash-preview',                          // Live API 對話
      'gemini-2.0-flash-live-001'                               // 2.0 Live 對話
    ],
    defaultModel: 'gemini-2.5-flash-preview-native-audio-dialog',
    helpText: '🎙️ 對話音頻模型選擇：\n\n' +
      '• *-native-audio-dialog (推薦：標準對話)\n' +
      '• *-thinking-dialog (思考對話模式)\n' +
      '• gemini-live-* (即時互動對話)\n\n' +
      '用於：語音助手、即時對話、互動式問答\n' +
      '特點：自然對話流程、支援打斷、情感表達'
  },

  // 📊 嵌入向量模型：語義搜索、文檔匹配
  'embedding': {
    sheetRow: 18,
    categoryName: '嵌入向量模型',
    description: '📊 語義搜索、文檔匹配、內容關聯',
    models: [
      'gemini-embedding-exp',     // 實驗性嵌入模型
      'text-embedding-004',       // 標準嵌入模型
      'embedding-001'             // 基礎嵌入模型
    ],
    defaultModel: 'gemini-embedding-exp',
    helpText: '📊 嵌入向量模型選擇：\n\n' +
      '• gemini-embedding-exp (推薦：最新實驗模型)\n' +
      '• text-embedding-004 (穩定標準模型)\n' +
      '• embedding-001 (基礎模型)\n\n' +
      '用於：語義搜索、文檔相似度、內容推薦\n' +
      '特點：高維向量空間、語義理解、多語言支援'
  },

  // ⚡ 高效率模型：快速處理、批量任務
  'cost_effective': {
    sheetRow: 19,
    categoryName: '高效率模型',
    description: '⚡ 快速處理、批量任務、成本優化',
    models: [
      'gemini-2.5-flash-lite-preview-06-17',  // 最新輕量模型
      'gemini-2.0-flash-lite',                // 2.0 輕量模型
      'gemini-1.5-flash-8b',                  // 8B 參數模型
      'gemini-1.5-flash'                      // 快速模型
    ],
    defaultModel: 'gemini-2.5-flash-lite-preview-06-17',
    helpText: '⚡ 高效率模型選擇：\n\n' +
      '• gemini-2.5-flash-lite-* (推薦：最新輕量)\n' +
      '• gemini-2.0-flash-lite (2.0 輕量版)\n' +
      '• gemini-1.5-flash-8b (輕量級處理)\n' +
      '• gemini-1.5-flash (穩定快速)\n\n' +
      '用於：批量處理、成本敏感任務、高吞吐量場景\n' +
      '特點：低延遲、高性價比、適合大量調用'
  },

  // 🎨 圖片生成模型：AI 繪畫、圖像創作
  'image_generation': {
    sheetRow: 20,
    categoryName: '圖片生成模型',
    description: '🎨 AI 繪畫、圖像創作、視覺內容生成',
    models: [
      'gemini-2.0-flash-preview-image-generation',  // Gemini 圖片生成
      'imagen-4.0-generate-preview-06-06',          // Imagen 4.0 標準
      'imagen-4.0-ultra-generate-preview-06-06',    // Imagen 4.0 Ultra
      'imagen-3.0-generate-002'                     // Imagen 3.0
    ],
    defaultModel: 'gemini-2.0-flash-preview-image-generation',
    helpText: '🎨 圖片生成模型選擇：\n\n' +
      '• gemini-2.0-flash-*-image-generation (推薦：對話式生成)\n' +
      '• imagen-4.0-*-preview-* (最新 Imagen 4.0)\n' +
      '• imagen-3.0-generate-002 (穩定 Imagen 3.0)\n\n' +
      '用於：AI 繪畫、圖像創作、視覺內容生成\n' +
      '特點：高解析度、豐富光線、改善文字渲染\n' +
      '注意：部分進階編輯功能可能不支援'
  }
};

/**
 * 🔧 傳統模型配置表（向後相容）
 * 保留原有的詳細配置資訊
 */
const GEMINI_MODEL_CONFIG = {
  // === Gemini 2.5 系列 ===
  'gemini-2.5-pro': {
    apiVersion: 'v1',
    category: 'pro',
    capabilities: ['text', 'image', 'video', 'audio', 'pdf', 'thinking'],
    description: '最強大的思考模型，回覆準確率和效能皆極為優異',
    recommendedFor: ['complex_analysis', 'vision_analysis', 'reasoning']
  },
  'gemini-2.5-flash': {
    apiVersion: 'v1beta',
    category: 'flash',
    capabilities: ['text', 'image', 'video', 'audio', 'thinking'],
    description: '價格與效能兼具的最佳型號，提供多元功能',
    recommendedFor: ['general_use', 'chat', 'quick_analysis']
  },
  'gemini-2.5-flash-lite-preview-06-17': {
    apiVersion: 'v1beta',
    category: 'flash-lite',
    capabilities: ['text', 'image', 'video', 'audio'],
    description: 'Flash 模型經過最佳化調整，延遲時間短且成本效益高',
    recommendedFor: ['cost_optimization', 'high_throughput', 'batch_processing']
  },
  
  // === Gemini 2.5 語音功能模型 ===
  'gemini-2.5-flash-preview-tts': {
    apiVersion: 'v1beta',
    category: 'tts',
    capabilities: ['text_to_speech'],
    description: '低延遲、可控的單一和多位說話者文字轉語音音訊產生',
    recommendedFor: ['tts_parrot_mode', 'speech_synthesis']
  },
  'gemini-2.5-pro-preview-tts': {
    apiVersion: 'v1beta',
    category: 'tts',
    capabilities: ['text_to_speech'],
    description: '最強大的文字轉語音模型',
    recommendedFor: ['high_quality_tts', 'professional_speech']
  },
  'gemini-2.5-flash-preview-native-audio-dialog': {
    apiVersion: 'v1beta',
    category: 'native_audio',
    capabilities: ['audio_dialog', 'text', 'audio_input', 'audio_output'],
    description: '無論是否使用思考功能，都能輸出高品質的自然對話音訊',
    recommendedFor: ['conversational_audio', 'interactive_dialog', 'voice_assistant']
  },
  'gemini-2.5-flash-exp-native-audio-thinking-dialog': {
    apiVersion: 'v1beta',
    category: 'native_audio_thinking',
    capabilities: ['audio_dialog', 'text', 'audio_input', 'audio_output', 'thinking'],
    description: '具備思考功能的對話音訊模型',
    recommendedFor: ['complex_audio_dialog', 'reasoning_conversations']
  },
  
  // === Gemini 2.0 系列 ===
  'gemini-2.0-flash': {
    apiVersion: 'v1',
    category: 'flash',
    capabilities: ['text', 'image', 'video', 'audio', 'thinking_experimental'],
    description: '新一代功能、速度和即時串流',
    recommendedFor: ['next_gen_features', 'streaming']
  },
  'gemini-2.0-flash-lite': {
    apiVersion: 'v1beta',
    category: 'flash-lite',
    capabilities: ['text', 'image', 'video', 'audio'],
    description: '成本效益高且延遲時間短',
    recommendedFor: ['cost_effective', 'low_latency']
  },
  'gemini-2.0-flash-preview-image-generation': {
    apiVersion: 'v1beta',
    category: 'image_generation',
    capabilities: ['text', 'image', 'video', 'audio', 'image_generation'],
    description: '對話式圖像生成和編輯',
    recommendedFor: ['image_creation', 'visual_content_generation', 'ai_art']
  },
  'gemini-2.0-flash-live-001': {
    apiVersion: 'v1beta',
    category: 'live',
    capabilities: ['text', 'audio', 'video', 'live_api'],
    description: '低延遲的雙向語音/視訊互動',
    recommendedFor: ['live_interaction', 'real_time_dialog']
  },
  
  // === Gemini 1.5 系列 ===
  'gemini-1.5-flash': {
    apiVersion: 'v1',
    category: 'flash',
    capabilities: ['text', 'image', 'video', 'audio'],
    description: '在各種任務中提供快速且多功能的效能',
    recommendedFor: ['stable_performance', 'general_tasks']
  },
  'gemini-1.5-flash-8b': {
    apiVersion: 'v1',
    category: 'flash-lite',
    capabilities: ['text', 'image', 'video', 'audio'],
    description: '大量且較不智慧的工作',
    recommendedFor: ['lightweight_tasks', 'bulk_processing']
  },
  'gemini-1.5-pro': {
    apiVersion: 'v1',
    category: 'pro',
    capabilities: ['text', 'image', 'video', 'audio'],
    description: '需要更多智慧的複雜推論工作',
    recommendedFor: ['complex_reasoning', 'professional_analysis']
  },
  
  // === 嵌入模型 ===
  'gemini-embedding-exp': {
    apiVersion: 'v1beta',
    category: 'embedding',
    capabilities: ['text_embedding'],
    description: '評估文字字串的相關性',
    recommendedFor: ['semantic_search', 'document_matching', 'content_correlation']
  },
  'text-embedding-004': {
    apiVersion: 'v1',
    category: 'embedding',
    capabilities: ['text_embedding'],
    description: '標準文字嵌入模型，性能優異',
    recommendedFor: ['stable_embedding', 'production_use']
  },
  'embedding-001': {
    apiVersion: 'v1',
    category: 'embedding',
    capabilities: ['text_embedding'],
    description: '基礎嵌入模型',
    recommendedFor: ['basic_embedding', 'legacy_support']
  },
  
  // === Imagen 圖片生成模型 ===
  'imagen-4.0-generate-preview-06-06': {
    apiVersion: 'v1beta',
    category: 'image_generation',
    capabilities: ['image_generation'],
    description: 'Imagen 4.0 標準版：高解析度圖像生成',
    recommendedFor: ['image_creation', 'high_resolution', 'text_rendering']
  },
  'imagen-4.0-ultra-generate-preview-06-06': {
    apiVersion: 'v1beta',
    category: 'image_generation',
    capabilities: ['image_generation'],
    description: 'Imagen 4.0 Ultra：最高品質圖像生成',
    recommendedFor: ['premium_image_creation', 'professional_quality']
  },
  'imagen-3.0-generate-002': {
    apiVersion: 'v1',
    category: 'image_generation',
    capabilities: ['image_generation'],
    description: 'Imagen 3.0：穩定的圖像生成模型',
    recommendedFor: ['stable_image_generation', 'production_use']
  },
  
  // === Live API 模型 ===
  'gemini-live-2.5-flash-preview': {
    apiVersion: 'v1beta',
    category: 'live',
    capabilities: ['text', 'audio', 'video', 'live_api'],
    description: '低延遲的雙向語音/視訊互動',
    recommendedFor: ['live_interaction', 'real_time_conversation']
  }
};

/**
 * 🎯 根據功能類別獲取模型清單
 * @param {string} category 功能類別名稱
 * @returns {Array<string>} 該類別支援的模型清單
 */
function getModelsByCategory(category) {
  const categoryConfig = FUNCTIONAL_MODEL_CATEGORIES[category];
  return categoryConfig ? categoryConfig.models : [];
}

/**
 * 🎯 根據功能類別獲取預設模型
 * @param {string} category 功能類別名稱
 * @returns {string} 該類別的預設模型
 */
function getDefaultModelForCategory(category) {
  const categoryConfig = FUNCTIONAL_MODEL_CATEGORIES[category];
  return categoryConfig ? categoryConfig.defaultModel : 'gemini-2.5-flash';
}

/**
 * 🎯 根據功能類別獲取幫助文字
 * @param {string} category 功能類別名稱
 * @returns {string} 該類別的詳細說明
 */
function getHelpTextForCategory(category) {
  const categoryConfig = FUNCTIONAL_MODEL_CATEGORIES[category];
  return categoryConfig ? categoryConfig.helpText : '請選擇適合的模型';
}

/**
 * 🎯 根據功能類別獲取工作表行號
 * @param {string} category 功能類別名稱
 * @returns {number} 對應的工作表行號
 */
function getSheetRowForCategory(category) {
  const categoryConfig = FUNCTIONAL_MODEL_CATEGORIES[category];
  return categoryConfig ? categoryConfig.sheetRow : 14;
}

/**
 * 🎯 獲取所有功能類別
 * @returns {Array<string>} 所有功能類別名稱
 */
function getAllFunctionalCategories() {
  return Object.keys(FUNCTIONAL_MODEL_CATEGORIES);
}

/**
 * 🔍 根據行號獲取功能類別
 * @param {number} row 工作表行號
 * @returns {string|null} 對應的功能類別名稱
 */
function getCategoryBySheetRow(row) {
  for (const [category, config] of Object.entries(FUNCTIONAL_MODEL_CATEGORIES)) {
    if (config.sheetRow === row) {
      return category;
    }
  }
  return null;
}

/**
 * 獲取模型的 API 版本
 * @param {string} modelName 模型名稱
 * @returns {string} API 版本 ('v1' 或 'v1beta')
 */
function getModelApiVersion(modelName) {
  const config = GEMINI_MODEL_CONFIG[modelName];
  if (!config) {
    console.warn(`未知模型 ${modelName}，使用預設 v1beta 版本`);
    return 'v1beta';
  }
  return config.apiVersion;
}

/**
 * 獲取模型的完整配置資訊
 * @param {string} modelName 模型名稱
 * @returns {object} 模型配置物件
 */
function getModelConfig(modelName) {
  return GEMINI_MODEL_CONFIG[modelName] || {
    apiVersion: 'v1beta',
    category: 'unknown',
    capabilities: ['text'],
    description: '未知模型',
    recommendedFor: []
  };
}

/**
 * 🎯 根據功能需求推薦模型
 * @param {string} functionType 功能類型
 * @returns {string} 推薦的模型名稱
 */
function getRecommendedModelForFunction(functionType) {
  // 先嘗試從功能分類中查找
  const categoryConfig = FUNCTIONAL_MODEL_CATEGORIES[functionType];
  if (categoryConfig) {
    return categoryConfig.defaultModel;
  }
  
  // 向後相容的功能映射
  const functionMapping = {
    'general_text': 'general',
    'complex_analysis': 'general',
    'quick_response': 'general',
    'vision_analysis': 'vision',
    'image_understanding': 'vision',
    'text_to_speech': 'tts',
    'conversational_audio': 'audio_dialog',
    'image_generation': 'image_generation',
    'semantic_search': 'embedding',
    'cost_effective': 'cost_effective'
  };
  
  const mappedCategory = functionMapping[functionType];
  if (mappedCategory) {
    return getDefaultModelForCategory(mappedCategory);
  }
  
  return 'gemini-2.5-flash'; // 最終預設值
}

/**
 * 🔍 獲取支援特定功能的所有模型
 * @param {string} capability 功能名稱
 * @returns {Array<string>} 支援該功能的模型名稱陣列
 */
function getModelsByCapability(capability) {
  const models = [];
  
  Object.entries(GEMINI_MODEL_CONFIG).forEach(([modelName, config]) => {
    if (config.capabilities.includes(capability)) {
      models.push(modelName);
    }
  });
  
  return models;
}

/**
 * 🎨 獲取圖片生成模型清單
 * @returns {Array<string>} 支援圖片生成的模型
 */
function getImageGenerationModels() {
  return getModelsByCategory('image_generation');
}

/**
 * 🔊 獲取語音功能模型清單
 * @returns {object} 分類的語音模型
 */
function getAudioModels() {
  return {
    tts: getModelsByCategory('tts'),
    dialog: getModelsByCategory('audio_dialog'),
    live: getModelsByCapability('live_api')
  };
}

/**
 * 📊 獲取嵌入向量模型清單
 * @returns {Array<string>} 支援嵌入功能的模型
 */
function getEmbeddingModels() {
  return getModelsByCategory('embedding');
}

/**
 * 檢查模型是否支援特定功能
 * @param {string} modelName 模型名稱
 * @param {string} capability 功能名稱
 * @returns {boolean} 是否支援
 */
function modelSupports(modelName, capability) {
  const config = GEMINI_MODEL_CONFIG[modelName];
  if (!config) return false;
  
  return config.capabilities.includes(capability);
}

/**
 * 🎯 智能模型驗證 - 確保功能與模型匹配
 * @param {string} modelName 模型名稱
 * @param {string} intendedFunction 預期功能
 * @returns {object} 驗證結果
 */
function validateModelForFunction(modelName, intendedFunction) {
  const config = getModelConfig(modelName);
  
  const validationResult = {
    isValid: true,
    warnings: [],
    recommendations: [],
    modelInfo: config
  };
  
  // 🔊 語音功能驗證
  if (intendedFunction === 'text_to_speech' && !modelSupports(modelName, 'text_to_speech')) {
    validationResult.isValid = false;
    validationResult.warnings.push('該模型不支援語音合成功能');
    validationResult.recommendations.push('建議使用：gemini-2.5-flash-preview-tts');
  }
  
  // 🎙️ 對話音頻驗證
  if (intendedFunction === 'conversational_audio' && !modelSupports(modelName, 'audio_dialog')) {
    validationResult.isValid = false;
    validationResult.warnings.push('該模型不支援對話音頻功能');
    validationResult.recommendations.push('建議使用：gemini-2.5-flash-preview-native-audio-dialog');
  }
  
  // 🎨 圖片生成驗證
  if (intendedFunction === 'image_generation' && !modelSupports(modelName, 'image_generation')) {
    validationResult.isValid = false;
    validationResult.warnings.push('該模型不支援圖片生成功能');
    validationResult.recommendations.push('建議使用：gemini-2.0-flash-preview-image-generation');
  }
  
  // 📊 嵌入向量驗證
  if (intendedFunction === 'semantic_search' && !modelSupports(modelName, 'text_embedding')) {
    validationResult.isValid = false;
    validationResult.warnings.push('該模型不支援嵌入向量功能');
    validationResult.recommendations.push('建議使用：gemini-embedding-exp');
  }
  
  return validationResult;
}

/**
 * 驗證模型名稱是否有效
 * @param {string} modelName 模型名稱
 * @returns {boolean} 是否有效
 */
function isValidModel(modelName) {
  return GEMINI_MODEL_CONFIG.hasOwnProperty(modelName);
}

// ===== 🧪 測試函數 =====

/**
 * 🧪 測試功能分類系統
 */
function testFunctionalCategoriesSystem_debug() {
  console.log('=== 測試功能分類系統 v3.0 ===');
  
  const categories = getAllFunctionalCategories();
  
  categories.forEach(category => {
    const config = FUNCTIONAL_MODEL_CATEGORIES[category];
    const models = getModelsByCategory(category);
    const defaultModel = getDefaultModelForCategory(category);
    
    console.log(`📋 ${config.categoryName} (${category})`);
    console.log(`   行號: ${config.sheetRow}`);
    console.log(`   說明: ${config.description}`);
    console.log(`   模型數量: ${models.length}`);
    console.log(`   預設模型: ${defaultModel}`);
    console.log(`   支援模型: ${models.join(', ')}`);
    console.log('');
  });
  
  console.log('✅ 功能分類系統測試完成');
  return `✅ 共 ${categories.length} 個功能分類已配置`;
}

/**
 * 🧪 測試模型推薦系統
 */
function testModelRecommendationSystem_debug() {
  console.log('=== 測試模型推薦系統 ===');
  
  const testFunctions = [
    'general', 'vision', 'tts', 'audio_dialog', 
    'embedding', 'cost_effective', 'image_generation'
  ];
  
  testFunctions.forEach(func => {
    const recommended = getRecommendedModelForFunction(func);
    const validation = validateModelForFunction(recommended, func);
    
    console.log(`🎯 功能: ${func}`);
    console.log(`   推薦模型: ${recommended}`);
    console.log(`   驗證結果: ${validation.isValid ? '✅ 有效' : '❌ 無效'}`);
    if (validation.warnings.length > 0) {
      console.log(`   警告: ${validation.warnings.join(', ')}`);
    }
    console.log('');
  });
  
  console.log('✅ 模型推薦系統測試完成');
}

/**
 * 🧪 測試行號映射系統
 */
function testSheetRowMapping_debug() {
  console.log('=== 測試工作表行號映射 ===');
  
  for (let row = 14; row <= 20; row++) {
    const category = getCategoryBySheetRow(row);
    if (category) {
      const config = FUNCTIONAL_MODEL_CATEGORIES[category];
      console.log(`📍 B${row}: ${config.categoryName} (${category})`);
    } else {
      console.log(`❌ B${row}: 未配置`);
    }
  }
  
  console.log('✅ 行號映射測試完成');
}
