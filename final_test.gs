// 🧪 最終測試：驗證語音和圖片功能修復

function finalIntentTest() {
  console.log('🧪 === 最終意圖檢測測試 ===');
  
  const testCases = [
    {
      input: '你說 你好嗎',
      expected: 'text_to_speech',
      description: '語音功能測試 1'
    },
    {
      input: '你念給我聽：今天天氣很好',
      expected: 'text_to_speech',
      description: '語音功能測試 2'
    },
    {
      input: '能給我炒高麗菜的圖片嗎',
      expected: 'image_generation',
      description: '圖片功能測試 1'
    },
    {
      input: '畫一張貓咪的圖',
      expected: 'image_generation',
      description: '圖片功能測試 2'
    }
  ];
  
  let passCount = 0;
  let totalCount = testCases.length;
  
  testCases.forEach((testCase, index) => {
    console.log(`\n🧪 ${testCase.description}: "${testCase.input}"`);
    
    try {
      // 測試 AI 意圖分析
      const intent = analyzeUserIntentWithAI(testCase.input, 'group', 'test-user');
      console.log(`檢測到的意圖: ${intent.primary_intent} (信心度: ${intent.confidence}%)`);
      console.log(`摘要: ${intent.natural_language_summary}`);
      
      // 檢查結果
      if (intent.primary_intent === testCase.expected) {
        console.log('✅ 測試通過！');
        passCount++;
      } else {
        console.log(`❌ 測試失敗！期望 ${testCase.expected}，但得到 ${intent.primary_intent}`);
        
        // 測試備用檢測
        console.log('🔧 測試備用檢測...');
        const fallback = generateFallbackIntent(testCase.input, 'group');
        console.log(`備用檢測結果: ${fallback.primary_intent} (信心度: ${fallback.confidence}%)`);
        
        if (fallback.primary_intent === testCase.expected) {
          console.log('✅ 備用檢測通過！');
          passCount++;
        }
      }
      
    } catch (error) {
      console.error(`❌ 測試 ${index + 1} 執行失敗:`, error);
    }
  });
  
  console.log(`\n🎯 測試結果: ${passCount}/${totalCount} 通過`);
  
  if (passCount === totalCount) {
    console.log('🎉 所有測試通過！語音和圖片功能應該正常工作了。');
  } else {
    console.log('⚠️ 部分測試失敗，需要進一步調試。');
  }
  
  return { passed: passCount, total: totalCount };
}

function testCompleteFlow() {
  console.log('🧪 === 完整流程測試 ===');
  
  try {
    // 測試語音完整流程
    console.log('\n🔊 測試語音完整流程...');
    const ttsResponse = handleTextMessageAIFirst('你說 你好嗎', null, 'test-user', 'group');
    console.log('語音回應類型:', typeof ttsResponse);
    
    if (typeof ttsResponse === 'object' && ttsResponse.type === 'audio_response') {
      console.log('✅ 語音功能正確返回特殊物件！');
      console.log('音頻結果:', ttsResponse.audioResult ? '有' : '無');
      console.log('原始文字:', ttsResponse.originalText);
    } else {
      console.log('❌ 語音功能沒有返回預期的特殊物件');
      console.log('實際回應:', ttsResponse);
    }
    
    // 測試圖片完整流程
    console.log('\n🎨 測試圖片完整流程...');
    const imageResponse = handleTextMessageAIFirst('能給我炒高麗菜的圖片嗎', null, 'test-user', 'group');
    console.log('圖片回應類型:', typeof imageResponse);
    
    if (typeof imageResponse === 'object' && imageResponse.type === 'image_response') {
      console.log('✅ 圖片功能正確返回特殊物件！');
      console.log('圖片結果:', imageResponse.imageResult ? '有' : '無');
      console.log('原始提示:', imageResponse.originalPrompt);
    } else {
      console.log('❌ 圖片功能沒有返回預期的特殊物件');
      console.log('實際回應:', imageResponse);
    }
    
  } catch (error) {
    console.error('完整流程測試失敗:', error);
  }
}

function runAllTests() {
  console.log('🚀 === 執行所有測試 ===');
  
  finalIntentTest();
  console.log('\n' + '='.repeat(50) + '\n');
  testCompleteFlow();
  
  console.log('\n🎉 所有測試完成！');
}
