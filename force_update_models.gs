/**
 * 🚀 立即強制更新試算表的功能分類系統
 * 執行這個函數來應用新的 Imagen 4.0 模型配置
 */
function forceUpdateImageGenerationModels() {
  console.log('🚀 === 強制更新圖片生成模型配置 ===');
  
  try {
    const ss = SpreadsheetApp.getActiveSpreadsheet();
    const apikeySheet = ss.getSheetByName('APIKEY');
    
    if (!apikeySheet) {
      console.log('❌ APIKEY 工作表不存在');
      return '❌ APIKEY 工作表不存在';
    }
    
    console.log('🔧 檢查當前圖片生成模型配置...');
    
    // 檢查 B20 (圖片生成模型) 的當前下拉選單
    const currentValue = apikeySheet.getRange('B20').getValue();
    const currentValidation = apikeySheet.getRange('B20').getDataValidation();
    
    console.log(`當前 B20 設定值: ${currentValue}`);
    console.log(`有驗證規則: ${currentValidation ? '是' : '否'}`);
    
    // 🎯 獲取新的圖片生成模型清單（包含 Imagen 4.0）
    const imageGenModels = getModelsByCategory('image_generation');
    const helpText = getHelpTextForCategory('image_generation');
    
    console.log(`🎨 新的圖片生成模型清單 (${imageGenModels.length} 個):`);
    imageGenModels.forEach((model, index) => {
      const isImagen4 = model.includes('imagen-4.0');
      const prefix = isImagen4 ? '🆕' : '📸';
      console.log(`${prefix} ${index + 1}. ${model}`);
    });
    
    // 🚀 強制更新 B20 的驗證規則
    console.log('\n🔧 正在更新 B20 下拉選單...');
    
    const newValidationRule = SpreadsheetApp.newDataValidation()
      .requireValueInList(imageGenModels, true)
      .setAllowInvalid(true)
      .setHelpText(helpText)
      .build();
    
    // 應用新的驗證規則到 B20
    apikeySheet.getRange('B20').setDataValidation(newValidationRule);
    
    // 設定欄寬以確保模型名稱能完整顯示
    apikeySheet.setColumnWidth(2, 380); // B欄加寬到380px
    
    // 如果當前值不在新的模型清單中，設定為預設值
    if (!imageGenModels.includes(currentValue)) {
      const defaultModel = getDefaultModelForCategory('image_generation');
      apikeySheet.getRange('B20').setValue(defaultModel);
      console.log(`🔧 已將 B20 設定為預設值: ${defaultModel}`);
    }
    
    // 驗證更新結果
    const updatedValidation = apikeySheet.getRange('B20').getDataValidation();
    const updatedValue = apikeySheet.getRange('B20').getValue();
    
    console.log('\n📊 更新結果驗證:');
    console.log(`✅ 新驗證規則已應用: ${updatedValidation ? '是' : '否'}`);
    console.log(`✅ 當前設定值: ${updatedValue}`);
    console.log(`✅ B欄寬度已調整為 380px`);
    
    // 檢查 Imagen 4.0 是否包含在下拉選單中
    const hasImagen4Standard = imageGenModels.includes('imagen-4.0-generate-preview-06-06');
    const hasImagen4Ultra = imageGenModels.includes('imagen-4.0-ultra-generate-preview-06-06');
    
    console.log('\n🆕 Imagen 4.0 模型驗證:');
    console.log(`✅ Imagen 4.0 標準版: ${hasImagen4Standard ? '已包含' : '❌ 缺失'}`);
    console.log(`✅ Imagen 4.0 Ultra: ${hasImagen4Ultra ? '已包含' : '❌ 缺失'}`);
    
    const success = hasImagen4Standard && hasImagen4Ultra && updatedValidation;
    
    if (success) {
      console.log('\n🎉 更新成功！現在請重新檢查 B20 的下拉選單');
      return `✅ 圖片生成模型更新成功！包含 ${imageGenModels.length} 個模型，Imagen 4.0 已正確包含`;
    } else {
      return `❌ 更新部分成功，但可能仍有問題`;
    }
    
  } catch (error) {
    console.error('❌ 強制更新失敗:', error);
    return `❌ 更新失敗: ${error.message}`;
  }
}

/**
 * 🔍 檢查所有功能的模型配置
 * 驗證每個功能是否都有正確的獨立下拉選單
 */
function checkAllFunctionalDropdowns() {
  console.log('🔍 === 檢查所有功能的下拉選單配置 ===');
  
  try {
    const ss = SpreadsheetApp.getActiveSpreadsheet();
    const apikeySheet = ss.getSheetByName('APIKEY');
    
    if (!apikeySheet) {
      return '❌ APIKEY 工作表不存在';
    }
    
    const categories = getAllFunctionalCategories();
    const results = [];
    
    categories.forEach(category => {
      const config = FUNCTIONAL_MODEL_CATEGORIES[category];
      const models = getModelsByCategory(category);
      const sheetRow = config.sheetRow;
      
      const currentValue = apikeySheet.getRange(`B${sheetRow}`).getValue();
      const hasValidation = !!apikeySheet.getRange(`B${sheetRow}`).getDataValidation();
      
      const result = {
        category: category,
        name: config.categoryName,
        row: sheetRow,
        modelCount: models.length,
        currentValue: currentValue,
        hasValidation: hasValidation,
        isValid: hasValidation && models.includes(currentValue)
      };
      
      results.push(result);
      
      const status = result.isValid ? '✅' : '❌';
      console.log(`${status} ${config.categoryName} (B${sheetRow}): ${models.length} 模型, 當前: ${currentValue}`);
      
      if (category === 'image_generation') {
        console.log(`   🎨 圖片生成模型詳細: ${models.join(', ')}`);
      }
    });
    
    const validCount = results.filter(r => r.isValid).length;
    console.log(`\n📊 總計: ${validCount}/${results.length} 個功能配置正確`);
    
    return `檢查完成: ${validCount}/${results.length} 個功能配置正確`;
    
  } catch (error) {
    console.error('❌ 檢查失敗:', error);
    return `❌ 檢查失敗: ${error.message}`;
  }
}

/**
 * 🚀 一鍵完整更新所有功能分類系統
 * 這會更新所有功能的下拉選單並設定最佳配置
 */
function completeSystemUpdate() {
  console.log('🚀 === 完整系統更新 ===');
  
  try {
    // 1. 執行完整的安全更新
    const ss = SpreadsheetApp.getActiveSpreadsheet();
    const apikeySheet = ss.getSheetByName('APIKEY');
    
    if (!apikeySheet) {
      return '❌ APIKEY 工作表不存在';
    }
    
    console.log('🔧 執行完整系統更新...');
    setupAPIKEYSheetSafe(apikeySheet);
    
    // 2. 特別檢查圖片生成模型
    console.log('\n🎨 特別檢查圖片生成模型...');
    const imageGenResult = forceUpdateImageGenerationModels();
    console.log(imageGenResult);
    
    // 3. 檢查所有功能
    console.log('\n🔍 檢查所有功能配置...');
    const checkResult = checkAllFunctionalDropdowns();
    console.log(checkResult);
    
    console.log('\n✅ 完整系統更新完成！');
    return '✅ 完整系統更新完成！請檢查 B20 的圖片生成模型下拉選單';
    
  } catch (error) {
    console.error('❌ 完整更新失敗:', error);
    return `❌ 更新失敗: ${error.message}`;
  }
}
