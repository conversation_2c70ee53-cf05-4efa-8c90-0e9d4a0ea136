// == Cloudinary 最終修復模組 v3.0 ==
// 🎯 徹底解決音訊和圖片上傳的簽名問題

/**
 * 🔐 生成 Cloudinary 簽名 (最終修復版)
 * 嚴格按照官方文檔，確保簽名參數與 payload 完全一致。
 * @param {Object} paramsToSign - 需要參與簽名的參數對象。
 * @param {string} apiSecret - 你的 Cloudinary API Secret。
 * @returns {string} 生成的 SHA-1 簽名。
 */
function generateCloudinarySignature(paramsToSign, apiSecret) {
  // 1. 按字母順序對 key 進行排序
  const sortedKeys = Object.keys(paramsToSign).sort();

  // 2. 構建 key=value 對，並用 '&' 連接
  const stringToSign = sortedKeys
    .map(key => `${key}=${paramsToSign[key]}`)
    .join('&');

  // 3. 附加 API secret
  const finalStringToSign = stringToSign + apiSecret;

  console.log(`🔐 String to Sign: ${finalStringToSign.substring(0, 150)}...`); // 顯示用於簽名的準確字符串

  // 4. 計算 SHA-1 摘要並轉為十六進制字符串
  const signature = Utilities.computeDigest(Utilities.DigestAlgorithm.SHA_1, finalStringToSign)
    .map(byte => ('0' + (byte & 0xFF).toString(16)).slice(-2))
    .join('');

  return signature;
}

/**
 * 🌩️ 通用 Cloudinary 上傳函數 (最終修復版)
 * @param {Blob} fileBlob - 要上傳的檔案 Blob。
 * @param {Object} options - 上傳選項，例如 { public_id: 'test', format: 'm4a', resource_type: 'video' }。
 * @returns {Object} 上傳結果或錯誤對象。
 */
function universalCloudinaryUpload(fileBlob, options = {}) {
  try {
    const config = getConfig();
    if (!config.cloudinaryCloudName || !config.cloudinaryApiKey || !config.cloudinaryApiSecret) {
      throw new Error('Cloudinary 配置不完整 (cloud_name, api_key, api_secret)');
    }

    // --- 參數準備 ---
    // 1. 準備所有要發送到 Cloudinary 的 payload 參數
    const payload = {
      timestamp: Math.floor(Date.now() / 1000),
      ...options // 將所有外部傳入的選項合併進來
    };

    // --- 簽名生成 ---
    // 2. 直接使用 payload 生成簽名（最關鍵的修復）
    const signature = generateCloudinarySignature(payload, config.cloudinaryApiSecret);

    // --- 表單數據構建 ---
    // 3. 構建包含檔案、API Key 和簽名的最終表單數據
    const formData = {
      file: fileBlob,
      api_key: config.cloudinaryApiKey,
      signature: signature,
      ...payload // 將所有已簽名的參數加入表單
    };

    // 確保 timestamp 是字符串，避免科學記數法問題
    formData.timestamp = String(formData.timestamp);

    // --- API 請求 ---
    // 4. 根據 resource_type 確定上傳 URL
    const resourceType = options.resource_type || 'image';
    const uploadUrl = `https://api.cloudinary.com/v1_1/${config.cloudinaryCloudName}/${resourceType}/upload`;

    console.log(`📤 Uploading to: ${uploadUrl}`);
    console.log(`📋 Payload:`, formData);

    const response = UrlFetchApp.fetch(uploadUrl, {
      method: 'POST',
      payload: formData,
      muteHttpExceptions: true
    });

    // --- 結果處理 ---
    const responseCode = response.getResponseCode();
    const responseText = response.getContentText();

    console.log(`📊 Response Code: ${responseCode}`);
    if (responseCode !== 200) {
      console.error(`❌ Upload Failed: ${responseText}`);
      throw new Error(`Cloudinary 上傳失敗: ${responseCode} - ${responseText}`);
    }

    const result = JSON.parse(responseText);
    console.log(`✅ Upload Successful: ${result.secure_url}`);
    return { success: true, result: result };

  } catch (error) {
    console.error('❌ universalCloudinaryUpload Error:', error);
    return { success: false, error: error.message };
  }
}


/**
 * 🧪 最終驗證測試：同時測試音訊和圖片上傳 (修正版)
 * 修正了 Utilities.newBlob 的參數錯誤。
 * 請執行此函數來驗證修復效果。
 */
function testAudioAndImageUpload_Final() {
  console.log('🧪 === 最終驗證測試：音訊與圖片 (v2) === 🧪');

  // --- 1. 測試音訊上傳 ---
  console.log('\n--- 1. Testing AUDIO Upload ---');
  try {
    // 修正：使用空的 byte[] 陣列創建測試 Blob
    const testAudioBlob = Utilities.newBlob([], 'audio/wav', 'test.wav');
    const audioOptions = {
      public_id: `linebot/test/audio_final_${new Date().getTime()}`,
      resource_type: 'video', // 音訊使用 'video'
      format: 'm4a'
    };
    const audioResult = universalCloudinaryUpload(testAudioBlob, audioOptions);
    if (audioResult.success) {
      console.log('🎉 音訊上傳成功！URL:', audioResult.result.secure_url);
    } else {
      console.error('🔥 音訊上傳失敗:', audioResult.error);
    }
  } catch (e) {
    console.error('🔥 執行音訊上傳測試時發生錯誤:', e);
  }

  // --- 2. 測試圖片上傳 ---
  console.log('\n--- 2. Testing IMAGE Upload ---');
  try {
    // 修正：使用空的 byte[] 陣列創建測試 Blob
    const testImageBlob = Utilities.newBlob([], 'image/png', 'test.png');
    const imageOptions = {
      public_id: `linebot/test/image_final_${new Date().getTime()}`,
      resource_type: 'image'
    };
    const imageResult = universalCloudinaryUpload(testImageBlob, imageOptions);
    if (imageResult.success) {
      console.log('🎉 圖片上傳成功！URL:', imageResult.result.secure_url);
    } else {
      console.error('🔥 圖片上傳失敗:', imageResult.error);
    }
  } catch (e) {
    console.error('🔥 執行圖片上傳測試時發生錯誤:', e);
  }

  console.log('\n🧪 === 測試完成 === 🧪');
}
