// 🔧 輔助函數模組 v1.3.1
// 通用輔助函數集合，避免跨檔案重複代碼

// ===== 🔧 通用輔助函數 =====

/**
 * 🔧 安全的對話記錄函數
 * 包含多層錯誤處理和備用機制
 */
function recordConversationSafely(userId, originalMessage, botResponse, sourceType, userIntent) {
  try {
    initializeSheets();

    const recordResult = memoryMiddleware('record_conversation', {
      userId: userId,
      originalMessage: originalMessage,
      botResponse: botResponse,
      detectedIntent: userIntent.primary_intent,
      confidence: userIntent.confidence,
      sourceType: sourceType
    });

    if (recordResult) {
      console.log(`✅ 對話記錄成功: 用戶=${userId}, 意圖=${userIntent.primary_intent}`);
      logActivity('對話記錄成功', `用戶=${userId}, 意圖=${userIntent.primary_intent}, 信心度=${userIntent.confidence}%`);
    } else {
      recordConversationBackup(userId, originalMessage, botResponse, sourceType);
    }
  } catch (memoryError) {
    console.error('❌ 對話記錄異常:', memoryError);
    try {
      recordConversationBackup(userId, originalMessage, botResponse, sourceType);
    } catch (backupError) {
      console.error('❌ 備用對話記錄也失敗:', backupError);
    }
  }
}

/**
 * 🔧 記錄特殊回覆到記憶系統
 */
function recordSpecialResponse(userId, originalMessage, responseDescription, sourceType, userIntent) {
  try {
    initializeSheets();

    memoryMiddleware('record_conversation', {
      userId: userId,
      originalMessage: originalMessage,
      botResponse: responseDescription,
      detectedIntent: userIntent.primary_intent,
      confidence: userIntent.confidence,
      sourceType: sourceType
    });

    console.log(`✅ 特殊回覆記錄成功: ${responseDescription}`);

  } catch (error) {
    console.error('特殊回覆記錄失敗:', error);
    try {
      recordConversationBackup(userId, originalMessage, responseDescription, sourceType);
    } catch (backupError) {
      console.error('特殊回覆備用記錄也失敗:', backupError);
    }
  }
}

/**
 * 🔧 備用對話記錄方法
 */
function recordConversationBackup(userId, originalMessage, botResponse, sourceType) {
  try {
    console.log(`🔄 使用備用方法記錄對話: 用戶=${userId}, 來源=${sourceType}`);

    const ss = SpreadsheetApp.getActiveSpreadsheet();
    let sheet = ss.getSheetByName('用戶對話歷史');

    // 如果工作表不存在，創建它
    if (!sheet) {
      sheet = ss.insertSheet('用戶對話歷史');
      const headers = ['時間戳', '用戶ID', '訊息類型', '原始訊息', '處理結果', '關聯檔案ID', '來源類型'];
      sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
      sheet.getRange(1, 1, 1, headers.length).setFontWeight('bold');
      sheet.getRange(1, 1, 1, headers.length).setBackground('#e1f5fe');
      console.log('✅ 備用方法: 已建立用戶對話歷史工作表');
    }

    const timestamp = new Date();
    const rowData = [
      timestamp,           // 時間戳
      userId,             // 用戶ID
      'text',             // 訊息類型
      originalMessage,    // 原始訊息
      botResponse,        // 處理結果
      '',                 // 關聯檔案ID
      sourceType          // 來源類型
    ];

    sheet.appendRow(rowData);
    console.log(`✅ 備用方法成功記錄對話: ${userId} - ${originalMessage.substring(0, 30)}...`);

    logActivity('備用對話記錄成功', `用戶:${userId}, 內容:${originalMessage.substring(0, 30)}...`);
    return true;

  } catch (error) {
    console.error('❌ 備用對話記錄方法錯誤:', error);
    throw error;
  }
}

/**
 * 🎯 處理特殊回覆類型（音頻、圖片等）
 */
function handleSpecialResponse(response, replyToken, originalText, userId, sourceType, userIntent) {
  try {
    console.log(`🎯 處理特殊回覆類型: ${response.type}`);

    switch (response.type) {
      case 'audio_response':
        // 🔊 音頻回覆
        if (replyToken) {
          replyWithAudio(replyToken, response.audioResult, response.originalText);
          logActivity('音頻回覆', `用戶${userId}(${sourceType}): ${response.originalText.substring(0, 50)}...`);
        }

        // 記錄對話
        recordSpecialResponse(userId, originalText, `[音頻] ${response.originalText}`, sourceType, userIntent);
        break;

      case 'image_response':
        // 🎨 圖片回覆
        if (replyToken) {
          replyWithImage(replyToken, response.imageResult, response.originalPrompt);
          logActivity('圖片回覆', `用戶${userId}(${sourceType}): ${response.originalPrompt.substring(0, 50)}...`);
        }

        // 記錄對話
        recordSpecialResponse(userId, originalText, `[圖片] ${response.originalPrompt}`, sourceType, userIntent);
        break;

      default:
        console.warn(`未知的特殊回覆類型: ${response.type}`);
        // 備用：發送文字回覆
        if (replyToken) {
          replyMessage(replyToken, '🤖 處理完成，但回覆格式有問題。請稍後再試。');
        }
    }

  } catch (error) {
    console.error('處理特殊回覆失敗:', error);
    if (replyToken) {
      replyMessage(replyToken, '🤖 處理時遇到問題，請稍後再試。');
    }
  }
}

/**
 * 🚨 AI 處理錯誤時的智能回復
 */
function handleAIProcessingError(originalText, replyToken, userId, sourceType, error) {
  console.error('AI-First 處理失敗，切換到備用模式:', error);
  
  // 記錄錯誤但不影響用戶體驗
  logActivity('AI處理錯誤', `用戶: ${userId}, 訊息: ${originalText}, 錯誤: ${error.message}`);
  
  // 提供友善的錯誤回應
  const errorResponse = `🤖 抱歉，我的 AI 大腦剛才卡住了一下！\n\n讓我用簡單的方式來幫助您：\n\n💡 您想要：\n• 記錄筆記？\n• 查詢資訊？\n• 分析檔案？\n• 其他需求？\n\n請再說一次，我會努力理解的！\n\n🔧 v1.3.1 重構版：系統更穩定，錯誤處理更佳`;
  
  if (replyToken) {
    replyMessage(replyToken, errorResponse);
  }
  
  return errorResponse;
}

// ===== 🔧 內容提取函數 =====

/**
 * 🔊 提取 TTS 文字內容
 * 從用戶訊息中提取要轉換成語音的文字
 */
function extractTextForTTS(originalMessage) {
  try {
    // 使用統一提示詞系統提取
    return callAIWithPrompt('TTS_TEXT_EXTRACTION', {
      message: originalMessage
    });
  } catch (error) {
    console.error('TTS 文字提取失敗:', error);
    // 備用：簡單的正則提取
    let extractedText = originalMessage;
    
    // 移除常見的 TTS 指令詞
    extractedText = extractedText.replace(/^(你說|你念|念出來|讀出來|語音|播放|說出來)[：:：\s]*/i, '');
    extractedText = extractedText.replace(/^(tts|語音轉換)[：:：\s]*/i, '');
    
    // 如果提取後為空，返回原始訊息
    return extractedText.trim() || originalMessage;
  }
}

/**
 * 🎨 提取圖像生成提示詞
 * 從用戶訊息中提取圖像描述
 */
function extractImagePrompt(originalMessage) {
  try {
    // 使用統一提示詞系統提取
    return callAIWithPrompt('IMAGE_PROMPT_EXTRACTION', {
      message: originalMessage
    });
  } catch (error) {
    console.error('圖像提示詞提取失敗:', error);
    // 備用：簡單的正則提取
    let extractedPrompt = originalMessage;
    
    // 移除常見的圖像生成指令詞
    extractedPrompt = extractedPrompt.replace(/^(畫|畫一|畫個|生成|生成圖|創建|創建圖|給我.*圖)[：:：\s]*/i, '');
    extractedPrompt = extractedPrompt.replace(/^(draw|generate|create)[：:：\s]*/i, '');
    extractedPrompt = extractedPrompt.replace(/的圖片?$|的圖像?$/i, '');
    
    // 如果提取後為空，返回原始訊息
    return extractedPrompt.trim() || originalMessage;
  }
}

// ===== 🧪 測試函數 =====

/**
 * 🧪 測試輔助函數
 */
function testHelperFunctions_debug() {
  console.log('🧪 === 測試輔助函數 v1.3.1 ===');
  
  const testCases = [
    {
      category: 'TTS 文字提取',
      function: extractTextForTTS,
      tests: [
        { input: '你說：今天天氣很好', expected: '今天天氣很好' },
        { input: '念出來：我愛台灣', expected: '我愛台灣' },
        { input: '語音播放：測試文字', expected: '測試文字' },
        { input: 'TTS: Hello World', expected: 'Hello World' },
        { input: '直接說話內容', expected: '直接說話內容' }
      ]
    },
    {
      category: '圖像提示詞提取',
      function: extractImagePrompt,
      tests: [
        { input: '畫一隻可愛的小貓', expected: '隻可愛的小貓' },
        { input: '生成圖片：夕陽下的海邊', expected: '夕陽下的海邊' },
        { input: '創建一張科幻風格的城市圖片', expected: '一張科幻風格的城市' },
        { input: 'draw a beautiful sunset', expected: 'a beautiful sunset' },
        { input: '機器人圖像', expected: '機器人圖像' }
      ]
    }
  ];
  
  testCases.forEach(category => {
    console.log(`\n📋 測試 ${category.category}`);
    console.log('=' + '='.repeat(category.category.length + 5));
    
    category.tests.forEach((test, index) => {
      try {
        const result = category.function(test.input);
        const isMatch = result.includes(test.expected) || test.expected.includes(result);
        
        console.log(`${index + 1}. 輸入: "${test.input}"`);
        console.log(`   預期: "${test.expected}"`);
        console.log(`   結果: "${result}"`);
        console.log(`   狀態: ${isMatch ? '✅ 通過' : '⚠️ 部分匹配'}`);
        
      } catch (error) {
        console.log(`${index + 1}. 輸入: "${test.input}"`);
        console.log(`   ❌ 錯誤: ${error.message}`);
      }
    });
  });
  
  console.log('\n✅ 輔助函數測試完成');
}

/**
 * 🧪 測試對話記錄功能
 */
function testConversationRecording_debug() {
  console.log('🧪 === 測試對話記錄功能 ===');
  
  try {
    const testIntent = {
      primary_intent: 'general_question',
      confidence: 85
    };
    
    console.log('📝 測試安全對話記錄...');
    recordConversationSafely('test-user', '測試訊息', '測試回應', 'group', testIntent);
    console.log('✅ 安全對話記錄測試完成');
    
    console.log('📝 測試特殊回覆記錄...');
    recordSpecialResponse('test-user', '測試語音', '[音頻] 測試語音內容', 'group', testIntent);
    console.log('✅ 特殊回覆記錄測試完成');
    
    console.log('📝 測試備用記錄方法...');
    recordConversationBackup('test-user', '備用測試', '備用回應', 'group');
    console.log('✅ 備用記錄方法測試完成');
    
  } catch (error) {
    console.error('❌ 對話記錄測試失敗:', error);
  }
}

/**
 * 🧪 測試錯誤處理功能
 */
function testErrorHandling_debug() {
  console.log('🧪 === 測試錯誤處理功能 ===');
  
  try {
    const testError = new Error('模擬錯誤');
    
    console.log('⚠️ 測試 AI 處理錯誤...');
    const errorResponse = handleAIProcessingError('測試訊息', null, 'test-user', 'group', testError);
    console.log(`✅ 錯誤回應: ${errorResponse.substring(0, 50)}...`);
    
  } catch (error) {
    console.error('❌ 錯誤處理測試失敗:', error);
  }
}

/**
 * 🧪 完整輔助函數測試套件
 */
function runHelperFunctionTestSuite_debug() {
  console.log('🚀 === 執行輔助函數完整測試套件 v1.3.1 ===');
  
  try {
    console.log('📋 階段 1: 內容提取測試');
    testHelperFunctions_debug();
    
    console.log('\n📋 階段 2: 對話記錄測試');
    testConversationRecording_debug();
    
    console.log('\n📋 階段 3: 錯誤處理測試');
    testErrorHandling_debug();
    
    console.log('\n🎉 輔助函數測試套件執行完成！');
    console.log('📊 v1.3.1 重構版：通用函數集中管理，避免重複代碼');
    
  } catch (error) {
    console.error('❌ 輔助函數測試套件執行失敗:', error);
  }
}

/**
 * 📋 輔助函數模組說明 v1.3.1
 * 
 * 🎯 本檔案職責：
 * - 通用輔助函數（避免跨檔案重複）
 * - 對話記錄相關函數
 * - 特殊回覆處理函數
 * - 錯誤處理函數
 * - 內容提取函數
 * - 測試函數
 * 
 * 🔗 被以下檔案調用：
 * - TextProcessor_AIFirst.gs
 * - AIHandlers_Specialized.gs
 * - 其他處理模組
 * 
 * 🎉 v1.3.1 改進：集中管理通用函數，消除重複代碼
 */
