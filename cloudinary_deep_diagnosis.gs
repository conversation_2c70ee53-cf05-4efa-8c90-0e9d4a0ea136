// == Cloudinary 簽名問題深度診斷 ==
// 🔍 詳細分析簽名失敗的根本原因

/**
 * 🔍 深度診斷 Cloudinary 簽名問題
 */
function deepDiagnoseCloudinarySignature_debug() {
  console.log('🔍 === 深度診斷 Cloudinary 簽名問題 ===');
  
  try {
    const config = getConfig();
    
    // 1. 檢查 API Secret
    console.log('1️⃣ 檢查 API Secret...');
    console.log(`   API Secret 長度: ${config.cloudinaryApiSecret ? config.cloudinaryApiSecret.length : 0}`);
    console.log(`   API Secret 前3位: ${config.cloudinaryApiSecret ? config.cloudinaryApiSecret.substring(0, 3) : '無'}`);
    console.log(`   API Secret 後3位: ${config.cloudinaryApiSecret ? config.cloudinaryApiSecret.substring(-3) : '無'}`);
    
    // 2. 模擬完整的上傳流程
    console.log('\n2️⃣ 模擬完整上傳流程...');
    
    const timestamp = Math.floor(Date.now() / 1000);
    const publicId = `test/debug_${timestamp}`;
    
    console.log(`   生成的 timestamp: ${timestamp}`);
    console.log(`   公開 ID: ${publicId}`);
    
    // 3. 檢查簽名參數構建
    console.log('\n3️⃣ 檢查簽名參數構建...');
    
    const signatureParams = {
      public_id: publicId,
      resource_type: 'video',
      format: 'm4a',
      timestamp: timestamp
    };
    
    console.log('   簽名參數:', signatureParams);
    
    // 手動構建簽名字符串
    const sortedKeys = Object.keys(signatureParams)
      .filter(key => key !== 'file' && key !== 'api_key')
      .sort();
    
    console.log('   排序後的鍵:', sortedKeys);
    
    const paramString = sortedKeys
      .map(key => `${key}=${String(signatureParams[key])}`)
      .join('&');
    
    console.log(`   參數字符串: ${paramString}`);
    
    const stringToSign = paramString + config.cloudinaryApiSecret;
    console.log(`   完整簽名字符串: ${stringToSign.substring(0, 80)}...`);
    
    // 4. 生成簽名
    console.log('\n4️⃣ 生成簽名...');
    
    const signature1 = generateCloudinarySignature(signatureParams, config.cloudinaryApiSecret);
    console.log(`   我們的簽名: ${signature1}`);
    
    // 5. 檢查表單數據構建
    console.log('\n5️⃣ 檢查表單數據構建...');
    
    const formData = {
      public_id: publicId,
      resource_type: 'video',
      format: 'm4a',
      api_key: config.cloudinaryApiKey,
      timestamp: String(timestamp),
      signature: signature1
    };
    
    console.log('   表單數據:');
    Object.keys(formData).forEach(key => {
      if (key !== 'signature') {
        console.log(`     ${key}: "${formData[key]}" (${typeof formData[key]})`);
      } else {
        console.log(`     ${key}: ${formData[key].substring(0, 20)}...`);
      }
    });
    
    // 6. 測試不同的簽名方法
    console.log('\n6️⃣ 測試不同的簽名方法...');
    
    // 方法1: 標準方法
    const method1Signature = generateCloudinarySignature(signatureParams, config.cloudinaryApiSecret);
    console.log(`   方法1 (標準): ${method1Signature}`);
    
    // 方法2: 手動 SHA-1
    const method2String = paramString + config.cloudinaryApiSecret;
    const method2Signature = Utilities.computeDigest(Utilities.DigestAlgorithm.SHA_1, method2String)
      .map(byte => ('0' + (byte & 0xFF).toString(16)).slice(-2))
      .join('');
    console.log(`   方法2 (手動): ${method2Signature}`);
    
    // 7. 檢查與 Cloudinary 期望的差異
    console.log('\n7️⃣ 檢查可能的問題...');
    
    // 檢查是否有特殊字符需要編碼
    const needsEncoding = /[^a-zA-Z0-9_\-\/]/.test(publicId);
    console.log(`   Public ID 需要編碼: ${needsEncoding}`);
    
    // 檢查參數順序
    const expectedOrder = ['format', 'public_id', 'resource_type', 'timestamp'];
    const actualOrder = sortedKeys;
    const orderMatch = JSON.stringify(expectedOrder) === JSON.stringify(actualOrder);
    console.log(`   參數順序正確: ${orderMatch}`);
    console.log(`   期望順序: ${expectedOrder.join(', ')}`);
    console.log(`   實際順序: ${actualOrder.join(', ')}`);
    
    // 8. 嘗試簡化的簽名測試
    console.log('\n8️⃣ 簡化簽名測試...');
    
    const simpleParams = {
      public_id: 'test',
      timestamp: 1234567890
    };
    
    const simpleSignature = generateCloudinarySignature(simpleParams, config.cloudinaryApiSecret);
    console.log(`   簡化簽名: ${simpleSignature}`);
    
    return {
      success: true,
      diagnosis: {
        apiSecretLength: config.cloudinaryApiSecret.length,
        timestamp: timestamp,
        paramString: paramString,
        signature: signature1,
        orderMatch: orderMatch
      }
    };
    
  } catch (error) {
    console.error('❌ 深度診斷失敗:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 🧪 測試 Cloudinary 官方範例
 */
function testCloudinaryOfficialExample_debug() {
  console.log('🧪 === 測試 Cloudinary 官方範例 ===');
  
  try {
    const config = getConfig();
    
    // Cloudinary 官方文檔範例
    // https://cloudinary.com/documentation/upload_images#generating_authentication_signatures
    
    console.log('📋 使用 Cloudinary 官方文檔的參數範例...');
    
    // 官方範例參數
    const officialParams = {
      public_id: 'sample_image',
      timestamp: 1315060510
    };
    
    console.log('   官方範例參數:', officialParams);
    
    // 構建簽名字符串
    const paramString = 'public_id=sample_image&timestamp=1315060510';
    console.log(`   參數字符串: ${paramString}`);
    
    // 如果我們使用官方範例的 API Secret (用於測試)
    const testApiSecret = 'abcd1234'; // 這只是測試用的假 API Secret
    const testStringToSign = paramString + testApiSecret;
    console.log(`   測試簽名字符串: ${testStringToSign}`);
    
    // 生成測試簽名
    const testSignature = Utilities.computeDigest(Utilities.DigestAlgorithm.SHA_1, testStringToSign)
      .map(byte => ('0' + (byte & 0xFF).toString(16)).slice(-2))
      .join('');
    console.log(`   測試簽名: ${testSignature}`);
    
    // 現在用我們的 API Secret 測試
    console.log('\n🔑 使用我們的 API Secret 測試...');
    
    const ourStringToSign = paramString + config.cloudinaryApiSecret;
    const ourSignature = Utilities.computeDigest(Utilities.DigestAlgorithm.SHA_1, ourStringToSign)
      .map(byte => ('0' + (byte & 0xFF).toString(16)).slice(-2))
      .join('');
    console.log(`   我們的簽名: ${ourSignature}`);
    
    // 使用我們的函數生成
    const functionSignature = generateCloudinarySignature(officialParams, config.cloudinaryApiSecret);
    console.log(`   函數生成簽名: ${functionSignature}`);
    
    const signaturesMatch = ourSignature === functionSignature;
    console.log(`   簽名匹配: ${signaturesMatch ? '✅' : '❌'}`);
    
    return {
      success: true,
      testSignature: testSignature,
      ourSignature: ourSignature,
      functionSignature: functionSignature,
      match: signaturesMatch
    };
    
  } catch (error) {
    console.error('❌ 官方範例測試失敗:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 🔧 嘗試修復 Cloudinary 簽名問題
 */
function attemptCloudinarySignatureFix_debug() {
  console.log('🔧 === 嘗試修復 Cloudinary 簽名問題 ===');
  
  try {
    const config = getConfig();
    
    // 1. 檢查 API 憑證是否正確
    console.log('1️⃣ 驗證 API 憑證...');
    
    // 使用最簡單的上傳測試
    const timestamp = Math.floor(Date.now() / 1000);
    const publicId = `test_simple_${timestamp}`;
    
    console.log(`   測試 Public ID: ${publicId}`);
    console.log(`   測試 Timestamp: ${timestamp}`);
    
    // 2. 嘗試不同的參數組合
    console.log('\n2️⃣ 嘗試不同的參數組合...');
    
    // 組合1: 最小參數集
    const minimalParams = {
      public_id: publicId,
      timestamp: timestamp
    };
    
    const minimalSignature = generateCloudinarySignature(minimalParams, config.cloudinaryApiSecret);
    console.log(`   最小參數簽名: ${minimalSignature}`);
    
    // 組合2: 包含 resource_type
    const withResourceParams = {
      public_id: publicId,
      resource_type: 'auto',
      timestamp: timestamp
    };
    
    const resourceSignature = generateCloudinarySignature(withResourceParams, config.cloudinaryApiSecret);
    console.log(`   包含 resource_type 簽名: ${resourceSignature}`);
    
    // 3. 嘗試實際上傳（使用最小參數）
    console.log('\n3️⃣ 嘗試最小參數上傳...');
    
    try {
      // 創建最小測試檔案
      const testData = new ArrayBuffer(100);
      const testBlob = Utilities.newBlob(new Uint8Array(testData), 'text/plain', 'test.txt');
      
      const minimalFormData = {
        file: testBlob,
        public_id: publicId,
        api_key: config.cloudinaryApiKey,
        timestamp: String(timestamp),
        signature: minimalSignature
      };
      
      console.log('   使用最小參數組合上傳...');
      
      const uploadUrl = `https://api.cloudinary.com/v1_1/${config.cloudinaryCloudName}/upload`;
      
      const response = UrlFetchApp.fetch(uploadUrl, {
        method: 'POST',
        payload: minimalFormData,
        muteHttpExceptions: true
      });
      
      const responseCode = response.getResponseCode();
      const responseText = response.getContentText();
      
      console.log(`   回應代碼: ${responseCode}`);
      
      if (responseCode === 200) {
        console.log('✅ 最小參數上傳成功！');
        const result = JSON.parse(responseText);
        console.log(`   上傳結果: ${result.secure_url}`);
        return {
          success: true,
          method: 'minimal',
          url: result.secure_url
        };
      } else {
        console.log(`❌ 最小參數上傳失敗: ${responseText}`);
      }
      
    } catch (uploadError) {
      console.log(`❌ 上傳錯誤: ${uploadError.message}`);
    }
    
    // 4. 檢查 API Secret 是否正確
    console.log('\n4️⃣ 驗證 API Secret...');
    
    // 檢查 API Secret 格式
    const apiSecretPattern = /^[a-zA-Z0-9_-]+$/;
    const isValidFormat = apiSecretPattern.test(config.cloudinaryApiSecret);
    console.log(`   API Secret 格式有效: ${isValidFormat ? '✅' : '❌'}`);
    
    if (!isValidFormat) {
      console.log('⚠️ API Secret 格式可能有問題');
      console.log('💡 請檢查 APIKEY 工作表中的 B10 儲存格');
      console.log('💡 確保沒有多餘的空格或特殊字符');
    }
    
    return {
      success: false,
      diagnosis: 'API Secret 或參數問題',
      recommendation: '請檢查 Cloudinary 憑證是否正確'
    };
    
  } catch (error) {
    console.error('❌ 修復嘗試失敗:', error);
    return {
      success: false,
      error: error.message
    };
  }
}
