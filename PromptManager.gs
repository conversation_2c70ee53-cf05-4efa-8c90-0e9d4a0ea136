// == 統一提示詞管理系統 ==
// 🤖 集中管理所有 AI 提示詞，實現真正的 AI First 體驗
// 🎯 重點：自然聊天口氣，避免 AI 味和八股文
// 🚀 v2.1 - 新增智能引導模板，支援雙重檢測機制

/**
 * 🧠 提示詞分類管理
 * 按功能分組，統一風格和調用方式
 */
const PROMPTS = {
  
  // === 意圖理解類 ===
  INTENT_ANALYSIS: {
    template: `你是個聰明的助手，幫我理解用戶想要什麼。

用戶說：「{userMessage}」
環境：{sourceType}

用戶可能想要：
• 記錄筆記 - 像是「記下這個」、「幫我記住」
• 查檔案 - 像是「剛才那個圖片」、「之前的文件」
• 看聊天記錄 - 像是「我們聊了什麼」
• 查群組成員發言 - 像是「Michael都說了什麼」、「蚵仔煎有提到吃的嗎」
• 發社群貼文 - 像是「幫我po文」
• 文字轉語音 - 🔊 重要！如果包含「你說」、「你念」、「念出來」、「讀出來」、「語音」、「播放」、「說出來」等關鍵字，必須識別為 text_to_speech
  範例：「你說 你好嗎」、「你念給我聽：今天天氣很好」、「念出來：測試語音」
• 對話音頻 - 🎙️ 重要！如果包含「語音聊天」、「語音對話」、「跟我語音聊」、「跟我聊」、「聊聊天」、「聊聊」、「聊天」、「用語音」、「語音回應」等語音對話關鍵字，必須識別為 conversational_audio
  範例：「語音聊天 今天天氣如何」、「語音對話 你好嗎」、「跟我語音聊聊天」、「聊聊天」、「跟我聊天」、「聊聊」、「用語音跟我說話」
• 生成圖片 - 🎨 重要！如果包含「畫」、「圖片」、「圖像」、「生成圖」、「創建圖」、「給我...圖」等關鍵字，必須識別為 image_generation
  範例：「畫一張貓咪的圖」、「能給我炒高麗菜的圖片嗎」、「生成圖像：美麗的風景」
• 語義搜索 - 📊 如果是要找相似內容、相關檔案、文檔匹配
  範例：「找相關的文件」、「有沒有類似的內容」、「搜索相關檔案」
• 批量處理 - ⚡ 如果是要快速處理、批量任務、大量操作
  範例：「快速整理這些」、「批量處理」、「幫我快速分類」
• 一般問題 - 各種問答
• 閒聊 - 打招呼、聊天
• 需要幫助 - 問怎麼用

請用 JSON 回答，primary_intent 必須是以下其中一個：
- note_taking (記錄筆記)
- file_query (查檔案)
- conversation_review (看聊天記錄)
- group_member_query (查群組成員發言)
- social_media_post (發社群貼文)
- text_to_speech (文字轉語音) ⭐ 重要：包含「你說」、「你念」、「念出來」等關鍵字
- conversational_audio (對話音頻) 🎙️ 重要：包含「語音聊天」、「語音對話」、「跟我語音聊」、「跟我聊」、「聊聊天」、「聊聊」、「聊天」、「用語音」、「語音回應」等語音對話關鍵字
- image_generation (生成圖片) ⭐ 重要：包含「畫」、「圖片」、「生成圖」等關鍵字
- semantic_search (語義搜索) 📊 新增：找相似內容、相關檔案
- batch_processing (批量處理) ⚡ 新增：快速處理、批量任務
- system_command (系統指令) 🔧 重要：以「!」開頭或明確的系統關鍵字，如「!測試」、「!help」、「測試」、「help」、「功能」、「狀態」
- model_examples (模型範例) 📚 重要：想了解各功能使用方法，如「範例」、「怎麼用」、「使用方法」
- general_question (一般問題)
- casual_chat (閒聊)
- system_help (需要幫助)
- drive_link_sharing (分享連結)

{
  "primary_intent": "從上面列表選一個",
  "confidence": 信心度(0-100),
  "key_entities": ["重要的人名或關鍵字"],
  "natural_language_summary": "用白話說用戶想幹嘛",
  "suggested_action": "建議怎麼處理"
}`,

    params: ['userMessage', 'sourceType']
  },

  // === 🤔 智能引導類 - 新增 ===
  
  // 統一智能引導模板
  UNIFIED_GUIDANCE: {
    template: `🤔 我不太確定您的需求，我可以幫您：

🔧 **系統功能**
• 「測試」- 檢查系統狀態
• 「help」- 完整功能說明  
• 「範例」- 使用示範

📝 **AI助理** (直接說話即可)
• 記錄筆記：「記住明天開會」
• 查詢對話：「我們聊了什麼」
• 群組查詢：「Michael說了什麼」  
• 語音功能：「你說今天很棒」
• 圖片生成：「畫一隻貓」

💡 直接告訴我您想做什麼，我會理解您的意圖！`,
    
    params: []
  },

  // === 🔊 TTS 和音頻處理類 ===
  
  // TTS 模式分析（判斷鸚鵡模式 vs 對話模式）
  TTS_MODE_ANALYSIS: {
    template: `分析用戶想要什麼類型的語音功能：

用戶說：「{message}」

有兩種模式：
🦜 鸚鵡模式：用戶明確要求複述特定文字
  - 關鍵字：「你說」、「重複」、「複述」、「念出來」、「讀出來」
  - 範例：「你說：今天天氣很好」、「念出來：我愛你」、「重複這句話」

🎙️ 對話模式：用戶想要智能對話回應並轉成語音
  - 關鍵字：「聊聊」、「聊天」、「說話」、「對話」、「跟我聊」、「語音聊天」
  - 範例：「跟我聊聊天」、「今天天氣如何？」、「你好嗎？」、「語音聊天」、「用語音跟我說話」

請用 JSON 回答：
{
  "mode": "parrot" 或 "conversational",
  "textToSpeak": "如果是鸚鵡模式，要說的具體文字",
  "userQuery": "如果是對話模式，用戶的問題",
  "confidence": 信心度(0-100)
}`,

    params: ['message']
  },

  // TTS 文字提取
  TTS_TEXT_EXTRACTION: {
    template: `從用戶的話中提取要轉換成語音的文字：

用戶說：「{message}」

請只回傳需要轉換成語音的核心文字內容，不要包含「念出來」、「語音」、「播放」、「你說」這些指令詞。
如果沒有明確的文字內容，就回傳原始訊息。

要轉換的文字：`,

    params: ['message']
  },

  // === 🎨 圖像生成類 ===
  
  // 圖像提示詞提取
  IMAGE_PROMPT_EXTRACTION: {
    template: `從用戶的話中提取圖像生成的描述：

用戶說：「{message}」

請只回傳要生成圖像的描述內容，不要包含「畫」、「生成」、「創建」、「給我」這些指令詞。
如果沒有明確的描述，就回傳原始訊息。

圖像描述：`,

    params: ['message']
  },

  // 圖像提示詞增強
  IMAGE_PROMPT_ENHANCEMENT: {
    template: `幫我改善這個圖像生成提示詞，讓 AI 生成更好的圖片：

原始描述：「{originalPrompt}」

請：
• 增加具體的視覺細節
• 指定風格和氛圍
• 加上光線和構圖建議
• 保持描述簡潔明確
• 用英文回答（因為圖像生成模型對英文效果更好）

優化後的提示詞：`,

    params: ['originalPrompt']
  },

  // === 📊 語義搜索類 ===
  
  // 語義搜索回應生成
  SEMANTIC_SEARCH_RESPONSE: {
    template: `用戶查詢：「{query}」

我找到了 {resultCount} 個相關結果：{topResults}

請用自然聊天的口氣回應：
• 告訴用戶找到了相關內容
• 簡單說明結果的相關性
• 語氣要友善自然
• 可以建議下一步操作

回應：`,

    params: ['query', 'resultCount', 'topResults']
  },

  // === 查詢解析類 ===
  QUERY_PARSING: {
    template: `幫我分析用戶的查詢需求：

用戶問：「{userQuery}」

我需要知道：
• 想查誰的發言？（用戶名稱）
• 要看多少筆？（數量限制，預設50筆）
• 是要簡單列表還是深度分析？

範例：
• "Michael最近500筆對話" → 要500筆
• "蚵仔煎都聊了什麼？" → 預設50筆，簡單總結
• "你覺得Andrew講的有道理嗎？" → 要分析，不只是列表
• "給我看所有蚵大的發言" → 全部

用 JSON 回答：
{
  "targetUser": "目標用戶名稱",
  "recordLimit": 數量或"all",
  "queryType": "summary"或"analysis",
  "isValidQuery": true/false,
  "analysisPrompt": "如果是分析類型，原始問題是什麼"
}`,
    
    params: ['userQuery']
  },

  // === 用戶映射類 ===
  USER_MAPPING: {
    template: `幫我建立群組用戶的昵稱對應表，讓大家更容易查詢：

用戶列表：
{userList}

請考慮：
• 昵稱和簡稱（Michael = Mike = 麥可）
• 去掉特殊符號（⚡Michael（台灣智能） = Michael）
• 常見縮寫（Andrew = Andy）
• 中英文對應

用 JSON 格式回答：
{
  "映射關鍵字": "完整顯示名稱",
  "另一個關鍵字": "對應的完整顯示名稱"
}

例如：
{
  "michael": "⚡Michael（台灣智能）",
  "mike": "⚡Michael（台灣智能）",
  "蚵仔煎": "蚵仔煎/雲林AI第一人",
  "蚵大": "蚵仔煎/雲林AI第一人"
}`,
    
    params: ['userList']
  },

  // === 用戶名稱解析 ===
  USER_NAME_RESOLVE: {
    template: `用戶想查詢「{queryUserName}」，幫我找出是指哪個群組成員：

可用的群組用戶：
{availableUsers}

請判斷用戶想查詢哪一個具體用戶。考慮昵稱、簡稱、特殊符號等。

如果找到匹配用戶，請只回答完整的顯示名稱。
如果沒有匹配用戶，請回答 "NONE"。

匹配用戶：`,
    
    params: ['queryUserName', 'availableUsers']
  },

  // === 回答生成類 ===
  
  // 簡潔查詢回答（針對「有提到...嗎？」類型問題）
  SIMPLE_QUERY_RESPONSE: {
    template: `用戶問：「{originalQuery}」

這是關於「{targetUser}」的發言記錄：
{messageContext}

這是個簡單的查詢問題，用戶只想要直接的答案。

請用自然聊天的口氣回答：
• 如果有提到，直接說「有」，然後簡單說在哪裡提到
• 如果沒提到，直接說「沒有」或「我沒看到」
• 不要寫長篇分析報告
• 語氣要像朋友聊天一樣自然

回答：`,
    
    params: ['originalQuery', 'targetUser', 'messageContext']
  },

  // 用戶發言總結
  USER_SUMMARY: {
    template: `幫我總結「{targetUser}」最近的發言：

發言記錄（共{totalCount}筆）：
{messageContext}

用自然的口氣總結：
• 主要在聊什麼話題
• 有什麼特別的觀點或想法
• 發言風格如何

不要寫得像報告，就像朋友之間聊天時說「他最近都在聊...」那樣自然。
控制在200字左右。

總結：`,
    
    params: ['targetUser', 'totalCount', 'messageContext']
  },

  // 用戶發言分析（深度問題）
  USER_ANALYSIS: {
    template: `用戶問：「{analysisPrompt}」

這是「{targetUser}」的發言記錄：
{messageContext}

請用聊天的口氣回答用戶的問題：
• 直接回應用戶問的問題
• 可以引用具體的發言內容
• 保持客觀但不要太正式
• 就像朋友間討論一樣自然

回答：`,
    
    params: ['analysisPrompt', 'targetUser', 'messageContext']
  },

  // === 內容提取類 ===
  
  // 筆記內容提取
  NOTE_EXTRACTION: {
    template: `從用戶的話中提取要記錄的內容：

用戶說：「{message}」

請只回傳需要記錄的核心內容，不要包含「記錄」、「記住」這些動作詞。
如果不確定要記什麼，就回傳空字符串。

要記錄的內容：`,
    
    params: ['message']
  },

  // 筆記確認回應
  NOTE_CONFIRMATION: {
    template: `用戶想記錄：「{noteContent}」

請用自然聊天的口氣確認已經記錄了：
• 語氣要友善自然，像朋友間對話
• 確認記錄的內容
• 可以加點關心或建議，但不要太多
• 50字以內

確認回應：`,
    
    params: ['noteContent']
  },

  // === 閒聊回應類 ===
  CASUAL_CHAT: {
    template: `用戶說：「{originalMessage}」

請用自然聊天的口氣回應：
• 語氣要友善親切，像朋友聊天
• 不要太正式或有 AI 味
• 可以適當表達關心
• 簡潔一點，30字以內

回應：`,
    
    params: ['originalMessage']
  },

  // === 🔧 系統指令類 ===

  // 系統指令識別
  SYSTEM_COMMAND_ANALYSIS: {
    template: `分析用戶的系統指令請求：

用戶說：「{userMessage}」

判斷用戶想要執行什麼系統功能：
🔧 測試 - 系統狀態檢查
📚 範例 - 功能使用範例
❓ 幫助 - 完整功能說明
📊 狀態 - 系統健康檢查

請用 JSON 回答：
{
  "command_type": "test|examples|help|status",
  "confidence": 信心度(0-100),
  "user_intent": "用戶想要什麼"
}`,

    params: ['userMessage']
  },

  // 模型範例生成
  MODEL_EXAMPLES_RESPONSE: {
    template: `生成 AI 功能使用範例指南：

用戶想了解：{requestType}

請生成包含以下功能的使用範例：
🔊 語音功能（TTS）
🎨 圖片生成
📊 語義搜索
⚡ 快速處理
📝 筆記功能
🧠 對話記憶

每個功能提供 2-3 個具體範例，語氣要自然友善。

範例指南：`,

    params: ['requestType']
  },

  // === 幫助說明類 ===
  HELP_RESPONSE: {
    template: `為智能 LINE Bot 生成使用說明：

環境：{environmentType}
AI 模型：{modelInfo}

用自然的口氣說明：
• 這是個 AI 助手，不需要記命令，直接說話就行
• 主要功能：記筆記、分析檔案、查聊天記錄{groupFeatures}
• 🔊 語音功能：說「念出來」或「語音播放」可以把文字轉成語音，說「跟我聊聊」可以語音對話
• 🎨 圖像生成：說「畫一張圖」或「生成圖片」可以創作圖像
• 📊 智能搜索：說「找相關檔案」可以進行語義搜索
• ⚡ 快速處理：說「批量處理」或「快速整理」可以高效完成任務
• 可以直接上傳檔案讓 AI 分析
• 就像跟朋友聊天一樣，AI 會懂你的意思

語氣要輕鬆友善，不要寫得像說明書。

使用說明：`,

    params: ['environmentType', 'modelInfo', 'groupFeatures']
  }
};

/**
 * 🎯 統一的提示詞調用接口
 * 取代分散在各處的提示詞硬編碼
 */
function callAIWithPrompt(promptType, context = {}, parameters = {}) {
  try {
    if (!PROMPTS[promptType]) {
      throw new Error(`未知的提示詞類型: ${promptType}`);
    }
    
    const promptConfig = PROMPTS[promptType];
    const prompt = buildPrompt(promptConfig.template, context, parameters);
    
    console.log(`🤖 使用提示詞: ${promptType}`);
    
    // 🚀 根據提示詞類型智能選擇模型
    const taskHint = getTaskHintFromPromptType(promptType);
    return callGemini(prompt, 'general', null, taskHint);
    
  } catch (error) {
    console.error(`提示詞調用失敗 (${promptType}):`, error);
    throw error;
  }
}

/**
 * 🧠 根據提示詞類型獲取任務提示
 * 讓系統自動選擇最適合的模型
 */
function getTaskHintFromPromptType(promptType) {
  const taskMapping = {
    // 語音相關
    'TTS_MODE_ANALYSIS': 'general',
    'TTS_TEXT_EXTRACTION': 'general',
    
    // 圖像相關
    'IMAGE_PROMPT_EXTRACTION': 'general',
    'IMAGE_PROMPT_ENHANCEMENT': 'general',
    
    // 語義搜索相關
    'SEMANTIC_SEARCH_RESPONSE': 'fast_processing', // 使用快速模型回應搜索結果
    
    // 智能引導相關 - 新增
    'UNIFIED_GUIDANCE': 'fast_processing', // 智能引導使用快速模型
    
    // 一般處理
    'INTENT_ANALYSIS': 'general',
    'CASUAL_CHAT': 'general',
    'NOTE_CONFIRMATION': 'fast_processing', // 簡單確認使用快速模型
    'HELP_RESPONSE': 'general'
  };
  
  return taskMapping[promptType] || 'general';
}

/**
 * 🔧 提示詞模板建構器
 * 支持參數替換和上下文注入
 */
function buildPrompt(template, context = {}, parameters = {}) {
  let prompt = template;
  
  // 替換模板中的參數
  const allParams = { ...context, ...parameters };
  
  for (const [key, value] of Object.entries(allParams)) {
    const placeholder = `{${key}}`;
    prompt = prompt.replace(new RegExp(placeholder.replace(/[{}]/g, '\\$&'), 'g'), value || '');
  }
  
  return prompt;
}

/**
 * 🎯 智能回答路由器
 * 根據問題類型選擇合適的回答策略
 */
function getResponseStrategy(userQuery, queryType = 'unknown') {
  // 簡單查詢（是否問題）
  if (/有提到|有說|有講|提過|說過/.test(userQuery) && /嗎\?*$/.test(userQuery)) {
    return 'SIMPLE_QUERY_RESPONSE';
  }
  
  // 分析類問題
  if (/你覺得|怎麼看|有道理|分析|評價/.test(userQuery)) {
    return 'USER_ANALYSIS';
  }
  
  // 預設總結
  return 'USER_SUMMARY';
}

// ===== 🧪 測試函數 - v2.1 擴展版 =====

/**
 * 🧪 測試智能引導功能 - 新增
 */
function testUnifiedGuidance_debug() {
  console.log('🧪 === 測試智能引導功能 ===');

  try {
    console.log('\n🤔 測試統一引導模板');
    const guidanceResponse = callAIWithPrompt('UNIFIED_GUIDANCE', {});
    console.log('智能引導結果:', guidanceResponse);

    console.log('\n✅ 智能引導功能測試完成！');

  } catch (error) {
    console.error('❌ 智能引導功能測試失敗:', error);
  }
}

/**
 * 🧪 測試雙重檢測整合 - 新增
 */
function testDualDetectionIntegration_debug() {
  console.log('🧪 === 測試雙重檢測整合 ===');

  const testScenarios = [
    {
      message: '測試',
      expected: '預檢測捕獲',
      description: '明確系統命令'
    },
    {
      message: 'help',
      expected: '預檢測捕獲',
      description: '明確系統命令'
    },
    {
      message: '嗯',
      expected: 'AI分析 + 智能引導',
      description: '模糊輸入，應觸發引導'
    },
    {
      message: '我想要',
      expected: 'AI分析 + 智能引導',
      description: '不完整請求，應觸發引導'
    },
    {
      message: '記住明天開會',
      expected: 'AI分析 + 正常處理',
      description: '明確功能需求'
    }
  ];

  testScenarios.forEach((scenario, index) => {
    console.log(`\n場景 ${index + 1}: "${scenario.message}"`);
    console.log(`預期行為: ${scenario.expected}`);
    console.log(`測試描述: ${scenario.description}`);

    try {
      // 模擬預檢測
      const cleanInput = scenario.message.replace(/^[!！]/, '').trim().toLowerCase();
      const EXPLICIT_SYSTEM_COMMANDS = {
        'test': 'system_test',
        'testing': 'system_test', 
        '測試': 'system_test',
        'help': 'system_help',
        '幫助': 'system_help',
        '功能': 'system_help',
        'status': 'system_status',
        '狀態': 'system_status',
        'examples': 'model_examples',
        'example': 'model_examples',
        '範例': 'model_examples',
        '示範': 'model_examples'
      };

      if (EXPLICIT_SYSTEM_COMMANDS[cleanInput]) {
        console.log(`🛡️ 結果: 預檢測捕獲 - ${EXPLICIT_SYSTEM_COMMANDS[cleanInput]}`);
      } else {
        // 模擬 AI 意圖分析
        const mockIntent = {
          primary_intent: 'casual_chat',
          confidence: scenario.message.length < 5 ? 30 : 75, // 簡單模擬
          natural_language_summary: `用戶說了: ${scenario.message}`
        };

        if (mockIntent.confidence < 70) {
          console.log(`🤔 結果: AI分析 (信心度: ${mockIntent.confidence}%) + 觸發智能引導`);
        } else {
          console.log(`🎯 結果: AI分析 (信心度: ${mockIntent.confidence}%) + 正常處理`);
        }
      }

    } catch (error) {
      console.log(`❌ 場景 ${index + 1} 測試失敗: ${error.message}`);
    }
  });

  console.log('\n✅ 雙重檢測整合測試完成！');
}

/**
 * 🧪 測試新增功能的提示詞
 */
function testEnhancedPromptManager_debug() {
  console.log('🧪 === 測試新增功能的提示詞系統 ===');

  try {
    // 測試 1: TTS 模式分析
    console.log('\n🔊 測試 1: TTS 模式分析');
    const ttsAnalysis = callAIWithPrompt('TTS_MODE_ANALYSIS', {
      message: '你說今天天氣很好'
    });
    console.log('TTS 模式分析結果:', ttsAnalysis);

    // 測試 2: 圖像提示詞增強
    console.log('\n🎨 測試 2: 圖像提示詞增強');
    const imageEnhancement = callAIWithPrompt('IMAGE_PROMPT_ENHANCEMENT', {
      originalPrompt: '一隻可愛的小貓'
    });
    console.log('圖像提示詞增強結果:', imageEnhancement);

    // 測試 3: 語義搜索回應
    console.log('\n📊 測試 3: 語義搜索回應');
    const searchResponse = callAIWithPrompt('SEMANTIC_SEARCH_RESPONSE', {
      query: '找相關的 AI 教育檔案',
      resultCount: 5,
      topResults: 'AI學習指南.pdf, 機器學習教程.docx, 深度學習筆記.md'
    });
    console.log('語義搜索回應結果:', searchResponse);

    // 測試 4: 意圖分析（包含新意圖）
    console.log('\n🧠 測試 4: 意圖分析（新增功能）');
    const enhancedIntent = callAIWithPrompt('INTENT_ANALYSIS', {
      userMessage: '幫我快速處理這些檔案',
      sourceType: '個人對話'
    });
    console.log('增強意圖分析結果:', enhancedIntent);

    console.log('\n✅ 新增功能提示詞測試完成！');

  } catch (error) {
    console.error('❌ 新增功能提示詞測試失敗:', error);
  }
}

/**
 * 🧪 測試提示詞系統
 */
function testPromptManager_debug() {
  console.log('🧪 === 測試統一提示詞管理系統 ===');

  try {
    // 測試 1: 簡潔查詢回答（核心問題）
    console.log('\n🎯 測試 1: 簡潔查詢回答');
    const simpleQuery = callAIWithPrompt('SIMPLE_QUERY_RESPONSE', {
      originalQuery: 'Michael有提到吃的嗎？',
      targetUser: 'Michael',
      messageContext: '1. [2小時前] 今天中午吃什麼好呢？\n2. [1小時前] 我想吃火鍋\n3. [30分鐘前] 大家一起去吃飯吧'
    });
    console.log('簡潔查詢結果:', simpleQuery);

    // 測試 2: 意圖分析
    console.log('\n🎯 測試 2: 意圖分析');
    const intentResult = callAIWithPrompt('INTENT_ANALYSIS', {
      userMessage: '蚵仔煎都聊了什麼？',
      sourceType: '群組聊天'
    });
    console.log('意圖分析結果:', intentResult);

    // 測試 3: 閒聊回應
    console.log('\n🎯 測試 3: 閒聊回應');
    const casualResponse = callAIWithPrompt('CASUAL_CHAT', {
      originalMessage: '你好！'
    });
    console.log('閒聊回應結果:', casualResponse);

    // 測試 4: 筆記確認
    console.log('\n🎯 測試 4: 筆記確認');
    const noteConfirm = callAIWithPrompt('NOTE_CONFIRMATION', {
      noteContent: '明天下午3點開會'
    });
    console.log('筆記確認結果:', noteConfirm);

    console.log('\n✅ 提示詞系統測試完成！');

  } catch (error) {
    console.error('❌ 提示詞系統測試失敗:', error);
  }
}

/**
 * 🧪 測試智能回答策略
 */
function testResponseStrategy_debug() {
  console.log('🧪 === 測試智能回答策略 ===');

  const testQueries = [
    'Michael有提到吃的嗎？',           // 應該是 SIMPLE_QUERY_RESPONSE
    '你覺得蚵仔煎講的有道理嗎？',      // 應該是 USER_ANALYSIS
    '總結一下Andrew最近的發言',        // 應該是 USER_SUMMARY
    'Lisa有說過關於工作的事嗎？'       // 應該是 SIMPLE_QUERY_RESPONSE
  ];

  testQueries.forEach((query, index) => {
    const strategy = getResponseStrategy(query);
    console.log(`${index + 1}. "${query}" → ${strategy}`);
  });

  console.log('\n✅ 回答策略測試完成！');
}

/**
 * 🧪 測試任務提示映射 - v2.1 更新版
 */
function testTaskHintMapping_debug() {
  console.log('🧪 === 測試任務提示映射 v2.1 ===');

  const promptTypes = [
    'TTS_MODE_ANALYSIS',
    'IMAGE_PROMPT_ENHANCEMENT',
    'SEMANTIC_SEARCH_RESPONSE',
    'NOTE_CONFIRMATION',
    'INTENT_ANALYSIS',
    'UNIFIED_GUIDANCE'  // 新增
  ];

  promptTypes.forEach(promptType => {
    const taskHint = getTaskHintFromPromptType(promptType);
    const selectedModel = getModelForTask ? getModelForTask(taskHint) : taskHint;
    
    console.log(`📋 ${promptType}:`);
    console.log(`   任務提示: ${taskHint}`);
    console.log(`   選擇模型: ${selectedModel}`);
    console.log('');
  });

  console.log('✅ 任務提示映射測試完成！');
}

/**
 * 🧪 測試完整的 v2.1 功能整合
 */
function testV21Integration_debug() {
  console.log('🧪 === 測試 v2.1 完整功能整合 ===');

  try {
    console.log('\n1. 測試智能引導模板');
    testUnifiedGuidance_debug();

    console.log('\n2. 測試雙重檢測整合');
    testDualDetectionIntegration_debug();

    console.log('\n3. 測試新版任務映射');
    testTaskHintMapping_debug();

    console.log('\n🎉 v2.1 功能整合測試完成！');
    console.log('📋 新功能清單：');
    console.log('   • ✅ 智能引導模板 (UNIFIED_GUIDANCE)');
    console.log('   • ✅ 雙重檢測機制整合');
    console.log('   • ✅ 更新的任務提示映射');
    console.log('   • ✅ 向下兼容所有現有功能');

  } catch (error) {
    console.error('❌ v2.1 功能整合測試失敗:', error);
  }
}
