// 🧪 簡單的意圖檢測測試

function testSimpleIntent() {
  console.log('🧪 === 簡單意圖檢測測試 ===');
  
  try {
    // 測試語音意圖
    console.log('\n🔊 測試語音意圖...');
    const ttsIntent = analyzeUserIntentWithAI('你說 你好嗎', 'group', 'test-user');
    console.log('語音意圖結果:', ttsIntent);
    
    // 測試圖片意圖
    console.log('\n🎨 測試圖片意圖...');
    const imageIntent = analyzeUserIntentWithAI('能給我炒高麗菜的圖片嗎', 'group', 'test-user');
    console.log('圖片意圖結果:', imageIntent);
    
    // 測試備用檢測
    console.log('\n🔧 測試備用檢測...');
    const fallbackTTS = generateFallbackIntent('你說 你好嗎', 'group');
    console.log('備用語音檢測:', fallbackTTS);
    
    const fallbackImage = generateFallbackIntent('能給我炒高麗菜的圖片嗎', 'group');
    console.log('備用圖片檢測:', fallbackImage);
    
  } catch (error) {
    console.error('測試失敗:', error);
  }
}

function testPromptDirectly() {
  console.log('🧪 === 直接測試提示詞 ===');
  
  try {
    const response = callAIWithPrompt('INTENT_ANALYSIS', {
      userMessage: '你說 你好嗎',
      sourceType: '群組聊天'
    });
    
    console.log('AI 原始回應:', response);
    
    // 嘗試解析 JSON
    const jsonMatch = response.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      try {
        const parsed = JSON.parse(jsonMatch[0]);
        console.log('解析成功:', parsed);
        console.log('檢測到的意圖:', parsed.primary_intent);
      } catch (parseError) {
        console.error('JSON 解析失敗:', parseError);
      }
    } else {
      console.log('❌ 沒有找到 JSON 格式');
    }
    
  } catch (error) {
    console.error('直接測試失敗:', error);
  }
}
