/**
 * 🔧 配置模板文件
 * 複製此文件為 config.js 並填入實際值
 * 
 * 注意：config.js 已被 .gitignore 排除，不會被提交到版本控制
 */

// 💡 在實際使用中，這些配置會從 Google Sheets 的 APIKEY 工作表讀取
// 此文件僅作為參考，說明需要哪些配置項目

const CONFIG_TEMPLATE = {
  // 🔗 Google Services
  GOOGLE_APPS_SCRIPT_ID: 'YOUR_GOOGLE_APPS_SCRIPT_PROJECT_ID_HERE',
  GOOGLE_DRIVE_FOLDER_ID: 'YOUR_GOOGLE_DRIVE_FOLDER_ID_HERE',
  GOOGLE_SHEETS_ID: 'YOUR_GOOGLE_SHEETS_ID_HERE',
  
  // 📱 LINE Bot API
  LINE_CHANNEL_ACCESS_TOKEN: 'YOUR_LINE_CHANNEL_ACCESS_TOKEN_HERE',
  LINE_CHANNEL_SECRET: 'YOUR_LINE_CHANNEL_SECRET_HERE',
  
  // 🤖 AI Services
  GEMINI_API_KEY: 'YOUR_GEMINI_API_KEY_HERE',
  
  // 🔍 Google Search (可選)
  GOOGLE_SEARCH_API_KEY: 'YOUR_GOOGLE_SEARCH_API_KEY_HERE',
  GOOGLE_SEARCH_ENGINE_ID: 'YOUR_CUSTOM_SEARCH_ENGINE_ID_HERE',
  
  // 📺 YouTube API (可選)
  YOUTUBE_DATA_API_KEY: 'YOUR_YOUTUBE_DATA_API_KEY_HERE',
  
  // 📱 Threads API (可選)
  THREADS_USER_ID: 'YOUR_THREADS_USER_ID_HERE',
  THREADS_ACCESS_TOKEN: 'YOUR_THREADS_ACCESS_TOKEN_HERE',
  
  // ☁️ Cloudinary API (可選)
  CLOUDINARY_CLOUD_NAME: 'YOUR_CLOUDINARY_CLOUD_NAME_HERE',
  CLOUDINARY_API_KEY: 'YOUR_CLOUDINARY_API_KEY_HERE',
  CLOUDINARY_API_SECRET: 'YOUR_CLOUDINARY_API_SECRET_HERE'
};

/**
 * 📚 設定指南
 * 
 * 1. LINE Bot API:
 *    - 前往 https://developers.line.biz/
 *    - 建立 Provider 和 Messaging API Channel
 *    - 取得 Channel Access Token 和 Channel Secret
 * 
 * 2. Gemini API:
 *    - 前往 https://aistudio.google.com/
 *    - 登入 Google 帳號並建立 API Key
 * 
 * 3. Google Drive:
 *    - 建立一個資料夾用於儲存上傳檔案
 *    - 複製資料夾 URL 中的 ID 部分
 * 
 * 4. Google Sheets:
 *    - 建立一個試算表作為資料庫
 *    - 複製試算表 URL 中的 ID 部分
 * 
 * 5. Google Apps Script:
 *    - 前往 https://script.google.com/
 *    - 建立新專案並複製專案 ID
 * 
 * 6. 可選 APIs:
 *    - Google Search: Google Cloud Console 中啟用 Custom Search API
 *    - YouTube: Google Cloud Console 中啟用 YouTube Data API v3
 *    - Threads: Meta for Developers 中建立應用程式
 *    - Cloudinary: https://cloudinary.com/ 註冊帳號
 */

// 🚀 部署檢查清單
const DEPLOYMENT_CHECKLIST = [
  '✅ 已建立 Google Apps Script 專案',
  '✅ 已建立 Google Sheets 資料庫',  
  '✅ 已建立 Google Drive 資料夾',
  '✅ 已設定 LINE Bot Channel',
  '✅ 已取得 Gemini API Key',
  '✅ 已在 APIKEY 工作表填入所有必要設定',
  '✅ 已部署為 Web App',
  '✅ 已設定 LINE Webhook URL',
  '✅ 已測試 Bot 功能正常'
];

/**
 * ⚠️ 安全注意事項
 * 
 * - 絕對不要將真實的 API Keys 提交到版本控制
 * - 所有敏感資訊都應該儲存在 Google Sheets 的 APIKEY 工作表中
 * - 定期更換 API Keys 以確保安全性
 * - 不要在公開場所分享包含 API Keys 的截圖或代碼
 */

// 導出配置（在實際的 config.js 中使用）
// module.exports = CONFIG_TEMPLATE;
