// == 系統診斷和修復腳本 ==
// 添加到 test_functions.gs 或創建新的 Diagnostics.gs 檔案

/**
 * 🔧 主要診斷函數 - 一鍵檢查所有系統問題
 * 在 GAS 編輯器中執行此函數來診斷問題
 */
function runSystemDiagnostics() {
  console.log('🔍 開始系統全面診斷...');

  const diagnosticReport = {
    timestamp: new Date(),
    overall: 'unknown',
    modules: {},
    fixes: [],
    errors: [],
    recommendations: []
  };

  try {
    // 1. 檢查基本配置
    console.log('📋 檢查基本配置...');
    diagnosticReport.modules.config = diagnoseBasisConfig();

    // 2. 檢查工作表結構
    console.log('📊 檢查工作表結構...');
    diagnosticReport.modules.sheets = diagnoseSheetStructure();

    // 3. 檢查函數依賴
    console.log('🔗 檢查函數依賴...');
    diagnosticReport.modules.functions = diagnoseFunctionDependencies();

    // 4. 檢查記憶系統
    console.log('🧠 檢查記憶系統...');
    diagnosticReport.modules.memory = diagnoseMemorySystem();

    // 5. 測試 API 連接
    console.log('🌐 測試 API 連接...');
    diagnosticReport.modules.apis = diagnoseAPIConnections();

    // 6. 綜合評估
    diagnosticReport.overall = evaluateOverallHealth(diagnosticReport.modules);

    // 7. 生成修復建議
    diagnosticReport.recommendations = generateRepairRecommendations(diagnosticReport.modules);

    console.log('🎯 診斷完成！結果:', diagnosticReport);

    // 8. 生成報告
    const reportText = generateDiagnosticReport(diagnosticReport);
    console.log('\n📄 詳細診斷報告:');
    console.log(reportText);

    return diagnosticReport;

  } catch (error) {
    console.error('❌ 診斷過程發生錯誤:', error);
    diagnosticReport.overall = 'critical_error';
    diagnosticReport.errors.push(error.toString());
    return diagnosticReport;
  }
}

/**
 * 🔧 自動修復函數 - 嘗試修復常見問題
 */
function autoFixCommonIssues() {
  console.log('🔧 開始自動修復...');

  const fixResults = {
    attempted: [],
    successful: [],
    failed: [],
    skipped: []
  };

  try {
    // 1. 嘗試初始化缺失的工作表
    console.log('📊 修復工作表結構...');
    try {
      initializeSheets();
      fixResults.successful.push('工作表初始化');
      fixResults.attempted.push('工作表初始化');
    } catch (error) {
      console.error('工作表初始化失敗:', error);
      fixResults.failed.push(`工作表初始化: ${error.message}`);
      fixResults.attempted.push('工作表初始化');
    }

    // 2. 檢查並修復 APIKEY 工作表
    console.log('🔑 修復 APIKEY 工作表...');
    try {
      const apikeySheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName('APIKEY');
      if (!apikeySheet || apikeySheet.getRange('A1').getValue() === '') {
        if (apikeySheet) {
          setupAPIKEYSheet(apikeySheet);
          fixResults.successful.push('APIKEY 工作表結構修復');
        } else {
          fixResults.failed.push('APIKEY 工作表不存在，需要手動創建');
        }
      } else {
        fixResults.skipped.push('APIKEY 工作表已正確設置');
      }
      fixResults.attempted.push('APIKEY 工作表');
    } catch (error) {
      console.error('APIKEY 工作表修復失敗:', error);
      fixResults.failed.push(`APIKEY 工作表: ${error.message}`);
      fixResults.attempted.push('APIKEY 工作表');
    }

    // 3. 測試記憶系統初始化
    console.log('🧠 修復記憶系統...');
    try {
      const memorySheets = ['用戶對話歷史', '檔案記憶庫', '分析任務佇列'];
      let memoryFixed = 0;

      memorySheets.forEach(sheetName => {
        const sheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(sheetName);
        if (sheet && sheet.getLastRow() === 0) {
          setupMemorySheetHeaders(sheet, sheetName);
          memoryFixed++;
        }
      });

      if (memoryFixed > 0) {
        fixResults.successful.push(`記憶系統：修復了 ${memoryFixed} 個工作表標題`);
      } else {
        fixResults.skipped.push('記憶系統工作表已正確設置');
      }
      fixResults.attempted.push('記憶系統');
    } catch (error) {
      console.error('記憶系統修復失敗:', error);
      fixResults.failed.push(`記憶系統: ${error.message}`);
      fixResults.attempted.push('記憶系統');
    }

    // 4. 清理舊數據（如果工作表太大）
    console.log('🧹 檢查數據清理需求...');
    try {
      const cleanupResult = cleanupWorksheets();
      if (cleanupResult.includes('清理完成')) {
        fixResults.successful.push('數據清理');
      } else {
        fixResults.skipped.push('無需數據清理');
      }
      fixResults.attempted.push('數據清理');
    } catch (error) {
      console.error('數據清理失敗:', error);
      fixResults.failed.push(`數據清理: ${error.message}`);
      fixResults.attempted.push('數據清理');
    }

    console.log('🎯 自動修復完成！結果:', fixResults);
    return fixResults;

  } catch (error) {
    console.error('❌ 自動修復過程發生錯誤:', error);
    fixResults.failed.push(`整體修復失敗: ${error.message}`);
    return fixResults;
  }
}

// 診斷輔助函數
function diagnoseBasisConfig() {
  try {
    const config = getConfig();
    return {
      status: 'healthy',
      details: {
        lineToken: !!config.lineChannelAccessToken,
        lineSecret: !!config.lineChannelSecret,
        geminiKey: !!config.geminiApiKey,
        driveFolder: !!config.folderId
      }
    };
  } catch (error) {
    return {
      status: 'error',
      error: error.message,
      details: {}
    };
  }
}

function diagnoseSheetStructure() {
  try {
    const health = checkSheetsHealthFixed();
    return {
      status: health.missingSheets.length === 0 ? 'healthy' : 'warning',
      details: health,
      missing: health.missingSheets
    };
  } catch (error) {
    return {
      status: 'error',
      error: error.message,
      details: {},
      missing: []
    };
  }
}

function diagnoseFunctionDependencies() {
  const functions = [
    'getConfig',
    'initializeSheets',
    'callGemini',
    'replyMessage',
    'logActivity',
    'handleGoogleDriveLink',
    'processSpecialFileTypes',
    'generateMediaProcessingReport'
  ];

  const results = {
    status: 'healthy',
    available: [],
    missing: []
  };

  functions.forEach(funcName => {
    try {
      if (typeof this[funcName] === 'function') {
        results.available.push(funcName);
      } else {
        results.missing.push(funcName);
        results.status = 'warning';
      }
    } catch (error) {
      results.missing.push(`${funcName}: ${error.message}`);
      results.status = 'error';
    }
  });

  return results;
}

function diagnoseMemorySystem() {
  try {
    const health = checkMemorySystemHealthFixed();
    return {
      status: health.status,
      details: health
    };
  } catch (error) {
    return {
      status: 'error',
      error: error.message,
      details: {}
    };
  }
}

function diagnoseAPIConnections() {
  const results = {
    status: 'healthy',
    apis: {}
  };

  try {
    const config = getConfig();

    // 測試 Gemini API
    if (config.geminiApiKey) {
      try {
        const testResponse = callGemini('測試', 'general');
        results.apis.gemini = testResponse ? 'healthy' : 'warning';
      } catch (error) {
        results.apis.gemini = 'error';
        results.status = 'warning';
      }
    } else {
      results.apis.gemini = 'not_configured';
    }

    // 檢查 LINE API 配置
    results.apis.line = (config.lineChannelAccessToken && config.lineChannelSecret) ? 'configured' : 'not_configured';

    // 檢查 Google Search API 配置
    results.apis.googleSearch = (config.googleSearchApiKey && config.googleSearchEngineId) ? 'configured' : 'not_configured';

  } catch (error) {
    results.status = 'error';
    results.error = error.message;
  }

  return results;
}

function evaluateOverallHealth(modules) {
  const statuses = Object.values(modules).map(m => m.status);

  if (statuses.includes('error')) {
    return 'error';
  } else if (statuses.includes('warning')) {
    return 'warning';
  } else {
    return 'healthy';
  }
}

function generateRepairRecommendations(modules) {
  const recommendations = [];

  if (modules.config && modules.config.status === 'error') {
    recommendations.push('🔑 請檢查 APIKEY 工作表是否存在且格式正確');
  }

  if (modules.sheets && modules.sheets.missing.length > 0) {
    recommendations.push(`📊 請執行 initializeSheets() 創建缺失的工作表: ${modules.sheets.missing.join(', ')}`);
  }

  if (modules.functions && modules.functions.missing.length > 0) {
    recommendations.push(`🔗 缺失函數，請檢查模組檔案: ${modules.functions.missing.join(', ')}`);
  }

  if (modules.memory && modules.memory.status !== 'healthy') {
    recommendations.push('🧠 記憶系統需要修復，請檢查相關工作表');
  }

  if (modules.apis && modules.apis.status !== 'healthy') {
    recommendations.push('🌐 API 連接有問題，請檢查 API Key 設定');
  }

  if (recommendations.length === 0) {
    recommendations.push('✅ 系統狀態良好，無需特殊修復');
  }

  return recommendations;
}

function generateDiagnosticReport(diagnosticData) {
  let report = '🔍 系統診斷報告\n';
  report += `⏰ 檢查時間：${diagnosticData.timestamp.toLocaleString('zh-TW')}\n`;
  report += `🎯 整體狀態：${diagnosticData.overall === 'healthy' ? '✅ 健康' : diagnosticData.overall === 'warning' ? '⚠️ 警告' : '❌ 錯誤'}\n\n`;

  // 模組狀態
  report += '📋 模組檢查結果：\n';
  Object.entries(diagnosticData.modules).forEach(([module, data]) => {
    const statusIcon = data.status === 'healthy' ? '✅' : data.status === 'warning' ? '⚠️' : '❌';
    report += `${statusIcon} ${module}: ${data.status}\n`;

    if (data.error) {
      report += `   錯誤: ${data.error}\n`;
    }

    if (data.missing && data.missing.length > 0) {
      report += `   缺失: ${data.missing.join(', ')}\n`;
    }
  });

  // 修復建議
  if (diagnosticData.recommendations.length > 0) {
    report += '\n🔧 修復建議：\n';
    diagnosticData.recommendations.forEach((rec, index) => {
      report += `${index + 1}. ${rec}\n`;
    });
  }

  // 錯誤詳情
  if (diagnosticData.errors.length > 0) {
    report += '\n❌ 錯誤詳情：\n';
    diagnosticData.errors.forEach((error, index) => {
      report += `${index + 1}. ${error}\n`;
    });
  }

  return report;
}
