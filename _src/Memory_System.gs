// == 個人檔案記憶系統模組 ==
// 專門處理用戶對話歷史、檔案記憶庫、AI檔案查詢等功能
// 🤖 AI First 架構：100% AI驅動的意圖識別和時間解析

// 1. Fallback時間範圍配置（僅作為AI失敗時的備用方案）
const FALLBACK_TIME_RANGES = {
  // 12小時 = 基本單位 * 0.5
  time_12: {
    hours: 12,
    duration: 12 * 60 * 60 * 1000,
    keywords: ['剛才', '剛剛', '刚才', '刚刚', 'just now', 'recently']
  },
  // 24小時 = 基本單位 * 1
  time_24: {
    hours: 24,
    duration: 24 * 60 * 60 * 1000,
    keywords: ['今天', '今日', '上午', '下午', 'today', 'this morning', 'this afternoon']
  },
  // 36小時 = 基本單位 * 1.5
  time_36: {
    hours: 36,
    duration: 36 * 60 * 60 * 1000,
    keywords: ['最近', '之前', 'lately', 'recently', 'before', 'earlier']
  }
};

// 2. 設定記憶系統工作表標題行
function setupMemorySheetHeaders(sheet, sheetName) {
  let headers = [];
  
  switch(sheetName) {
    case '用戶對話歷史':
      headers = ['時間戳', '用戶ID', '訊息類型', '原始訊息', '處理結果', '關聯檔案ID', '來源類型'];
      break;
    case '檔案記憶庫':
      headers = ['時間戳', '用戶ID', '檔案ID', '檔案名稱', '檔案類型', '檔案大小', '簡短描述', '關鍵特徵', '檔案連結', '分析狀態'];
      break;
    case '分析任務佇列':
      headers = ['時間戳', '用戶ID', '檔案ID', '分析類型', '任務狀態', '分析結果', '用戶查詢'];
      break;
    default:
      return; // 不是記憶系統工作表，跳過
  }
  
  if (headers.length > 0) {
    sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
    sheet.getRange(1, 1, 1, headers.length).setFontWeight('bold');
    sheet.getRange(1, 1, 1, headers.length).setBackground('#e1f5fe');
    console.log(`已設定 ${sheetName} 工作表標題行`);
  }
}

// 3. 記憶中間件 - 統一處理所有記憶相關操作
function memoryMiddleware(operation, data) {
  try {
    switch(operation) {
      case 'record_conversation':
        return recordConversation(data.userId, data.originalMessage, data.botResponse, data.relatedFileId, data.sourceType);
      case 'record_file':
        return recordFileMemory(data.userId, data.fileId, data.fileName, data.fileType, data.fileSize, data.description, data.features, data.fileUrl, data.sourceType);
      case 'find_file':
        return handleFileReference(data.userId, data.query, data.sourceType);
      case 'cleanup_history':
        return cleanupUserHistory(data.userId, data.maxRecords || 1000);
      default:
        console.log(`未知的記憶操作: ${operation}`);
        return null;
    }
  } catch (error) {
    console.error(`記憶中間件錯誤 (${operation}):`, error);
    return null;
  }
}

// 4. 記錄用戶對話歷史
function recordConversation(userId, originalMessage, botResponse, relatedFileId = null, sourceType = 'user') {
  try {
    const sheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName('用戶對話歷史');
    if (sheet) {
      const timestamp = new Date();
      sheet.appendRow([
        timestamp,           // 時間戳
        userId,             // 用戶ID
        'text',             // 訊息類型
        originalMessage,    // 原始訊息
        botResponse,        // 處理結果
        relatedFileId || '', // 關聯檔案ID
        sourceType          // 來源類型
      ]);
      
      // 保持記錄數量在合理範圍內（每個用戶最多保留1000條記錄）
      cleanupUserHistory(userId, 1000);
      return true;
    }
    return false;
  } catch (error) {
    console.error('記錄對話歷史錯誤:', error);
    return false;
  }
}

// 5. 記錄檔案記憶
function recordFileMemory(userId, fileId, fileName, fileType, fileSize, description, features, fileUrl, sourceType = 'user') {
  try {
    const sheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName('檔案記憶庫');
    if (sheet) {
      const timestamp = new Date();
      sheet.appendRow([
        timestamp,          // 時間戳
        userId,            // 用戶ID
        fileId,            // 檔案ID  
        fileName,          // 檔案名稱
        fileType,          // 檔案類型
        fileSize,          // 檔案大小
        description,       // 簡短描述
        features,          // 關鍵特徵
        fileUrl,           // 檔案連結
        '已儲存'           // 分析狀態
      ]);
      
      console.log(`已記錄檔案記憶: ${fileName} (用戶: ${userId})`);
      return true;
    }
    return false;
  } catch (error) {
    console.error('記錄檔案記憶錯誤:', error);
    return false;
  }
}

// 6. 清理用戶歷史記錄（永久保留策略，但限制搜尋範圍）
function cleanupUserHistory(userId, maxRecords = 1000) {
  try {
    const sheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName('用戶對話歷史');
    if (sheet) {
      const data = sheet.getDataRange().getValues();
      const userRecords = data.filter(row => row[1] === userId); // 篩選該用戶的記錄
      
      if (userRecords.length > maxRecords) {
        // 需要清理舊記錄
        const recordsToDelete = userRecords.length - maxRecords;
        let deletedCount = 0;
        
        // 從第2行開始檢查（第1行是標題）
        for (let i = 2; i <= sheet.getLastRow() && deletedCount < recordsToDelete; i++) {
          if (sheet.getRange(i, 2).getValue() === userId) {
            sheet.deleteRow(i);
            deletedCount++;
            i--; // 因為刪除了一行，索引需要調整
          }
        }
        
        console.log(`已清理用戶 ${userId} 的 ${deletedCount} 條舊記錄`);
      }
    }
    return true;
  } catch (error) {
    console.error('清理用戶歷史記錄錯誤:', error);
    return false;
  }
}

// 7. 🤖 AI驅動的時間範圍解析（取代傳統關鍵字匹配）
function parseTimeRange(query) {
  const config = getConfig();
  
  // 如果沒有API Key，直接使用fallback
  if (!config.geminiApiKey) {
    console.log('沒有Gemini API Key，使用fallback時間解析');
    return parseTimeRangeFallback(query);
  }
  
  const prompt = `分析用戶查詢中的時間範圍意圖：

用戶查詢：「${query}」

請判斷用戶想查詢的時間範圍，以JSON格式回答：
{
  "timeRange": "12h/24h/36h/72h/week",
  "confidence": 0-100,
  "reasoning": "判斷原因",
  "keywords": ["識別到的時間詞彙"]
}

時間範圍說明：
- 12h: 最近12小時（剛才、剛剛、最近一下）
- 24h: 今天一整天（今天、今日、上午、下午）
- 36h: 最近1-2天（最近、昨天、前天）
- 72h: 最近幾天（幾天前、這幾天）
- week: 一週內（這週、本週、上週）

如果查詢中沒有明確時間指示，請選擇24h作為預設。`;

  try {
    const aiResponse = callGemini(prompt, 'general');
    const jsonMatch = aiResponse.match(/\{[\s\S]*\}/);
    
    if (jsonMatch) {
      const result = JSON.parse(jsonMatch[0]);
      
      // 只有當AI信心度足夠高時才使用AI結果
      if (result.confidence >= 60) {
        const durationMap = {
          '12h': 12 * 60 * 60 * 1000,
          '24h': 24 * 60 * 60 * 1000, 
          '36h': 36 * 60 * 60 * 1000,
          '72h': 72 * 60 * 60 * 1000,
          'week': 7 * 24 * 60 * 60 * 1000
        };
        
        const timeRange = result.timeRange || '24h';
        return {
          range: timeRange,
          duration: durationMap[timeRange] || durationMap['24h'],
          hours: parseInt(timeRange) || 24,
          confidence: result.confidence || 60,
          keyword: result.keywords?.join('、') || 'AI分析',
          method: 'AI驅動'
        };
      }
    }
  } catch (error) {
    console.error('AI時間範圍解析錯誤:', error);
  }
  
  // AI失敗或信心度不足時，使用fallback
  console.log('AI時間解析失敗或信心度不足，使用fallback');
  return parseTimeRangeFallback(query);
}

// 8. Fallback時間範圍解析（保留原有邏輯作為備用）
function parseTimeRangeFallback(query) {
  const queryLower = query.toLowerCase();
  
  // 遍歷所有時間範圍配置
  for (const [rangeKey, config] of Object.entries(FALLBACK_TIME_RANGES)) {
    for (const keyword of config.keywords) {
      if (queryLower.includes(keyword.toLowerCase())) {
        return {
          range: rangeKey,
          duration: config.duration,
          hours: config.hours,
          keyword: keyword,
          confidence: 70,
          method: 'Fallback規則'
        };
      }
    }
  }
  
  // 預設返回24小時
  return {
    range: 'time_24',
    duration: FALLBACK_TIME_RANGES.time_24.duration,
    hours: FALLBACK_TIME_RANGES.time_24.hours,
    keyword: '預設',
    confidence: 50,
    method: 'Fallback預設'
  };
}

// 9. 🤖 AI驅動的檔案引用檢測（取代傳統正則表達式）
function detectFileReference(text) {
  const config = getConfig();
  
  // 如果沒有API Key，直接使用fallback
  if (!config.geminiApiKey) {
    console.log('沒有Gemini API Key，使用fallback檔案檢測');
    return detectFileReferenceFallback(text);
  }
  
  const prompt = `分析用戶訊息是否在詢問檔案相關問題：

用戶訊息：「${text}」

請判斷這是否為檔案查詢意圖。注意區分：
✅ 檔案查詢：「剛才的圖片怎麼樣？」、「那個PDF檔案」、「今天上傳的文件分析」
❌ 非檔案查詢：「今天美國有什麼新聞？」、「天氣如何？」、「股價查詢」

以JSON格式回答：
{
  "isFileQuery": true/false,
  "confidence": 0-100,
  "reasoning": "判斷原因",
  "detectedElements": ["檔案相關詞彙"]
}`;

  try {
    const aiResponse = callGemini(prompt, 'general');
    const jsonMatch = aiResponse.match(/\{[\s\S]*\}/);
    
    if (jsonMatch) {
      const result = JSON.parse(jsonMatch[0]);
      
      // 只有當AI高信心度認為是檔案查詢時才返回true
      if (result.confidence >= 75 && result.isFileQuery) {
        console.log(`AI檔案檢測：檔案查詢 (信心度: ${result.confidence}%, 原因: ${result.reasoning})`);
        return true;
      } else {
        console.log(`AI檔案檢測：非檔案查詢 (信心度: ${result.confidence}%, 原因: ${result.reasoning})`);
        return false;
      }
    }
  } catch (error) {
    console.error('AI檔案引用檢測錯誤:', error);
  }
  
  // AI失敗時，使用智能fallback
  console.log('AI檔案檢測失敗，使用智能fallback');
  return detectFileReferenceFallback(text);
}

// 10. 智能Fallback檔案引用檢測（比原版更保守）
function detectFileReferenceFallback(text) {
  // 明確排除非檔案查詢（更嚴格的過濾）
  const nonFilePatterns = [
    /新聞|消息|news/i,                    // 新聞查詢
    /天氣|氣溫|weather|溫度/i,            // 天氣查詢  
    /股價|股票|匯率|price|金融/i,         // 金融查詢
    /時間|現在|what time/i,               // 時間查詢
    /建議|推薦|recommend/i,               // 建議請求
    /幫我.*?(查|找|搜)(?!.*檔案)/i,       // 協助查詢（非檔案）
    /美國|中國|台灣.*?(如何|什麼)/i,      // 地區資訊查詢
    /電影|音樂|遊戲.*?(推薦|介紹)/i       // 娛樂推薦
  ];
  
  // 如果包含非檔案相關的模式，直接返回 false
  if (nonFilePatterns.some(pattern => pattern.test(text))) {
    console.log('Fallback檔案檢測：排除非檔案查詢');
    return false;
  }
  
  // 必須同時滿足「檔案詞彙」+「指向詞彙」才算檔案查詢（更嚴格）
  const hasFileKeyword = /檔案|文件|圖片|照片|PDF|影片|音訊|相片|報告/.test(text);
  const hasPointerKeyword = /剛才|剛剛|那個|這個|上一個|最新|最近.*?(上傳|傳送|分享)/.test(text);
  const hasAnalysisKeyword = /分析|查看|開啟|摘要/.test(text) && hasFileKeyword;
  
  const isFileReference = (hasFileKeyword && hasPointerKeyword) || hasAnalysisKeyword;
  
  console.log(`Fallback檔案檢測：${isFileReference ? '檔案查詢' : '非檔案查詢'} (檔案詞彙:${hasFileKeyword}, 指向詞彙:${hasPointerKeyword}, 分析詞彙:${hasAnalysisKeyword})`);
  return isFileReference;
}

// 11. AI驅動的檔案引用處理
function handleFileReference(userId, query, sourceType) {
  try {
    // 1. 解析時間範圍
    const timeInfo = parseTimeRange(query);
    console.log(`檔案查詢 - 用戶: ${userId}, 查詢: ${query}, 時間範圍: ${timeInfo.hours}小時 (${timeInfo.keyword}) [${timeInfo.method}]`);
    
    // 2. 獲取候選檔案
    const candidateFiles = getRecentFiles(userId, timeInfo.duration, 20);
    
    if (candidateFiles.length === 0) {
      return `🤔 在${timeInfo.keyword}的時間範圍內沒有找到檔案。\n\n💡 您可以嘗試：\n• 擴大搜尋範圍：「最近的檔案」\n• 具體描述：「今天上傳的PDF檔案」\n• 重新上傳檔案`;
    }
    
    // 3. 使用AI智能匹配檔案
    const config = getConfig();
    if (config.geminiApiKey) {
      const bestMatch = findFileWithAI(query, candidateFiles, config.geminiApiKey);
      
      if (!bestMatch) {
        return `🤔 雖然找到${candidateFiles.length}個檔案，但沒有完全符合「${query}」的檔案。\n\n📁 ${timeInfo.keyword}的檔案：\n${candidateFiles.slice(0, 3).map((f, i) => `${i+1}. ${f.fileName} (${f.fileType})`).join('\n')}`;
      }
      
      // 4. 檢查是否需要進行延遲分析
      if (bestMatch.analysisStatus === '已儲存') {
        const analysisResult = performDelayedAnalysis(bestMatch, query, config.geminiApiKey);
        updateFileAnalysisStatus(bestMatch.fileId, '已分析', analysisResult);
        
        return `📁 找到檔案：${bestMatch.fileName}\n\n🤖 AI 分析：${analysisResult}`;
      } else {
        return `📁 找到檔案：${bestMatch.fileName}\n📊 檔案大小：${bestMatch.fileSize}\n🔗 檔案連結：${bestMatch.fileUrl}\n\n💡 輸入「分析 ${bestMatch.fileName}」進行詳細分析`;
      }
    } else {
      // 沒有AI，返回最新的檔案
      const latestFile = candidateFiles[0];
      return `📁 找到最新檔案：${latestFile.fileName}\n📊 檔案大小：${latestFile.fileSize}\n🔗 檔案連結：${latestFile.fileUrl}\n\n💡 設定 Gemini API Key 可啟用智能檔案匹配`;
    }
    
  } catch (error) {
    console.error('處理檔案引用錯誤:', error);
    return `⚠️ 處理檔案引用時發生錯誤：${error.message}`;
  }
}

// 12. AI智能檔案匹配
function findFileWithAI(userQuery, candidateFiles, apiKey) {
  try {
    const prompt = `分析用戶檔案查詢請求：

用戶問：「${userQuery}」

候選檔案清單：
${candidateFiles.map((f, i) => `
${i+1}. 檔案名：${f.fileName}
   類型：${f.fileType}
   上傳時間：${formatTimeAgo(f.timestamp)}
   AI描述：${f.description}
   檔案ID：${f.fileId}
`).join('\n')}

請分析哪個檔案最符合用戶需求。
直接回答檔案ID，如果沒有合適的檔案請回答"NONE"。
只要回答檔案ID，不要解釋。`;

    const payload = JSON.stringify({
      contents: [{
        parts: [{ text: prompt }]
      }]
    });

    const response = UrlFetchApp.fetch(
      `https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${apiKey}`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        payload: payload
      }
    );

    if (response.getResponseCode() !== 200) {
      throw new Error(`Gemini API 錯誤 ${response.getResponseCode()}`);
    }

    const result = JSON.parse(response.getContentText());
    const aiResponse = result.candidates[0].content.parts[0].text || '';
    const fileId = aiResponse.trim();

    if (fileId === 'NONE') {
      return null;
    }

    return candidateFiles.find(f => f.fileId === fileId);

  } catch (error) {
    console.error('AI檔案匹配錯誤:', error);
    return null;
  }
}

// 13. 獲取最近檔案
function getRecentFiles(userId, timeRange = null, limit = 20) {
  try {
    const sheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName('檔案記憶庫');
    if (!sheet) return [];

    const data = sheet.getDataRange().getValues();
    let userFiles = data.slice(1) // 跳過標題行
      .filter(row => row[1] === userId) // 篩選該用戶的檔案
      .sort((a, b) => new Date(b[0]) - new Date(a[0])); // 按時間排序（最新的在前）

    // 如果指定了時間範圍，進行時間篩選
    if (timeRange) {
      const cutoffTime = new Date(Date.now() - timeRange);
      userFiles = userFiles.filter(row => new Date(row[0]) >= cutoffTime);
    }

    return userFiles.slice(0, limit).map(row => ({
      timestamp: row[0],
      fileId: row[2],
      fileName: row[3],
      fileType: row[4],
      fileSize: row[5],
      description: row[6],
      features: row[7],
      fileUrl: row[8],
      analysisStatus: row[9]
    }));

  } catch (error) {
    console.error('獲取最近檔案錯誤:', error);
    return [];
  }
}

// 14. 執行延遲分析
function performDelayedAnalysis(fileInfo, userQuery, apiKey) {
  try {
    const prompt = `用戶詢問關於檔案「${fileInfo.fileName}」的問題：「${userQuery}」

檔案資訊：
- 檔案類型：${fileInfo.fileType}
- 檔案大小：${fileInfo.fileSize}
- 簡短描述：${fileInfo.description}
- 關鍵特徵：${fileInfo.features}

請根據用戶的具體問題，用繁體中文提供針對性的分析和回答。限制在200字以內。`;

    const payload = JSON.stringify({
      contents: [{
        parts: [{ text: prompt }]
      }]
    });

    const response = UrlFetchApp.fetch(
      `https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${apiKey}`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        payload: payload
      }
    );

    if (response.getResponseCode() !== 200) {
      throw new Error(`Gemini API 錯誤 ${response.getResponseCode()}`);
    }

    const result = JSON.parse(response.getContentText());
    return result.candidates[0].content.parts[0].text || '無法進行分析';
    
  } catch (error) {
    console.error('延遲分析錯誤:', error);
    return `分析過程中發生錯誤：${error.message}`;
  }
}

// 15. 更新檔案分析狀態
function updateFileAnalysisStatus(fileId, status, analysisResult = '') {
  try {
    const sheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName('檔案記憶庫');
    if (!sheet) return;
    
    const data = sheet.getDataRange().getValues();
    
    for (let i = 1; i < data.length; i++) { // 從第2行開始（跳過標題）
      if (data[i][2] === fileId) { // 檔案ID在第3列（索引2）
        sheet.getRange(i + 1, 10).setValue(status); // 分析狀態在第10列
        
        // 如果有分析結果，記錄到分析任務佇列
        if (analysisResult) {
          recordAnalysisTask(data[i][1], fileId, '延遲分析', '已完成', analysisResult, '');
        }
        
        break;
      }
    }
  } catch (error) {
    console.error('更新檔案分析狀態錯誤:', error);
  }
}

// 16. 記錄分析任務
function recordAnalysisTask(userId, fileId, analysisType, status, result, userQuery) {
  try {
    const sheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName('分析任務佇列');
    if (sheet) {
      const timestamp = new Date();
      sheet.appendRow([
        timestamp,      // 時間戳
        userId,         // 用戶ID
        fileId,         // 檔案ID
        analysisType,   // 分析類型
        status,         // 任務狀態
        result,         // 分析結果
        userQuery       // 用戶查詢
      ]);
    }
  } catch (error) {
    console.error('記錄分析任務錯誤:', error);
  }
}

// 17. 處理檔案記憶查詢
function handleFileMemoryQuery(userId, query, sourceType) {
  try {
    if (query.includes('最新') || query.includes('latest')) {
      // 顯示最新檔案
      const latestFiles = getRecentFiles(userId, null, 5);
      if (latestFiles.length === 0) {
        return '📁 您還沒有上傳任何檔案。';
      }
      
      let response = '📁 您最近上傳的檔案：\n\n';
      latestFiles.forEach((file, index) => {
        response += `${index + 1}. ${file.fileName}\n📊 ${file.fileSize} | 🔗 ${file.fileUrl}\n\n`;
      });
      
      return response;
    } else {
      // 一般檔案查詢
      return handleFileReference(userId, query, sourceType);
    }
  } catch (error) {
    console.error('處理檔案記憶查詢錯誤:', error);
    return '⚠️ 查詢檔案記憶時發生錯誤';
  }
}

// 18. 格式化時間差
function formatTimeAgo(timestamp) {
  const now = new Date();
  const uploadTime = new Date(timestamp);
  const diffMs = now - uploadTime;
  
  const diffMins = Math.floor(diffMs / (1000 * 60));
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
  
  if (diffMins < 60) {
    return `${diffMins}分鐘前`;
  } else if (diffHours < 24) {
    return `${diffHours}小時前`;
  } else {
    return `${diffDays}天前`;
  }
}

// 19. 簡化的檔案引用查找（用於測試）
function findReferencedFile(userId, query, limit = 20) {
  try {
    const candidateFiles = getRecentFiles(userId, null, limit);

    if (candidateFiles.length === 0) {
      return null;
    }

    // 簡單的關鍵字匹配
    const queryLower = query.toLowerCase();

    // 優先匹配檔案名稱
    let bestMatch = candidateFiles.find(file =>
      file.fileName.toLowerCase().includes(queryLower)
    );

    // 如果沒找到，嘗試匹配描述
    if (!bestMatch) {
      bestMatch = candidateFiles.find(file =>
        file.description.toLowerCase().includes(queryLower)
      );
    }

    // 如果還沒找到，嘗試匹配檔案類型
    if (!bestMatch) {
      bestMatch = candidateFiles.find(file =>
        file.fileType.toLowerCase().includes(queryLower)
      );
    }

    // 最後返回最新的檔案
    return bestMatch || candidateFiles[0];

  } catch (error) {
    console.error('查找引用檔案錯誤:', error);
    return null;
  }
}

// 20. 檢查記憶系統健康狀態
function checkMemorySystemHealth() {
  try {
    const ss = SpreadsheetApp.getActiveSpreadsheet();
    const requiredSheets = ['用戶對話歷史', '檔案記憶庫', '分析任務佇列'];
    const result = {
      status: 'healthy',
      sheets: {},
      totalRecords: 0
    };
    
    requiredSheets.forEach(sheetName => {
      const sheet = ss.getSheetByName(sheetName);
      if (sheet) {
        const recordCount = sheet.getLastRow() - 1; // 扣除標題行
        result.sheets[sheetName] = {
          exists: true,
          records: recordCount
        };
        result.totalRecords += recordCount;
      } else {
        result.sheets[sheetName] = {
          exists: false,
          records: 0
        };
        result.status = 'warning';
      }
    });
    
    console.log('記憶系統健康檢查:', result);
    return result;
  } catch (error) {
    console.error('記憶系統健康檢查錯誤:', error);
    return { status: 'error', error: error.toString() };
  }
}

// 21. 🧪 測試函數：檔案引用檢測
function testDetectFileReference() {
  console.log('=== 測試 AI 驅動檔案引用檢測 ===');
  
  const testCases = [
    // 應該被識別為檔案查詢
    { text: '剛才的圖片怎麼樣？', expected: true },
    { text: '那個PDF檔案', expected: true },
    { text: '今天上傳的文件分析', expected: true },
    { text: '這張照片內容', expected: true },
    
    // 應該被識別為非檔案查詢
    { text: '今天美國有什麼新聞？', expected: false },
    { text: '天氣如何？', expected: false },
    { text: '股價查詢', expected: false },
    { text: '最近有什麼好電影？', expected: false }
  ];
  
  testCases.forEach((testCase, index) => {
    const result = detectFileReference(testCase.text);
    const status = result === testCase.expected ? '✅' : '❌';
    console.log(`${status} 測試 ${index + 1}: "${testCase.text}" -> ${result} (預期: ${testCase.expected})`);
  });
}

// 22. 🧪 測試函數：時間範圍解析
function testParseTimeRange() {
  console.log('=== 測試 AI 驅動時間範圍解析 ===');
  
  const testCases = [
    '剛才的檔案',
    '今天上傳的圖片',
    '最近的文件',
    '這週的報告',
    '昨天的照片'
  ];
  
  testCases.forEach((testCase, index) => {
    const result = parseTimeRange(testCase);
    console.log(`📅 測試 ${index + 1}: "${testCase}" -> ${result.range} (${result.hours}小時) [${result.method}] 信心度:${result.confidence}%`);
  });
}
