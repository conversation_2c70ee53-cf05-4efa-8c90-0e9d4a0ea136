// == 群組聊天記錄與查詢系統 ==
// 新增到適當的模組中，建議創建 GroupChatTracker.gs

// 1. 設定群組聊天記錄工作表標題行（添加到 Sheets.gs）
function setupGroupChatSheetHeaders() {
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  let sheet = ss.getSheetByName('群組發言記錄');
  
  if (!sheet) {
    sheet = ss.insertSheet('群組發言記錄');
    console.log('已建立群組發言記錄工作表');
  }
  
  // 檢查是否已有標題行
  if (sheet.getRange('A1').getValue() === '') {
    const headers = [
      '時間戳', '群組ID', '用戶ID', '用戶顯示名稱', '訊息內容', '訊息類型', '回應狀態'
    ];
    sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
    sheet.getRange(1, 1, 1, headers.length).setFontWeight('bold');
    sheet.getRange(1, 1, 1, headers.length).setBackground('#e1f5fe');
    console.log('已設定群組發言記錄工作表標題行');
  }
  
  return sheet;
}

// 2. 記錄群組發言（修改 Webhook.gs 中的 handleGroupMessage）
/**
 * 🔧 修復版的群組發言記錄函數
 * 包含所有必要的容錯處理和獨立實現
 */
function recordGroupMessage(event, userId, sourceType) {
  try {
    console.log(`🔄 開始記錄群組發言: 用戶=${userId}, 來源=${sourceType}`);

    // 1. 確保工作表存在
    const ss = SpreadsheetApp.getActiveSpreadsheet();
    let sheet = ss.getSheetByName('群組發言記錄');

    if (!sheet) {
      sheet = ss.insertSheet('群組發言記錄');
      const headers = ['時間戳', '群組ID', '用戶ID', '用戶顯示名稱', '訊息內容', '訊息類型', '回應狀態'];
      sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
      sheet.getRange(1, 1, 1, headers.length).setFontWeight('bold');
      sheet.getRange(1, 1, 1, headers.length).setBackground('#e1f5fe');
      console.log('✅ 已建立群組發言記錄工作表');
    }

    // 2. 提取基本資訊
    const timestamp = new Date();
    const groupId = event.source?.groupId || event.source?.roomId || 'unknown';
    const messageType = event.message?.type || event.type || 'unknown';

    // 3. 處理訊息內容（自包含實現）
    let messageContent = '';
    let responseStatus = '已記錄';

    if (event.message?.type === 'text') {
      messageContent = event.message.text || '';
      // 簡單的正規化（避免依賴外部函數）
      const normalizedText = messageContent.replace(/[！？。，；：]/g, function(match) {
        return { '！': '!', '？': '?', '。': '.', '，': ',', '；': ';', '：': ':' }[match] || match;
      }).trim();

      responseStatus = normalizedText.startsWith('!') ? '已回應' : '靜默記錄';
    } else if (['image', 'video', 'audio', 'file'].includes(event.message?.type)) {
      // 自包含的媒體類型顯示
      const mediaTypeMap = {
        'image': '圖片',
        'video': '影片',
        'audio': '音訊',
        'file': '檔案'
      };
      messageContent = `[${mediaTypeMap[event.message.type] || '媒體'}]`;
      responseStatus = '媒體已處理';
    } else {
      messageContent = `[${messageType}]`;
      responseStatus = '已記錄';
    }

    // 4. 獲取用戶顯示名稱（簡化版）
    let displayName = userId; // 預設使用 userId

    try {
      // 嘗試獲取配置（容錯處理）
      const config = getConfig();
      if (config && config.lineChannelAccessToken && groupId !== 'unknown') {
        const url = `https://api.line.me/v2/bot/group/${groupId}/member/${userId}`;
        const response = UrlFetchApp.fetch(url, {
          method: 'GET',
          headers: {
            'Authorization': 'Bearer ' + config.lineChannelAccessToken
          },
          muteHttpExceptions: true
        });

        if (response.getResponseCode() === 200) {
          const memberInfo = JSON.parse(response.getContentText());
          displayName = memberInfo.displayName || userId;
        }
      }
    } catch (nameError) {
      console.log(`無法獲取用戶顯示名稱，使用 userId: ${nameError.message}`);
      // 繼續使用 userId 作為 displayName
    }

    // 5. 寫入資料
    const rowData = [
      timestamp,
      groupId,
      userId,
      displayName,
      messageContent,
      messageType,
      responseStatus
    ];

    sheet.appendRow(rowData);
    console.log(`✅ 成功記錄群組發言: ${displayName}(${userId}) - ${messageContent.substring(0, 30)}...`);

    // 6. 記錄到活動日誌（容錯處理）
    try {
      logActivity('群組發言記錄', `群組:${groupId}, 用戶:${displayName}(${userId}), 內容:${messageContent.substring(0, 30)}...`);
    } catch (logError) {
      console.log(`活動日誌記錄失敗: ${logError.message}`);
      // 不影響主要功能繼續執行
    }

    return true;

  } catch (error) {
    console.error('❌ 記錄群組發言錯誤:', error);

    // 嘗試記錄錯誤到活動日誌
    try {
      logActivity('群組發言記錄錯誤', `用戶:${userId}, 錯誤:${error.toString()}`);
    } catch (logError) {
      console.error('活動日誌記錄也失敗:', logError);
    }

    return false;
  }
}


// 3. 獲取用戶顯示名稱（使用 LINE API）
function getUserDisplayName(userId, groupId) {
  try {
    const config = getConfig();
    if (!config.lineChannelAccessToken) {
      return null;
    }
    
    // 嘗試獲取群組成員資料
    const url = `https://api.line.me/v2/bot/group/${groupId}/member/${userId}`;
    const response = UrlFetchApp.fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer ' + config.lineChannelAccessToken
      },
      muteHttpExceptions: true
    });
    
    if (response.getResponseCode() === 200) {
      const memberInfo = JSON.parse(response.getContentText());
      return memberInfo.displayName || null;
    } else {
      console.log(`無法獲取用戶 ${userId} 的顯示名稱，HTTP ${response.getResponseCode()}`);
      return null;
    }
    
  } catch (error) {
    console.error('獲取用戶顯示名稱錯誤:', error);
    return null;
  }
}

// 4. 處理用戶查詢命令（添加到 TextProcessor.gs 的命令路由中）
function handleUserQueryCommand(query, replyToken, userId, sourceType) {
  try {
    console.log(`處理用戶查詢: ${query}`);
    
    // 解析查詢命令
    const queryResult = parseUserQuery(query);
    if (!queryResult.isValidQuery) {
      return `❌ 查詢格式錯誤\n\n💡 正確格式：\n• !張三都講了些什麼？\n• !李四最近說什麼？\n• !你覺得王五講的有道理嗎？`;
    }
    
    // 搜尋用戶發言記錄
    const userMessages = searchUserMessages(queryResult.targetUser, queryResult.groupId, queryResult.timeRange);
    
    if (userMessages.length === 0) {
      return `🤔 沒有找到「${queryResult.targetUser}」的發言記錄\n\n💡 可能原因：\n• 用戶名稱不完全匹配\n• 該用戶最近沒有發言\n• 記錄系統剛啟用`;
    }
    
    // 根據查詢類型生成回應
    if (queryResult.isAnalysisQuery) {
      return generateUserAnalysis(queryResult.targetUser, userMessages, queryResult.analysisPrompt);
    } else {
      return generateUserMessageSummary(queryResult.targetUser, userMessages, queryResult.timeRange);
    }
    
  } catch (error) {
    console.error('處理用戶查詢錯誤:', error);
    return `⚠️ 查詢處理失敗：${error.message}`;
  }
}

// 5. 解析用戶查詢（AI 輔助 + 規則匹配）
function parseUserQuery(query) {
  try {
    const result = {
      isValidQuery: false,
      targetUser: '',
      timeRange: '7days', // 預設7天
      isAnalysisQuery: false,
      analysisPrompt: '',
      groupId: null
    };
    
    // 使用 AI 解析查詢意圖
    const config = getConfig();
    if (config.geminiApiKey) {
      try {
        const aiPrompt = `分析用戶查詢意圖：「${query}」

請判斷這是否為查詢特定用戶發言的請求，以JSON格式回答：
{
  "isValidQuery": true/false,
  "targetUser": "目標用戶名稱或關鍵字",
  "timeRange": "recent/7days/30days/all",
  "isAnalysisQuery": true/false,
  "analysisPrompt": "如果是分析請求，提取分析意圖"
}

範例：
• "蚵仔煎都講了些什麼？" -> 查詢發言
• "你覺得蚵仔大講的有道理嗎？" -> 分析請求`;

        const aiResponse = callGemini(aiPrompt, 'general');
        const jsonMatch = aiResponse.match(/\{[\s\S]*\}/);
        
        if (jsonMatch) {
          const aiResult = JSON.parse(jsonMatch[0]);
          Object.assign(result, aiResult);
          
          // AI 解析成功，直接返回
          if (result.isValidQuery && result.targetUser) {
            console.log('AI 解析查詢成功:', result);
            return result;
          }
        }
      } catch (error) {
        console.log('AI 解析查詢失敗，使用規則匹配:', error.message);
      }
    }
    
    // AI 失敗時使用規則匹配
    return parseUserQueryFallback(query);
    
  } catch (error) {
    console.error('解析用戶查詢錯誤:', error);
    return {
      isValidQuery: false,
      targetUser: '',
      timeRange: '7days',
      isAnalysisQuery: false,
      analysisPrompt: '',
      groupId: null
    };
  }
}

// 6. 規則匹配解析（備用方案）
function parseUserQueryFallback(query) {
  const result = {
    isValidQuery: false,
    targetUser: '',
    timeRange: '7days',
    isAnalysisQuery: false,
    analysisPrompt: '',
    groupId: null
  };
  
  // 檢測查詢模式
  const queryPatterns = [
    // 發言查詢模式
    /(.+?)都講了些?什麼/,
    /(.+?)最近說什麼/,
    /(.+?)都說了什麼/,
    /(.+?)講過什麼/,
    
    // 分析查詢模式  
    /你覺得(.+?)講的/,
    /分析(.+?)的發言/,
    /(.+?)說得對嗎/,
    /(.+?)有道理嗎/
  ];
  
  for (const pattern of queryPatterns) {
    const match = query.match(pattern);
    if (match) {
      result.isValidQuery = true;
      result.targetUser = match[1].trim();
      
      // 判斷是否為分析查詢
      result.isAnalysisQuery = query.includes('你覺得') || query.includes('分析') || 
                              query.includes('有道理') || query.includes('說得對');
      
      if (result.isAnalysisQuery) {
        result.analysisPrompt = query;
      }
      
      // 判斷時間範圍
      if (query.includes('最近')) {
        result.timeRange = '7days';
      } else if (query.includes('今天')) {
        result.timeRange = '24h';
      } else {
        result.timeRange = '30days';
      }
      
      break;
    }
  }
  
  console.log('規則匹配解析結果:', result);
  return result;
}

// 7. 搜尋用戶發言記錄
function searchUserMessages(targetUser, groupId = null, timeRange = '7days') {
  try {
    const sheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName('群組發言記錄');
    if (!sheet) {
      console.log('群組發言記錄工作表不存在');
      return [];
    }
    
    // 計算時間範圍
    let cutoffTime = new Date();
    switch(timeRange) {
      case '24h':
        cutoffTime.setHours(cutoffTime.getHours() - 24);
        break;
      case '7days':
        cutoffTime.setDate(cutoffTime.getDate() - 7);
        break;
      case '30days':
        cutoffTime.setDate(cutoffTime.getDate() - 30);
        break;
      case 'all':
        cutoffTime = new Date(0);
        break;
      default:
        cutoffTime.setDate(cutoffTime.getDate() - 7);
    }
    
    const data = sheet.getDataRange().getValues();
    const userMessages = data.slice(1) // 跳過標題行
      .filter(row => {
        const timestamp = new Date(row[0]);
        const displayName = row[3] || '';
        const messageContent = row[4] || '';
        const messageType = row[5] || '';
        
        // 時間篩選
        if (timestamp < cutoffTime) return false;
        
        // 只包含文字訊息
        if (messageType !== 'text') return false;
        
        // 排除 Bot 命令
        if (messageContent.startsWith('!')) return false;
        
        // 用戶名稱匹配（支援部分匹配和昵稱）
        return matchUserName(displayName, targetUser);
      })
      .sort((a, b) => new Date(b[0]) - new Date(a[0])) // 按時間倒序
      .slice(0, 50) // 限制最多50條記錄
      .map(row => ({
        timestamp: row[0],
        groupId: row[1],
        userId: row[2],
        displayName: row[3],
        messageContent: row[4],
        messageType: row[5]
      }));
    
    console.log(`找到用戶「${targetUser}」的 ${userMessages.length} 條發言記錄`);
    return userMessages;
    
  } catch (error) {
    console.error('搜尋用戶發言記錄錯誤:', error);
    return [];
  }
}

// 8. 用戶名稱匹配（支援昵稱和簡稱）
function matchUserName(displayName, targetUser) {
  if (!displayName || !targetUser) return false;
  
  const name = displayName.toLowerCase();
  const target = targetUser.toLowerCase();
  
  // 完全匹配
  if (name === target) return true;
  
  // 包含匹配
  if (name.includes(target) || target.includes(name)) return true;
  
  // 昵稱匹配邏輯（可擴展）
  const nicknameMap = {
    '蚵仔煎': ['蚵大', '煎大', '蚵仔', '煎哥'],
    // 可以根據需要添加更多昵稱映射
  };
  
  // 檢查是否為已知昵稱
  for (const [fullName, nicknames] of Object.entries(nicknameMap)) {
    if (name.includes(fullName.toLowerCase()) && nicknames.some(nick => target.includes(nick.toLowerCase()))) {
      return true;
    }
    if (target.includes(fullName.toLowerCase()) && nicknames.some(nick => name.includes(nick.toLowerCase()))) {
      return true;
    }
  }
  
  return false;
}

// 9. 生成用戶發言摘要
function generateUserMessageSummary(targetUser, userMessages, timeRange) {
  try {
    if (userMessages.length === 0) {
      return `📝 ${targetUser} 在指定時間範圍內沒有發言記錄`;
    }
    
    const timeRangeText = {
      '24h': '今天',
      '7days': '最近7天',
      '30days': '最近30天',
      'all': '全部時間'
    }[timeRange] || '指定時間';
    
    let summary = `💬 ${targetUser} 的發言記錄 (${timeRangeText})\n\n`;
    summary += `📊 發言統計：共 ${userMessages.length} 條發言\n`;
    summary += `⏰ 時間範圍：${formatTimeAgo(userMessages[userMessages.length - 1].timestamp)} 到 ${formatTimeAgo(userMessages[0].timestamp)}\n\n`;
    
    // 顯示最近5條發言
    summary += `📋 最近發言：\n`;
    userMessages.slice(0, 5).forEach((msg, index) => {
      const timeAgo = formatTimeAgo(msg.timestamp);
      const content = msg.messageContent.substring(0, 50) + (msg.messageContent.length > 50 ? '...' : '');
      summary += `${index + 1}. ${content} (${timeAgo})\n`;
    });
    
    if (userMessages.length > 5) {
      summary += `\n... 還有 ${userMessages.length - 5} 條發言\n`;
    }
    
    summary += `\n💡 輸入「!你覺得${targetUser}講的有道理嗎？」可獲得AI分析`;
    
    return summary;
    
  } catch (error) {
    console.error('生成用戶發言摘要錯誤:', error);
    return `⚠️ 摘要生成失敗：${error.message}`;
  }
}

// 10. 生成用戶發言分析（AI 輔助）
function generateUserAnalysis(targetUser, userMessages, analysisPrompt) {
  try {
    if (userMessages.length === 0) {
      return `🤔 沒有找到 ${targetUser} 的發言記錄，無法進行分析`;
    }
    
    const config = getConfig();
    if (!config.geminiApiKey) {
      return `🤖 AI 分析功能需要設定 Gemini API Key\n\n📊 ${targetUser} 共有 ${userMessages.length} 條發言記錄\n💡 請先設定 API Key 後再嘗試分析功能`;
    }
    
    // 構建分析上下文
    const recentMessages = userMessages.slice(0, 20); // 最近20條發言
    const messageContext = recentMessages.map((msg, index) => 
      `${index + 1}. ${msg.messageContent}`
    ).join('\n');
    
    const aiPrompt = `請分析用戶「${targetUser}」的發言內容：

用戶最近的發言：
${messageContext}

用戶問題：「${analysisPrompt}」

請用繁體中文回答，分析要求：
1. 客觀分析發言內容的特點
2. 評估發言的邏輯性和合理性
3. 指出有價值的觀點
4. 保持中性和尊重的態度
5. 控制在250字以內

分析：`;

    try {
      const analysis = callGemini(aiPrompt, 'general');
      
      let response = `🤖 AI 分析：${targetUser} 的發言\n\n`;
      response += `${analysis}\n\n`;
      response += `📊 分析依據：最近 ${recentMessages.length} 條發言\n`;
      response += `⏰ 時間範圍：${formatTimeAgo(recentMessages[recentMessages.length - 1].timestamp)} 到 ${formatTimeAgo(recentMessages[0].timestamp)}`;
      
      return response;
      
    } catch (aiError) {
      console.error('AI 分析失敗:', aiError);
      return `⚠️ AI 分析失敗：${aiError.message}\n\n📊 ${targetUser} 共有 ${userMessages.length} 條發言記錄\n💡 您可以嘗試查看發言摘要：「!${targetUser}都講了些什麼？」`;
    }
    
  } catch (error) {
    console.error('生成用戶發言分析錯誤:', error);
    return `⚠️ 分析生成失敗：${error.message}`;
  }
}

// 11. 格式化時間差（輔助函數）
function formatTimeAgo(timestamp) {
  const now = new Date();
  const time = new Date(timestamp);
  const diffMs = now - time;
  
  const diffMins = Math.floor(diffMs / (1000 * 60));
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
  
  if (diffMins < 1) {
    return '剛剛';
  } else if (diffMins < 60) {
    return `${diffMins}分鐘前`;
  } else if (diffHours < 24) {
    return `${diffHours}小時前`;
  } else {
    return `${diffDays}天前`;
  }
}

// 12. 🧪 測試函數：群組聊天記錄系統
function testGroupChatTracker() {
  console.log('=== 測試群組聊天記錄系統 ===');
  
  try {
    // 1. 初始化工作表
    const sheet = setupGroupChatSheetHeaders();
    console.log('✅ 工作表初始化成功');
    
    // 2. 測試查詢解析
    const testQueries = [
      '蚵仔煎都講了些什麼？',
      '你覺得蚵大講的有道理嗎？',
      '煎大最近說什麼？'
    ];
    
    testQueries.forEach((query, index) => {
      const result = parseUserQuery(query);
      console.log(`測試查詢 ${index + 1}: "${query}"`);
      console.log(`  解析結果: ${result.isValidQuery ? '✅' : '❌'} 目標用戶: ${result.targetUser}, 分析查詢: ${result.isAnalysisQuery}`);
    });
    
    // 3. 測試昵稱匹配
    const testMatches = [
      ['蚵仔煎', '蚵大'],
      ['蚵仔煎大師', '煎大'],
      ['張三', '小張']
    ];
    
    testMatches.forEach(([displayName, targetUser], index) => {
      const match = matchUserName(displayName, targetUser);
      console.log(`昵稱匹配 ${index + 1}: "${displayName}" vs "${targetUser}" -> ${match ? '✅' : '❌'}`);
    });
    
    console.log('✅ 群組聊天記錄系統測試完成');
    
  } catch (error) {
    console.error('❌ 群組聊天記錄系統測試失敗:', error);
  }
}
