// == AI 智能功能模組 (The Brain) ==
// 專門處理需要多步驟推理的複雜 AI 功能，例如智能搜尋

/**
 * AI 驅動的智能搜尋系統主函數
 * 1. 判斷是否需要網路搜尋
 * 2. 若需要，執行搜尋
 * 3. 整合搜尋結果或直接回答
 * @param {string} question 用戶的原始問題
 * @returns {string} AI 生成的最終回答
 */
function callGeminiWithSmartSearch(question) {
  const config = getConfig(); // 從 Code.gs 獲取設定

  // 階段一：讓 Gemini 判斷是否需要搜尋
  const searchDecision = checkIfNeedSearch(question);
  
  if (searchDecision.needSearch && config.googleSearchApiKey && config.googleSearchEngineId) {
    // 需要搜尋：執行AI驅動的搜尋流程
    logActivity('AI 搜尋判斷', `問題: ${question}, 搜尋關鍵字: ${searchDecision.searchQuery}`);
    
    try {
      // 執行 Google 搜尋
      const searchResults = performGoogleSearch(searchDecision.searchQuery);
      
      // 階段二：讓 Gemini 整合搜尋結果回答
      return generateAnswerWithSearchResults(question, searchResults);
      
    } catch (searchError) {
      console.error('搜尋過程錯誤:', searchError);
      // 搜尋失敗時回退到一般回答
      const generalPrompt = `請用繁體中文簡潔明瞭地回答以下問題，限制在200字以內。\n\n問題：${question}`;
      return callGemini(generalPrompt, 'general');
    }
  } else {
    // 不需要搜尋：直接用 Gemini 回答
    const generalPrompt = `請用繁體中文簡潔明瞭地回答以下問題，限制在200字以內。\n\n問題：${question}`;
    return callGemini(generalPrompt, 'general');
  }
}

/**
 * AI 判斷是否需要搜尋
 * @param {string} question 用戶問題
 * @returns {{needSearch: boolean, searchQuery: string, reason: string}} 判斷結果
 */
function checkIfNeedSearch(question) {
  const prompt = `分析用戶問題「${question}」，判斷是否需Google搜尋以獲取最新資訊（如新聞、股價、天氣）。以JSON格式回答：{"needSearch": true/false, "searchQuery": "最佳搜尋關鍵字", "reason": "判斷原因"}`;

  try {
    // 注意：這裡呼叫的是 Code.gs 中定義的統一 callGemini 函數
    const aiResponse = callGemini(prompt, 'general');
    
    const jsonMatch = aiResponse.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      return JSON.parse(jsonMatch[0]);
    }
    return { needSearch: false, searchQuery: '', reason: 'AI回應解析失敗' };
  } catch (error) {
    console.error('AI 搜尋判斷錯誤:', error);
    return { needSearch: false, searchQuery: '', reason: '判斷過程錯誤' };
  }
}

/**
 * 執行 Google Custom Search
 * @param {string} query 搜尋關鍵字
 * @returns {{results: Array<{title: string, snippet: string, link: string}>}} 搜尋結果
 */
function performGoogleSearch(query) {
  const config = getConfig(); // 從 Code.gs 獲取設定
  const url = `https://www.googleapis.com/customsearch/v1?key=${config.googleSearchApiKey}&cx=${config.googleSearchEngineId}&q=${encodeURIComponent(query)}&num=3`;
  
  const response = UrlFetchApp.fetch(url, { muteHttpExceptions: true });
  if (response.getResponseCode() !== 200) {
    throw new Error(`Google Search API 錯誤 ${response.getResponseCode()}`);
  }
  
  const result = JSON.parse(response.getContentText());
  return {
    results: (result.items || []).map(item => ({ title: item.title, snippet: item.snippet, link: item.link }))
  };
}

/**
 * 整合搜尋結果生成回答
 * @param {string} originalQuestion 原始問題
 * @param {object} searchResults 搜尋結果
 * @returns {string} 整合後的回答
 */
function generateAnswerWithSearchResults(originalQuestion, searchResults) {
  if (!searchResults.results || searchResults.results.length === 0) {
    const generalPrompt = `請用繁體中文簡潔明瞭地回答以下問題，限制在200字以內。\n\n問題：${originalQuestion}`;
    return callGemini(generalPrompt, 'general');
  }
  
  const searchContext = searchResults.results.map((r, i) => `[${i+1}] ${r.title}\n${r.snippet}`).join('\n\n');
  const finalPrompt = `基於以下搜尋結果，用繁體中文回答用戶問題「${originalQuestion}」。\n\n搜尋結果：\n${searchContext}\n\n回答：`;
  
  // 注意：這裡呼叫的是 Code.gs 中定義的統一 callGemini 函數
  let answer = callGemini(finalPrompt, 'general');
  answer += `\n\n🔍 資訊來源：Google 搜尋`;
  return answer;
}

/**
 * 智能內容生成 - 根據不同場景生成個人化回應
 * @param {string} userMessage 用戶訊息
 * @param {string} context 上下文（可選）
 * @returns {string} 個人化回應
 */
function generateSmartResponse(userMessage, context = '') {
  const prompt = `你是一個智能助理，請根據用戶訊息生成自然、友善的回應。

用戶訊息：${userMessage}
${context ? `上下文：${context}` : ''}

要求：
1. 用繁體中文回應
2. 語氣親切自然
3. 回應要有幫助性
4. 長度控制在150字以內

回應：`;

  try {
    return callGemini(prompt, 'general');
  } catch (error) {
    console.error('智能回應生成錯誤:', error);
    return '很抱歉，我現在無法為您提供適當的回應。請稍後再試。';
  }
}

/**
 * 多輪對話上下文管理
 * @param {string} userId 用戶ID
 * @param {string} currentMessage 當前訊息
 * @returns {string} 帶上下文的智能回應
 */
function handleContextualConversation(userId, currentMessage) {
  try {
    // 獲取用戶最近的對話歷史
    const recentHistory = getRecentConversationHistory(userId, 3);
    
    if (recentHistory.length === 0) {
      // 沒有對話歷史，直接處理當前訊息
      return generateSmartResponse(currentMessage);
    }
    
    // 構建上下文
    const contextLines = recentHistory.map(conv => 
      `用戶: ${conv.message}\n助理: ${conv.response}`
    );
    const context = contextLines.join('\n---\n');
    
    const prompt = `基於對話歷史，用繁體中文回應用戶的新訊息。

對話歷史：
${context}

用戶新訊息：${currentMessage}

請生成自然、連貫的回應，考慮對話脈絡，限制在200字以內：`;

    return callGemini(prompt, 'general');
    
  } catch (error) {
    console.error('上下文對話處理錯誤:', error);
    return generateSmartResponse(currentMessage);
  }
}

/**
 * 獲取用戶最近對話歷史
 * @param {string} userId 用戶ID
 * @param {number} limit 獲取數量限制
 * @returns {Array} 對話歷史陣列
 */
function getRecentConversationHistory(userId, limit = 3) {
  try {
    const sheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName('用戶對話歷史');
    if (!sheet) return [];
    
    const data = sheet.getDataRange().getValues();
    const userConversations = data.slice(1) // 跳過標題行
      .filter(row => row[1] === userId) // 篩選該用戶
      .sort((a, b) => new Date(b[0]) - new Date(a[0])) // 按時間倒序
      .slice(0, limit) // 取前N條
      .map(row => ({
        timestamp: row[0],
        message: row[3], // 原始訊息
        response: row[4]  // 處理結果
      }));
    
    return userConversations.reverse(); // 恢復時間順序
  } catch (error) {
    console.error('獲取對話歷史錯誤:', error);
    return [];
  }
}

/**
 * AI 驅動的意圖識別
 * @param {string} userMessage 用戶訊息
 * @returns {{intent: string, confidence: number, entities: object}} 意圖識別結果
 */
function detectUserIntent(userMessage) {
  const prompt = `分析用戶訊息的意圖和實體。

用戶訊息：「${userMessage}」

請識別：
1. 主要意圖 (query_info, request_help, file_operation, note_taking, casual_chat)
2. 信心度 (0-100)
3. 關鍵實體 (時間、地點、檔案類型等)

以JSON格式回答：
{
  "intent": "主要意圖",
  "confidence": 信心度數字,
  "entities": {
    "time": "時間相關",
    "file_type": "檔案類型",
    "topic": "討論主題"
  }
}`;

  try {
    const aiResponse = callGemini(prompt, 'general');
    const jsonMatch = aiResponse.match(/\{[\s\S]*\}/);
    
    if (jsonMatch) {
      return JSON.parse(jsonMatch[0]);
    }
    
    return {
      intent: 'casual_chat',
      confidence: 50,
      entities: {}
    };
  } catch (error) {
    console.error('意圖識別錯誤:', error);
    return {
      intent: 'unknown',
      confidence: 0,
      entities: {}
    };
  }
}

/**
 * 智能摘要生成器
 * @param {string} content 要摘要的內容
 * @param {number} maxLength 摘要最大長度
 * @returns {string} 摘要內容
 */
function generateIntelligentSummary(content, maxLength = 150) {
  const prompt = `請為以下內容生成簡潔的摘要，用繁體中文，控制在${maxLength}字以內：

內容：
${content.substring(0, 3000)}${content.length > 3000 ? '...(內容已截斷)' : ''}

摘要要求：
1. 抓住核心重點
2. 保持客觀中性
3. 語言簡潔明瞭
4. 突出關鍵信息

摘要：`;

  try {
    return callGemini(prompt, 'general');
  } catch (error) {
    console.error('智能摘要生成錯誤:', error);
    return '無法生成摘要，請檢查內容格式。';
  }
}

/**
 * 多語言翻譯功能
 * @param {string} text 要翻譯的文字
 * @param {string} targetLang 目標語言
 * @returns {string} 翻譯結果
 */
function smartTranslate(text, targetLang = '繁體中文') {
  const prompt = `請將以下文字翻譯成${targetLang}，保持原意並使用自然的表達方式：

原文：
${text}

翻譯：`;

  try {
    return callGemini(prompt, 'general');
  } catch (error) {
    console.error('智能翻譯錯誤:', error);
    return `翻譯失敗：${error.message}`;
  }
}

/**
 * 情感分析功能
 * @param {string} text 要分析的文字
 * @returns {{sentiment: string, confidence: number, keywords: Array}} 情感分析結果
 */
function analyzeSentiment(text) {
  const prompt = `分析以下文字的情感傾向：

文字：「${text}」

請以JSON格式回答：
{
  "sentiment": "positive/negative/neutral",
  "confidence": 信心度(0-100),
  "keywords": ["關鍵情感詞彙"],
  "summary": "簡短分析"
}`;

  try {
    const aiResponse = callGemini(prompt, 'general');
    const jsonMatch = aiResponse.match(/\{[\s\S]*\}/);
    
    if (jsonMatch) {
      return JSON.parse(jsonMatch[0]);
    }
    
    return {
      sentiment: 'neutral',
      confidence: 50,
      keywords: [],
      summary: '無法確定情感傾向'
    };
  } catch (error) {
    console.error('情感分析錯誤:', error);
    return {
      sentiment: 'unknown',
      confidence: 0,
      keywords: [],
      summary: '分析失敗'
    };
  }
}

/**
 * 智能問題建議生成器
 * @param {string} topic 話題
 * @returns {Array<string>} 建議問題清單
 */
function generateSmartQuestions(topic) {
  const prompt = `基於話題「${topic}」，生成5個有意義的相關問題，用繁體中文。

要求：
1. 問題要具體且實用
2. 涵蓋不同角度
3. 適合深入討論
4. 語言自然流暢

請以JSON陣列格式回答：
["問題1", "問題2", "問題3", "問題4", "問題5"]`;

  try {
    const aiResponse = callGemini(prompt, 'general');
    const jsonMatch = aiResponse.match(/\[[\s\S]*\]/);
    
    if (jsonMatch) {
      return JSON.parse(jsonMatch[0]);
    }
    
    return [`關於${topic}的更多資訊？`];
  } catch (error) {
    console.error('智能問題生成錯誤:', error);
    return [`如何了解更多關於${topic}的內容？`];
  }
}

/**
 * 創意寫作助手
 * @param {string} prompt 寫作提示
 * @param {string} style 寫作風格
 * @returns {string} 創意內容
 */
function creativeWritingAssistant(prompt, style = '自然對話') {
  const fullPrompt = `你是一個創意寫作助手。根據以下要求創作內容：

寫作提示：${prompt}
寫作風格：${style}

要求：
1. 用繁體中文創作
2. 內容要有創意和吸引力
3. 符合指定的寫作風格
4. 長度適中，約200-300字

創作內容：`;

  try {
    return callGemini(fullPrompt, 'general');
  } catch (error) {
    console.error('創意寫作錯誤:', error);
    return '很抱歉，創意寫作功能暫時無法使用。';
  }
}

/**
 * 智能學習輔助 - 解釋複雜概念
 * @param {string} concept 要解釋的概念
 * @param {string} level 解釋深度 (basic/intermediate/advanced)
 * @returns {string} 概念解釋
 */
function explainConcept(concept, level = 'basic') {
  const levelMap = {
    'basic': '用簡單易懂的語言，適合初學者',
    'intermediate': '用中等深度，適合有基礎知識的人',
    'advanced': '用專業深入的方式，適合專家'
  };
  
  const prompt = `請解釋「${concept}」這個概念。

解釋要求：
- ${levelMap[level] || levelMap['basic']}
- 用繁體中文
- 包含實際例子
- 結構清晰，易於理解
- 控制在250字以內

解釋：`;

  try {
    return callGemini(prompt, 'general');
  } catch (error) {
    console.error('概念解釋錯誤:', error);
    return `無法解釋「${concept}」概念，請稍後再試。`;
  }
}

/**
 * AI 決策建議系統
 * @param {string} situation 決策情境
 * @param {Array<string>} options 可選選項
 * @returns {object} 決策建議
 */
function provideDecisionAdvice(situation, options = []) {
  const optionsText = options.length > 0 ? 
    `\n可選選項：${options.map((opt, i) => `${i+1}. ${opt}`).join('\n')}` : '';
  
  const prompt = `請為以下決策情境提供建議：

情境：${situation}${optionsText}

請以JSON格式提供建議：
{
  "recommendation": "主要建議",
  "reasoning": "推理過程",
  "pros_cons": {
    "pros": ["優點1", "優點2"],
    "cons": ["缺點1", "缺點2"]
  },
  "alternatives": ["替代方案1", "替代方案2"]
}`;

  try {
    const aiResponse = callGemini(prompt, 'general');
    const jsonMatch = aiResponse.match(/\{[\s\S]*\}/);
    
    if (jsonMatch) {
      return JSON.parse(jsonMatch[0]);
    }
    
    return {
      recommendation: '需要更多資訊才能提供準確建議',
      reasoning: '情境資訊不足',
      pros_cons: { pros: [], cons: [] },
      alternatives: []
    };
  } catch (error) {
    console.error('決策建議錯誤:', error);
    return {
      recommendation: '建議諮詢專業人士',
      reasoning: '系統無法處理此決策情境',
      pros_cons: { pros: [], cons: [] },
      alternatives: ['尋求專業建議', '收集更多資訊']
    };
  }
}
