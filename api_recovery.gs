// 🔧 API Keys 恢復工具 - 直接在 Google Apps Script 中使用

/**
 * 🚨 緊急恢復 API Keys
 * 當 APIKEY 工作表數據丟失時，執行此函數進行恢復
 */
function emergencyRestoreAPIKeys() {
  console.log('🚨 開始緊急恢復 API Keys...');
  
  try {
    const sheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName('APIKEY');
    if (!sheet) {
      throw new Error('找不到 APIKEY 工作表');
    }
    
    // 🔐 您的 API Keys（請在此處填入實際值）
    const apiConfig = {
      lineChannelAccessToken: '8xWtDZ4A1Pb9kkklsD8CEvuwLOvVmnYGp6kKAG+czmW87WNv64uG9dY73u8sUXnz+o59KveR5KzMyWLrKTAMrPTn0mQD9ut5htj9yJvTofet1/rJP4rpyEDqJ6BSQfkD+uwsqogqMKio9uEd/6FEuwdB04t89/1O/w1cDnyilFU=',
      lineChannelSecret: '9fb4df5609b5845b879efb9ea8072d23',
      geminiApiKey: 'AIzaSyCHqxhLA40jPuoZ8SppFVBSyn8zNQNJltE',
      youtubeApiKey: 'AIzaSyDTqyVy_Ni_dWQ_zlsbP6o30oVBJivkmgM',
      googleSearchApiKey: 'AIzaSyDi5-KuMSk3GmYR-oCP5scET-h_k663-lU',
      googleSearchEngineId: 'd7076a73f75ba464f',
      folderId: '1vjius2gRLSvxOmtFhNsS9ALvfkdGOTK-',
      generalModel: 'gemini-2.5-flash',
      visionModel: 'gemini-2.5-pro'
    };
    
    // 執行恢復
    const updates = [
      ['B2', apiConfig.lineChannelAccessToken],
      ['B3', apiConfig.lineChannelSecret],
      ['B4', apiConfig.geminiApiKey],
      ['B5', apiConfig.youtubeApiKey],
      ['B6', ''], // Threads User ID
      ['B7', ''], // Threads Access Token
      ['B8', ''], // Cloudinary Cloud Name
      ['B9', ''], // Cloudinary API Key
      ['B10', ''], // Cloudinary API Secret
      ['B11', apiConfig.folderId],
      ['B12', apiConfig.googleSearchApiKey],
      ['B13', apiConfig.googleSearchEngineId],
      ['B14', apiConfig.generalModel],
      ['B15', apiConfig.visionModel]
    ];
    
    // 批量更新
    let restored = 0;
    updates.forEach(([cell, value]) => {
      if (value && value.trim() !== '') {
        sheet.getRange(cell).setValue(value);
        restored++;
      }
    });
    
    console.log(`✅ 成功恢復 ${restored} 個 API Keys`);
    logActivity('緊急恢復API Keys', `已恢復 ${restored} 個配置項目`);
    
    // 驗證恢復結果
    const testConfig = getConfig();
    console.log('📋 恢復驗證：');
    console.log(`• LINE Token: ${testConfig.lineChannelAccessToken ? '✅ 已恢復' : '❌ 未恢復'}`);
    console.log(`• Gemini API: ${testConfig.geminiApiKey ? '✅ 已恢復' : '❌ 未恢復'}`);
    console.log(`• Drive Folder: ${testConfig.folderId ? '✅ 已恢復' : '❌ 未恢復'}`);
    
    return `✅ 緊急恢復完成！已恢復 ${restored} 個 API Keys`;
    
  } catch (error) {
    console.error('❌ 緊急恢復失敗:', error);
    logActivity('緊急恢復失敗', error.toString());
    return `❌ 恢復失敗: ${error.message}`;
  }
}

/**
 * 🔧 快速診斷 API 配置狀態
 */
function quickDiagnoseAPIConfig() {
  console.log('=== 快速診斷 API 配置 ===');
  
  try {
    const config = getConfig();
    
    // 檢查關鍵配置
    const checks = [
      { name: 'LINE Channel Access Token', value: config.lineChannelAccessToken, critical: true },
      { name: 'LINE Channel Secret', value: config.lineChannelSecret, critical: true },
      { name: 'Gemini API Key', value: config.geminiApiKey, critical: true },
      { name: 'Google Drive Folder ID', value: config.folderId, critical: true },
      { name: 'YouTube API Key', value: config.youtubeApiKey, critical: false },
      { name: 'Google Search API Key', value: config.googleSearchApiKey, critical: false },
      { name: '一般功能模型', value: config.generalModel, critical: true },
      { name: '視覺模型', value: config.visionModel, critical: true }
    ];
    
    let criticalMissing = 0;
    let optionalMissing = 0;
    
    console.log('📊 配置檢查結果：');
    checks.forEach(check => {
      const isSet = check.value && check.value.toString().trim() !== '';
      const status = isSet ? '✅' : '❌';
      const displayValue = isSet ? (check.value.length > 20 ? '***已設定***' : check.value) : '未設定';
      
      console.log(`${status} ${check.name}: ${displayValue}`);
      
      if (!isSet) {
        if (check.critical) {
          criticalMissing++;
        } else {
          optionalMissing++;
        }
      }
    });
    
    // 測試 Gemini API 連接
    if (config.geminiApiKey) {
      try {
        const testResponse = callGemini('測試', 'general');
        console.log('✅ Gemini API 連接測試成功');
      } catch (apiError) {
        console.log('❌ Gemini API 連接測試失敗:', apiError.message);
        criticalMissing++;
      }
    }
    
    // 生成總結
    console.log('\n📋 診斷總結：');
    if (criticalMissing === 0) {
      console.log('🎉 所有關鍵配置都正常！');
    } else {
      console.log(`🚨 發現 ${criticalMissing} 個關鍵配置問題`);
      console.log('💡 建議執行 emergencyRestoreAPIKeys() 進行恢復');
    }
    
    if (optionalMissing > 0) {
      console.log(`⚠️ ${optionalMissing} 個可選配置未設定`);
    }
    
    return {
      criticalMissing,
      optionalMissing,
      healthy: criticalMissing === 0
    };
    
  } catch (error) {
    console.error('❌ 診斷失敗:', error);
    return { error: error.message };
  }
}

/**
 * 🔄 創建 APIKEY 工作表備份
 */
function createAPIKeyBackup() {
  try {
    const ss = SpreadsheetApp.getActiveSpreadsheet();
    const apikeySheet = ss.getSheetByName('APIKEY');
    
    if (!apikeySheet) {
      return '❌ APIKEY 工作表不存在';
    }
    
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').substring(0, 19);
    const backupName = `APIKEY_備份_${timestamp}`;
    
    // 創建備份
    const backupSheet = apikeySheet.copyTo(ss);
    backupSheet.setName(backupName);
    
    console.log(`✅ 已創建 APIKEY 備份: ${backupName}`);
    logActivity('創建APIKEY備份', `備份名稱: ${backupName}`);
    
    return `✅ 備份創建成功: ${backupName}`;
    
  } catch (error) {
    console.error('❌ 創建備份失敗:', error);
    return `❌ 備份失敗: ${error.message}`;
  }
}

/**
 * 🧪 測試所有 API 功能
 */
function testAllAPIFunctions() {
  console.log('=== 測試所有 API 功能 ===');
  
  try {
    // 1. 診斷配置
    const diagnosis = quickDiagnoseAPIConfig();
    
    if (!diagnosis.healthy) {
      console.log('⚠️ 配置不完整，建議先執行 emergencyRestoreAPIKeys()');
      return diagnosis;
    }
    
    // 2. 測試 Gemini API
    console.log('\n🤖 測試 Gemini API...');
    try {
      const response = callGemini('請簡短回答：你好', 'general');
      console.log(`✅ Gemini API 正常: ${response.substring(0, 30)}...`);
    } catch (e) {
      console.log(`❌ Gemini API 失敗: ${e.message}`);
    }
    
    // 3. 測試活動日誌
    console.log('\n📊 測試活動日誌...');
    try {
      logActivity('系統測試', '完整 API 功能測試');
      console.log('✅ 活動日誌正常');
    } catch (e) {
      console.log(`❌ 活動日誌失敗: ${e.message}`);
    }
    
    // 4. 測試模型配置
    console.log('\n🔧 測試模型配置...');
    try {
      const config = getConfig();
      const generalVersion = getModelApiVersion(config.generalModel);
      const visionVersion = getModelApiVersion(config.visionModel);
      console.log(`✅ 模型配置正常: ${config.generalModel} (${generalVersion}), ${config.visionModel} (${visionVersion})`);
    } catch (e) {
      console.log(`❌ 模型配置失敗: ${e.message}`);
    }
    
    console.log('\n🎉 完整測試完成！');
    return '✅ 所有 API 功能測試完成';
    
  } catch (error) {
    console.error('❌ 測試過程失敗:', error);
    return `❌ 測試失敗: ${error.message}`;
  }
}
