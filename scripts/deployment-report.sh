#!/bin/bash

# LINE Bot 部署報告生成腳本
# 避免特殊字符導致的 shell 解析問題

echo "=== 📊 LINE Bot 完整部署報告 ==="

# 安全獲取基本信息
SCRIPT_ID=$(jq -r '.scriptId' .clasp.json 2>/dev/null || echo "unknown")
DEPLOY_TIME=$(date '+%Y-%m-%d %H:%M:%S UTC')
COMMIT_SHA_SHORT=$(echo "${GITHUB_SHA:-unknown}" | cut -c1-7)

# 計算檔案數量
FILE_COUNT=$(find . -maxdepth 1 \( -name "*.gs" -o -name "*.html" -o -name "appsscript.json" \) 2>/dev/null | wc -l)

echo "🎉 LINE Bot 部署完成時間: $DEPLOY_TIME"
echo "📝 專案 ID: $SCRIPT_ID"
echo "🎯 部署 ID: ${GAS_DEPLOYMENT_ID:-未設定}"
echo "📦 版本號: ${VERSION_NUMBER:-未知}"
echo "📝 Commit: $COMMIT_SHA_SHORT"
echo "💬 Commit 訊息: 語音聊天意圖檢測修復 - 修復 thinking 模型路由問題"
echo "📊 推送檔案數量: $FILE_COUNT"
echo "✅ 認證方式: OAuth"
echo ""
echo "📋 LINE Bot 自動化流程狀態:"
echo "   ✅ 代碼推送: 成功"
echo "   ✅ 版本創建: 成功"
echo "   ✅ 部署更新: 成功"
echo "   ✅ Webhook URL 固定: 是"
echo "   ✅ 立即生效: 是"
echo "   ✅ 需要手動操作: 否"
echo ""
echo "🤖 您的 LINE Bot 智能助理已自動更新並上線！"
echo "📱 用戶可以立即使用新版本功能"
echo ""
echo "🔧 本次修復重點："
echo "   • 修復語音聊天意圖檢測錯誤"
echo "   • 強化 thinking 模型路由邏輯"
echo "   • 確保語音對話功能持續可用"
echo "   • 支援更多語音聊天表達方式"
