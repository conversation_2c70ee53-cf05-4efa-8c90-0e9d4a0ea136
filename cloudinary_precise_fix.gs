// == 立即修復 Cloudinary 簽名問題 ==
// 🎯 基於錯誤信息分析的精確修復

/**
 * 🔍 分析 Cloudinary 錯誤信息
 * 
 * 從錯誤信息可以看出：
 * Cloudinary 期望的簽名字符串：'format=m4a&public_id=linebot/tts/audio_1751102746686&timestamp=1751102746'
 * 我們生成的簽名字符串包含：'format=m4a&public_id=test/signature_fix&resource_type=video&timestamp=1751067835'
 * 
 * 問題：Cloudinary 的簽名驗證中缺少了 resource_type=video 參數！
 */
function analyzeCloudinaryError_debug() {
  console.log('🔍 === 分析 Cloudinary 錯誤信息 ===');
  
  console.log('📋 錯誤分析:');
  console.log('   Cloudinary 期望: format=m4a&public_id=xxx&timestamp=xxx');
  console.log('   我們生成: format=m4a&public_id=xxx&resource_type=video&timestamp=xxx');
  console.log('');
  console.log('🎯 發現問題: resource_type 參數處理不一致');
  console.log('');
  
  console.log('💡 可能的原因:');
  console.log('   1. 表單數據和簽名參數不匹配');
  console.log('   2. Cloudinary 對 video 上傳端點的簽名要求不同');
  console.log('   3. resource_type 不應該包含在簽名中');
  console.log('');
  
  return {
    issue: 'resource_type 參數不一致',
    solution: '調整簽名生成以匹配 Cloudinary 期望'
  };
}

/**
 * 🔧 修復的 Cloudinary 音頻上傳函數
 * 基於錯誤信息調整簽名生成
 */
function uploadAudioToCloudinaryFixed(audioBlob, originalText) {
  const config = getConfig();
  
  // 檢查 Cloudinary 配置
  if (!config.cloudinaryCloudName || !config.cloudinaryApiKey || !config.cloudinaryApiSecret) {
    throw new Error('請在 APIKEY 工作表中設定 Cloudinary 配置（Cloud Name, API Key, API Secret）');
  }
  
  try {
    console.log(`🌩️ 上傳音頻到 Cloudinary (修復版)...`);
    
    // 生成唯一的檔案名稱
    const timestamp = new Date().getTime();
    const publicId = `linebot/tts/audio_${timestamp}`;
    
    // 🔧 關鍵修復：根據 Cloudinary 錯誤信息調整簽名參數
    // 只包含 Cloudinary 實際驗證的參數
    const signatureParams = {
      format: 'm4a',
      public_id: publicId,
      timestamp: Math.floor(Date.now() / 1000)
      // 注意：不包含 resource_type，因為 Cloudinary 錯誤信息中沒有
    };
    
    console.log('🔐 簽名參數 (修復版):', signatureParams);
    
    // 生成簽名
    const signature = generateCloudinarySignature(signatureParams, config.cloudinaryApiSecret);
    
    // 🔧 表單數據：包含所有上傳需要的參數
    const formData = {
      file: audioBlob,
      public_id: publicId,
      resource_type: 'video',  // 表單中包含，但簽名中不包含
      format: 'm4a',
      api_key: config.cloudinaryApiKey,
      timestamp: String(signatureParams.timestamp),
      signature: signature
    };
    
    console.log('📤 表單數據 (修復版):');
    Object.keys(formData).forEach(key => {
      if (key !== 'file' && key !== 'signature') {
        console.log(`   ${key}: "${formData[key]}"`);
      } else if (key === 'signature') {
        console.log(`   ${key}: ${formData[key].substring(0, 20)}...`);
      }
    });
    
    // 上傳到 Cloudinary
    const uploadUrl = `https://api.cloudinary.com/v1_1/${config.cloudinaryCloudName}/video/upload`;
    
    const response = UrlFetchApp.fetch(uploadUrl, {
      method: 'POST',
      payload: formData,
      muteHttpExceptions: true
    });
    
    const responseCode = response.getResponseCode();
    const responseText = response.getContentText();
    
    console.log(`📊 Cloudinary 回應代碼: ${responseCode}`);
    
    if (responseCode !== 200) {
      console.log(`❌ Cloudinary 錯誤詳情: ${responseText}`);
      throw new Error(`Cloudinary 上傳失敗: ${responseCode} - ${responseText}`);
    }
    
    const result = JSON.parse(responseText);
    
    if (!result.secure_url) {
      throw new Error('Cloudinary 未返回有效的 URL');
    }
    
    // 🎯 確保返回 M4A 格式的 URL
    let m4aUrl = result.secure_url;
    if (!m4aUrl.endsWith('.m4a')) {
      m4aUrl = m4aUrl.replace(/\.[^.]+$/, '.m4a');
    }
    
    console.log(`✅ Cloudinary 上傳成功: ${m4aUrl}`);
    logActivity('Cloudinary音頻上傳', `公開ID: ${publicId}, URL: ${m4aUrl}`);
    
    return m4aUrl;
    
  } catch (error) {
    console.error('Cloudinary 上傳錯誤:', error);
    throw error;
  }
}

/**
 * 🧪 測試修復的上傳函數
 */
function testFixedCloudinaryUpload_debug() {
  console.log('🧪 === 測試修復的 Cloudinary 上傳 ===');
  
  try {
    // 分析錯誤
    analyzeCloudinaryError_debug();
    
    console.log('🔧 測試修復的上傳函數...');
    
    // 創建測試音頻檔案
    const testAudioData = new ArrayBuffer(1000);
    const testBlob = Utilities.newBlob(new Uint8Array(testAudioData), 'audio/wav', 'test_audio.wav');
    
    console.log(`📁 測試檔案大小: ${testBlob.getBytes().length} bytes`);
    
    // 使用修復的上傳函數
    const uploadResult = uploadAudioToCloudinaryFixed(testBlob, '修復測試');
    
    console.log('🎉 修復成功！');
    console.log(`✅ 上傳 URL: ${uploadResult}`);
    
    return {
      success: true,
      url: uploadResult,
      message: 'Cloudinary 上傳修復成功'
    };
    
  } catch (error) {
    console.error('❌ 修復測試失敗:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 🔄 更新主要上傳函數
 * 將修復應用到原有函數
 */
function applyCloudinaryFix_debug() {
  console.log('🔄 === 應用 Cloudinary 修復 ===');
  
  console.log('📝 修復重點:');
  console.log('   1. 簽名參數中移除 resource_type');
  console.log('   2. 保持表單數據中的 resource_type');
  console.log('   3. 確保參數順序與 Cloudinary 期望一致');
  console.log('');
  
  console.log('💡 需要更新的函數:');
  console.log('   - uploadAudioToCloudinary()');
  console.log('   - uploadImageToCloudinary() (如果有同樣問題)');
  console.log('');
  
  console.log('✅ 修復已實現在 uploadAudioToCloudinaryFixed() 函數中');
  console.log('🔧 請運行 testFixedCloudinaryUpload_debug() 驗證修復效果');
  
  return {
    success: true,
    message: '修復方案已準備就緒'
  };
}
