// == 工作表管理模組 ==
// 專門處理所有 Google Sheets 工作表的初始化和管理

// 1. 自動建立所有必要工作表
function initializeSheets() {
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  const sheetNames = [
    '活動日誌', '摘要日誌', '筆記暫存', '檔案日誌', '地點日誌',
    'APIKEY', 'Threads發文紀錄', 'Sheet1', 'TempSearch',
    // 記憶系統工作表
    '用戶對話歷史', '檔案記憶庫', '分析任務佇列',
    // 群組功能工作表
    '群組發言記錄'
  ];
  
  sheetNames.forEach(name => {
    if (!ss.getSheetByName(name)) {
      const newSheet = ss.insertSheet(name);
      console.log(`已建立工作表: ${name}`);
      
      // 設定新工作表的標題行
      setupSheetHeaders(newSheet, name);
    }
  });
  
  // 設定 APIKEY 工作表結構
  const apikeySheet = ss.getSheetByName('APIKEY');
  if (apikeySheet && apikeySheet.getRange('A1').getValue() === '') {
    setupAPIKEYSheet(apikeySheet);
  }
}

// 2. 設定工作表標題行
function setupSheetHeaders(sheet, sheetName) {
  // 先檢查是否是記憶系統工作表
  if (['用戶對話歷史', '檔案記憶庫', '分析任務佇列'].includes(sheetName)) {
    setupMemorySheetHeaders(sheet, sheetName);
    return;
  }
  
  // 原有的其他工作表設定
  let headers = [];
  switch(sheetName) {
    case '活動日誌':
      headers = ['時間戳', '動作', '詳細資訊'];
      break;
    case '檔案日誌':
      headers = ['時間戳', '用戶ID', '檔案名稱', '檔案連結', '檔案大小', '媒體類型', '來源類型'];
      break;
    case '筆記暫存':
      headers = ['時間戳', '用戶ID', '來源類型', '筆記內容'];
      break;
    case 'Threads發文紀錄':
      headers = ['時間戳', '用戶ID', '發文內容', '發文狀態', '回應訊息'];
      break;
    case '摘要日誌':
      headers = ['時間戳', '事件類型', '摘要內容', '相關用戶'];
      break;
    case '地點日誌':
      headers = ['時間戳', '用戶ID', '位置名稱', '經度', '緯度', '地址'];
      break;
    case 'TempSearch':
      headers = ['時間戳', '搜尋關鍵字', '搜尋結果', '用戶ID'];
      break;
    case '群組發言記錄':
      headers = ['時間戳', '群組ID', '用戶ID', '用戶顯示名稱', '訊息內容', '訊息類型', '回應狀態'];
      break;
    default:
      return; // 其他工作表不設定標題
  }
  
  if (headers.length > 0) {
    sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
    sheet.getRange(1, 1, 1, headers.length).setFontWeight('bold');
    sheet.getRange(1, 1, 1, headers.length).setBackground('#e1f5fe');
    console.log(`已設定 ${sheetName} 工作表標題行`);
  }
}

// 3. 設定 APIKEY 工作表結構
function setupAPIKEYSheet(apikeySheet) {
  const headers = [
    ['API 項目說明', '請在此填入實際值', '備註'],
    ['LINE Channel Access Token', '', ''],
    ['LINE Channel Secret', '', ''],
    ['Gemini API Key', '', ''],
    ['YouTube Data API Key', '', ''],
    ['Threads User ID', '', ''],
    ['Threads Access Token', '', ''],
    ['Cloudinary Cloud Name', '', ''],
    ['Cloudinary API Key', '', ''],
    ['Cloudinary API Secret', '', ''],
    ['Google Drive Folder ID', '1vjius2gRLSvxOmtFhNsS9ALvfkdGOTK-', ''],
    ['Google Search API Key', '', '用於AI智能搜尋'],
    ['Google Search Engine ID', '', '用於AI智能搜尋'],
    ['一般功能模型', 'gemini-1.5-flash', '用於文字對話、摘要、搜尋判斷等'],
    ['圖片分析模型', 'gemini-1.5-pro', '專用於圖片、視覺內容分析']
  ];
  
  apikeySheet.getRange(1, 1, headers.length, 3).setValues(headers);
  apikeySheet.getRange('A1:A15').setFontWeight('bold');
  apikeySheet.getRange('B2:B15').setBackground('#f0f8ff');
  
  // 設定模型選擇下拉選單
  const modelRule = SpreadsheetApp.newDataValidation()
    .requireValueInList(['gemini-1.5-flash', 'gemini-1.5-pro'], true)
    .setAllowInvalid(true)
    .setHelpText('請從清單選擇或手動輸入新模型名稱。')
    .build();
  apikeySheet.getRange('B14:B15').setDataValidation(modelRule);
  
  console.log('已設定 APIKEY 工作表結構（包含模型選擇）');
}

// 4. 清理工作表數據（維護功能）
function cleanupWorksheets() {
  try {
    const ss = SpreadsheetApp.getActiveSpreadsheet();
    const cleanupConfig = {
      '活動日誌': { maxRows: 1000, keepRows: 500 },
      '檔案日誌': { maxRows: 2000, keepRows: 1000 },
      '用戶對話歷史': { maxRows: 5000, keepRows: 2500 },
      '筆記暫存': { maxRows: 1000, keepRows: 500 },
      '群組發言記錄': { maxRows: 5000, keepRows: 2500 }
    };
    
    Object.entries(cleanupConfig).forEach(([sheetName, config]) => {
      const sheet = ss.getSheetByName(sheetName);
      if (sheet && sheet.getLastRow() > config.maxRows) {
        const rowsToDelete = sheet.getLastRow() - config.keepRows;
        sheet.deleteRows(2, rowsToDelete); // 從第2行開始刪除（保留標題行）
        console.log(`已清理 ${sheetName}：刪除 ${rowsToDelete} 行舊資料`);
      }
    });
    
    return `✅ 工作表清理完成`;
  } catch (error) {
    console.error('清理工作表錯誤:', error);
    return `❌ 工作表清理失敗：${error.message}`;
  }
}

// 5. 檢查工作表健康狀態
function checkSheetsHealth() {
  try {
    const ss = SpreadsheetApp.getActiveSpreadsheet();
    const requiredSheets = [
      '活動日誌', '摘要日誌', '筆記暫存', '檔案日誌', '地點日誌',
      'APIKEY', 'Threads發文紀錄', '用戶對話歷史', '檔案記憶庫', '分析任務佇列',
      '群組發言記錄'
    ];
    
    const healthReport = {
      totalSheets: requiredSheets.length,
      existingSheets: 0,
      missingSheets: [],
      sheetDetails: {}
    };
    
    requiredSheets.forEach(sheetName => {
      const sheet = ss.getSheetByName(sheetName);
      if (sheet) {
        healthReport.existingSheets++;
        healthReport.sheetDetails[sheetName] = {
          exists: true,
          rows: sheet.getLastRow(),
          columns: sheet.getLastColumn()
        };
      } else {
        healthReport.missingSheets.push(sheetName);
        healthReport.sheetDetails[sheetName] = {
          exists: false,
          rows: 0,
          columns: 0
        };
      }
    });
    
    console.log('工作表健康檢查完成:', healthReport);
    return healthReport;
  } catch (error) {
    console.error('工作表健康檢查錯誤:', error);
    return { error: error.toString() };
  }
}

// 6. 備份重要工作表數據
function backupImportantSheets() {
  try {
    const ss = SpreadsheetApp.getActiveSpreadsheet();
    const timestamp = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
    const backupSheets = ['APIKEY', '檔案記憶庫', '用戶對話歷史', '群組發言記錄'];
    
    backupSheets.forEach(sheetName => {
      const originalSheet = ss.getSheetByName(sheetName);
      if (originalSheet) {
        const backupName = `${sheetName}_備份_${timestamp}`;
        
        // 檢查是否已存在備份
        if (!ss.getSheetByName(backupName)) {
          const backupSheet = originalSheet.copyTo(ss);
          backupSheet.setName(backupName);
          console.log(`已建立備份: ${backupName}`);
        }
      }
    });
    
    return `✅ 重要工作表備份完成 (${timestamp})`;
  } catch (error) {
    console.error('備份工作表錯誤:', error);
    return `❌ 備份失敗：${error.message}`;
  }
}

// 7. 🧪 測試函數：初始化所有工作表
function testInitializeAllSheets() {
  console.log('=== 測試工作表初始化 ===');
  
  try {
    initializeSheets();
    const healthReport = checkSheetsHealth();
    
    console.log(`✅ 工作表初始化測試完成`);
    console.log(`📊 存在的工作表: ${healthReport.existingSheets}/${healthReport.totalSheets}`);
    
    if (healthReport.missingSheets.length > 0) {
      console.log(`❌ 缺少的工作表: ${healthReport.missingSheets.join(', ')}`);
    }
    
    return healthReport;
  } catch (error) {
    console.error('❌ 工作表初始化測試失敗:', error);
    return { error: error.toString() };
  }
}

// 8. 🧪 測試函數：清理所有工作表
function testCleanupAllSheets() {
  console.log('=== 測試工作表清理 ===');
  
  try {
    const result = cleanupWorksheets();
    console.log(result);
    return result;
  } catch (error) {
    console.error('❌ 工作表清理測試失敗:', error);
    return `❌ 清理測試失敗：${error.message}`;
  }
}
