// == 媒體處理模組 ==
// 專門處理圖片、影片、音訊、檔案等媒體訊息的處理和分析

// 1. 處理媒體訊息主函數（支援不回應模式）
function handleMediaMessage(message, replyToken, userId, sourceType) {
  try {
    const config = getConfig();
    
    if (!config.folderId) {
      // 只有當有 replyToken 時才回應錯誤訊息
      if (replyToken) {
        replyMessage(replyToken, '❌ 請先設定 Google Drive 資料夾 ID');
      }
      return;
    }
    
    const messageId = message.id;
    const messageType = message.type;
    
    // 記錄嘗試處理的檔案類型
    logActivity('處理媒體', `類型: ${messageType}, ID: ${messageId}, 用戶: ${userId}, 來源: ${sourceType}, 回應模式: ${replyToken ? '回應' : '不回應'}`);
    
    // 下載和處理檔案
    const processResult = downloadAndProcessMedia(message, config);
    if (!processResult.success) {
      if (replyToken) {
        replyMessage(replyToken, processResult.errorMessage);
      }
      return;
    }
    
    // 記錄到檔案記憶庫（使用 Memory_System.gs 的記憶中間件）
    memoryMiddleware('record_file', {
      userId: userId,
      fileId: processResult.fileId,
      fileName: processResult.fileName,
      fileType: messageType,
      fileSize: processResult.fileSize,
      description: processResult.description,
      features: processResult.features,
      fileUrl: processResult.fileUrl,
      sourceType: sourceType
    });
    
    // 🔧 修正：只有當 replyToken 存在時才回應
    if (replyToken) {
      const responseText = generateMediaResponse(processResult, messageType);
      replyMessage(replyToken, responseText);
      logActivity('媒體回覆', `回覆給${userId}(${sourceType}): 檔案處理完成`);
    } else {
      // 群組中不回應但記錄處理結果
      logActivity('媒體靜默處理', `已處理並記錄 ${userId}(${sourceType}) 的 ${messageType}，檔案: ${processResult.fileName}`);
    }
    
    // 記錄檔案日誌（無論是否回應都要記錄）
    logFileActivity(userId, processResult.fileName, processResult.fileUrl, processResult.fileSize, messageType, sourceType);
    
  } catch (error) {
    console.error('處理媒體錯誤:', error);
    logActivity('處理媒體錯誤', `類型: ${message.type}, 錯誤: ${error.toString()}`);
    
    // 只有當 replyToken 存在時才回應錯誤訊息
    if (replyToken) {
      replyMessage(replyToken, `❌ ${getMediaTypeDisplay(message.type)}處理失敗\n\n錯誤詳情：${error.message}\n\n可能原因：\n1. 檔案類型不受 LINE Bot 支援\n2. 檔案大小超過限制\n3. 網路連接問題`);
    }
  }
}

// 2. 下載和處理媒體檔案
function downloadAndProcessMedia(message, config) {
  try {
    const messageId = message.id;
    const messageType = message.type;
    
    // 從 LINE 下載檔案
    let blob;
    try {
      blob = UrlFetchApp.fetch(`https://api-data.line.me/v2/bot/message/${messageId}/content`, {
        headers: {
          Authorization: 'Bearer ' + config.lineChannelAccessToken
        }
      }).getBlob();
    } catch (downloadError) {
      logActivity('媒體下載錯誤', `類型: ${messageType}, 錯誤: ${downloadError.toString()}`);
      return {
        success: false,
        errorMessage: `❌ 無法下載檔案：${downloadError.message}`
      };
    }
    
    // 生成檔案名稱
    const fileName = generateFileName(messageType, message.fileName);
    
    // 設定檔案名稱並儲存到 Google Drive
    blob.setName(fileName);
    const file = DriveApp.getFolderById(config.folderId).createFile(blob);
    
    // 基本檔案資訊
    const finalFileName = file.getName();
    const fileSize = (file.getSize() / 1024).toFixed(2) + ' KB';
    const fileUrl = file.getUrl();
    const mimeType = blob.getContentType();
    const fileId = file.getId();
    
    // 檔案類型資訊
    const fileTypeInfo = getFileTypeInfo(finalFileName, mimeType);
    
    // 生成簡短描述和關鍵特徵
    let description = `${fileTypeInfo.description}`;
    let features = `類型:${messageType}, 大小:${fileSize}`;
    
    // 立即進行簡單分析獲取特徵
    if (config.geminiApiKey && messageType === 'image') {
      try {
        const quickAnalysis = callGemini("請用繁體中文簡潔描述這張圖片的內容。", 'vision', blob);
        description = quickAnalysis.substring(0, 100) + (quickAnalysis.length > 100 ? '...' : '');
        features += `, 內容:${quickAnalysis.substring(0, 50)}`;
      } catch (error) {
        console.log('快速分析失敗:', error);
      }
    }
    
    return {
      success: true,
      fileId: fileId,
      fileName: finalFileName,
      fileSize: fileSize,
      fileUrl: fileUrl,
      mimeType: mimeType,
      fileTypeInfo: fileTypeInfo,
      description: description,
      features: features
    };
    
  } catch (error) {
    return {
      success: false,
      errorMessage: `❌ 檔案處理失敗：${error.message}`
    };
  }
}

// 3. 生成檔案名稱
function generateFileName(messageType, originalFileName = null) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  
  switch(messageType) {
    case 'image':
      return `image_${timestamp}.jpg`;
    case 'video':
      return `video_${timestamp}.mp4`;
    case 'audio':
      return `audio_${timestamp}.m4a`;
    case 'file':
      return originalFileName || `file_${timestamp}`;
    default:
      return `media_${timestamp}`;
  }
}

// 4. 生成媒體回應訊息
function generateMediaResponse(processResult, messageType) {
  let responseText = `✅ ${getMediaTypeDisplay(messageType)}已成功儲存！\n\n📁 檔案名稱：${processResult.fileName}\n📊 檔案大小：${processResult.fileSize}\n${processResult.fileTypeInfo.icon} 檔案類型：${processResult.fileTypeInfo.description}\n🔗 檔案連結：${processResult.fileUrl}`;
  
  // 提供延遲分析選項
  responseText += `\n\n🧠 記憶功能：已記錄到檔案記憶庫\n💡 您可以稍後說：「剛才那個${processResult.fileTypeInfo.description}如何？」\n或輸入「分析 ${processResult.fileName}」進行詳細分析`;
  
  return responseText;
}

// 5. 批次處理媒體檔案
function batchProcessMediaFiles(fileList, userId, sourceType) {
  try {
    const results = [];
    const config = getConfig();
    
    fileList.forEach((fileInfo, index) => {
      try {
        logActivity('批次處理媒體', `處理第 ${index + 1}/${fileList.length} 個檔案: ${fileInfo.fileName}`);
        
        // 模擬 LINE message 格式
        const mockMessage = {
          id: fileInfo.messageId || `batch_${index}`,
          type: fileInfo.type || 'file',
          fileName: fileInfo.fileName
        };
        
        // 處理單個檔案（不回應）
        handleMediaMessage(mockMessage, null, userId, sourceType);
        
        results.push({
          fileName: fileInfo.fileName,
          status: 'success',
          message: '處理完成'
        });
        
      } catch (error) {
        results.push({
          fileName: fileInfo.fileName || '未知檔案',
          status: 'error',
          message: error.message
        });
      }
    });
    
    const successCount = results.filter(r => r.status === 'success').length;
    const errorCount = results.filter(r => r.status === 'error').length;
    
    logActivity('批次處理完成', `成功: ${successCount}, 失敗: ${errorCount}`);
    
    return {
      total: fileList.length,
      successful: successCount,
      failed: errorCount,
      results: results
    };
    
  } catch (error) {
    console.error('批次處理媒體檔案錯誤:', error);
    return {
      total: fileList.length,
      successful: 0,
      failed: fileList.length,
      error: error.message
    };
  }
}

// 6. 媒體檔案安全檢查
function checkMediaSafety(blob, fileName, messageType) {
  try {
    const safetyReport = {
      isSafe: true,
      warnings: [],
      risks: [],
      recommendations: []
    };
    
    // 檔案大小檢查
    const fileSizeKB = blob.getBytes().length / 1024;
    if (fileSizeKB > 50 * 1024) { // 50MB
      safetyReport.warnings.push('檔案大小較大，處理可能較慢');
      safetyReport.recommendations.push('建議壓縮檔案後再上傳');
    }
    
    // MIME 類型檢查
    const mimeType = blob.getContentType();
    const riskyMimeTypes = [
      'application/x-executable',
      'application/x-msdownload',
      'application/x-msdos-program',
      'application/x-sh',
      'application/x-csh'
    ];
    
    if (riskyMimeTypes.some(type => mimeType.includes(type))) {
      safetyReport.isSafe = false;
      safetyReport.risks.push('可執行檔案類型，存在安全風險');
    }
    
    // 檔案名稱檢查
    const suspiciousExtensions = ['.exe', '.bat', '.cmd', '.scr', '.vbs', '.js', '.jar'];
    const hasRiskyExtension = suspiciousExtensions.some(ext => 
      fileName.toLowerCase().endsWith(ext)
    );
    
    if (hasRiskyExtension) {
      safetyReport.warnings.push('檔案副檔名可能具有風險');
      safetyReport.recommendations.push('建議謹慎處理此類型檔案');
    }
    
    // 圖片特定檢查
    if (messageType === 'image') {
      // 檢查圖片是否過大
      if (fileSizeKB > 10 * 1024) { // 10MB
        safetyReport.warnings.push('圖片檔案較大，可能影響處理速度');
      }
    }
    
    return safetyReport;
    
  } catch (error) {
    console.error('媒體安全檢查錯誤:', error);
    return {
      isSafe: false,
      warnings: ['安全檢查過程出現錯誤'],
      risks: ['無法確定檔案安全性'],
      recommendations: ['建議謹慎處理']
    };
  }
}

// 7. 生成媒體處理報告
function generateMediaProcessingReport(userId, timeRange = null) {
  try {
    const recentFiles = getRecentFiles(userId, timeRange, 100);
    const mediaFiles = recentFiles.filter(file => 
      ['image', 'video', 'audio', 'file'].includes(file.fileType)
    );
    
    if (mediaFiles.length === 0) {
      return '📊 媒體處理報告：目前沒有媒體檔案記錄';
    }
    
    // 統計資料
    const stats = {
      totalFiles: mediaFiles.length,
      byType: {},
      totalSizeMB: 0,
      avgSizeKB: 0,
      processingTimes: []
    };
    
    // 按類型統計
    mediaFiles.forEach(file => {
      const type = file.fileType;
      stats.byType[type] = (stats.byType[type] || 0) + 1;
      
      // 計算總大小
      const sizeKB = parseFloat(file.fileSize.replace(' KB', ''));
      if (!isNaN(sizeKB)) {
        stats.totalSizeMB += sizeKB / 1024;
      }
    });
    
    stats.avgSizeKB = (stats.totalSizeMB * 1024) / mediaFiles.length;
    
    // 生成報告
    let report = `📊 媒體處理報告\n\n`;
    report += `📁 總檔案數：${stats.totalFiles}\n`;
    report += `💾 總大小：${stats.totalSizeMB.toFixed(2)} MB\n`;
    report += `📏 平均大小：${stats.avgSizeKB.toFixed(2)} KB\n\n`;
    
    report += `🎯 檔案類型分布：\n`;
    Object.entries(stats.byType)
      .sort((a, b) => b[1] - a[1])
      .forEach(([type, count]) => {
        const displayType = getMediaTypeDisplay(type);
        const percentage = ((count / stats.totalFiles) * 100).toFixed(1);
        report += `• ${displayType}: ${count}個 (${percentage}%)\n`;
      });
    
    // 最近處理的檔案
    if (mediaFiles.length > 0) {
      report += `\n📋 最近處理的檔案：\n`;
      mediaFiles.slice(0, 3).forEach((file, index) => {
        const timeAgo = formatTimeAgo(file.timestamp);
        report += `${index + 1}. ${file.fileName} (${timeAgo})\n`;
      });
    }
    
    return report;
    
  } catch (error) {
    console.error('生成媒體處理報告錯誤:', error);
    return `📊 報告生成失敗：${error.message}`;
  }
}

// 8. 媒體檔案預處理
function preprocessMediaFile(blob, messageType) {
  try {
    const preprocessResult = {
      success: true,
      processedBlob: blob,
      modifications: [],
      warnings: []
    };
    
    // 圖片預處理
    if (messageType === 'image') {
      const imageSize = blob.getBytes().length;
      
      // 如果圖片過大，添加警告
      if (imageSize > 5 * 1024 * 1024) { // 5MB
        preprocessResult.warnings.push('圖片檔案較大，分析可能需要較長時間');
      }
      
      // 檢查圖片格式
      const mimeType = blob.getContentType();
      if (!mimeType.includes('image/')) {
        preprocessResult.warnings.push('檔案MIME類型與圖片不符');
      }
    }
    
    // 影片預處理
    if (messageType === 'video') {
      preprocessResult.warnings.push('影片檔案暫不支援內容分析，僅進行儲存');
    }
    
    // 音訊預處理
    if (messageType === 'audio') {
      preprocessResult.warnings.push('音訊檔案暫不支援內容分析，僅進行儲存');
    }
    
    return preprocessResult;
    
  } catch (error) {
    console.error('媒體預處理錯誤:', error);
    return {
      success: false,
      error: error.message,
      processedBlob: blob,
      modifications: [],
      warnings: ['預處理過程發生錯誤']
    };
  }
}

// 9. 🧪 測試函數：媒體處理流程
function testMediaProcessing() {
  console.log('=== 測試媒體處理流程 ===');
  
  try {
    // 模擬測試數據
    const mockFileList = [
      { fileName: 'test_image.jpg', type: 'image', messageId: 'mock_1' },
      { fileName: 'test_document.pdf', type: 'file', messageId: 'mock_2' },
      { fileName: 'test_video.mp4', type: 'video', messageId: 'mock_3' }
    ];
    
    console.log(`📁 準備處理 ${mockFileList.length} 個測試檔案`);
    
    // 測試檔案名稱生成
    mockFileList.forEach((file, index) => {
      const generatedName = generateFileName(file.type, file.fileName);
      console.log(`${index + 1}. ${file.fileName} -> ${generatedName}`);
    });
    
    console.log('✅ 媒體處理流程測試完成');
    
  } catch (error) {
    console.error('❌ 媒體處理測試失敗:', error);
  }
}

// 10. 🧪 測試函數：媒體安全檢查
function testMediaSafetyCheck() {
  console.log('=== 測試媒體安全檢查 ===');
  
  try {
    // 模擬不同類型的檔案名稱進行測試
    const testFiles = [
      { name: 'normal_image.jpg', type: 'image' },
      { name: 'suspicious_file.exe', type: 'file' },
      { name: 'large_video.mp4', type: 'video' },
      { name: 'script_file.bat', type: 'file' }
    ];
    
    testFiles.forEach((testFile, index) => {
      console.log(`測試 ${index + 1}: ${testFile.name}`);
      
      // 創建模擬 blob（實際使用中會是真實檔案）
      const mockBlob = Utilities.newBlob('test content', 'text/plain', testFile.name);
      const safetyReport = checkMediaSafety(mockBlob, testFile.name, testFile.type);
      
      console.log(`  安全性: ${safetyReport.isSafe ? '✅ 安全' : '❌ 有風險'}`);
      if (safetyReport.warnings.length > 0) {
        console.log(`  警告: ${safetyReport.warnings.join(', ')}`);
      }
      if (safetyReport.risks.length > 0) {
        console.log(`  風險: ${safetyReport.risks.join(', ')}`);
      }
      console.log('---');
    });
    
    console.log('✅ 媒體安全檢查測試完成');
    
  } catch (error) {
    console.error('❌ 媒體安全檢查測試失敗:', error);
  }
}
