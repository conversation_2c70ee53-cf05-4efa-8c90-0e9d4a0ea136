// 提供全專案使用的配置管理和基礎工具函數

// 1. Configuration - 從 APIKEY 工作表讀取設定
function getConfig() {
  const sheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName('APIKEY');
  if (!sheet) {
    throw new Error('找不到 APIKEY 工作表，請先執行 initializeSheets()');
  }
  
  return {
    lineChannelAccessToken: sheet.getRange('B2').getValue() || '',
    lineChannelSecret: sheet.getRange('B3').getValue() || '',
    geminiApiKey: sheet.getRange('B4').getValue() || '',
    youtubeApiKey: sheet.getRange('B5').getValue() || '',
    threadsUserId: sheet.getRange('B6').getValue() || '',
    threadsAccessToken: sheet.getRange('B7').getValue() || '',
    cloudinaryCloudName: sheet.getRange('B8').getValue() || '',
    cloudinaryApiKey: sheet.getRange('B9').getValue() || '',
    cloudinaryApiSecret: sheet.getRange('B10').getValue() || '',
    folderId: sheet.getRange('B11').getValue() || '1vjius2gRLSvxOmtFhNsS9ALvfkdGOTK-',
    googleSearchApiKey: sheet.getRange('B12').getValue() || '',
    googleSearchEngineId: sheet.getRange('B13').getValue() || '',
    generalModel: sheet.getRange('B14').getValue() || 'gemini-1.5-flash',    // 一般功能模型
    visionModel: sheet.getRange('B15').getValue() || 'gemini-1.5-pro'       // 圖片分析模型
  };
}

// 2. [核心] 統一的 Gemini API 呼叫函數
function callGemini(prompt, modelType = 'general', imageBlob = null) {
  const config = getConfig();
  if (!config.geminiApiKey) throw new Error('Gemini API Key 未設定');

  const modelName = modelType === 'vision' ? config.visionModel : config.generalModel;
  const url = `https://generativelanguage.googleapis.com/v1beta/models/${modelName}:generateContent?key=${config.geminiApiKey}`;

  let parts = [{ text: prompt }];
  if (modelType === 'vision' && imageBlob) {
    parts.push({
      inline_data: {
        mime_type: imageBlob.getContentType(),
        data: Utilities.base64Encode(imageBlob.getBytes())
      }
    });
  }

  const payload = JSON.stringify({ contents: [{ parts: parts }] });
  const options = { method: 'POST', headers: { 'Content-Type': 'application/json' }, payload: payload, muteHttpExceptions: true };

  const response = UrlFetchApp.fetch(url, options);
  const responseCode = response.getResponseCode();
  const responseText = response.getContentText();

  if (responseCode !== 200) {
    console.error(`Gemini API 錯誤 (${modelName}) - Code: ${responseCode}, Response: ${responseText}`);
    throw new Error(`Gemini API 呼叫失敗 (${modelName})`);
  }

  const result = JSON.parse(responseText);
  if (!result.candidates || !result.candidates[0].content || !result.candidates[0].content.parts) {
    throw new Error('Gemini API 回應格式不正確');
  }
  
  return result.candidates[0].content.parts[0].text.trim();
}

// 3. 統一處理全形半形符號
function normalizeText(text) {
  return text
    .replace(/！/g, '!')  // 全形驚嘆號轉半形
    .replace(/？/g, '?')  // 全形問號轉半形
    .replace(/，/g, ',')  // 全形逗號轉半形（如需要）
    .replace(/。/g, '.')  // 全形句號轉半形（如需要）
    .trim();
}

// 4. 多語言指令對照表
function normalizeCommand(text) {
  const commandMap = {
    // 筆記功能
    '记录': '記錄',     // 簡體
    'note': '記錄',     // 英文
    'record': '記錄',   // 英文
    'memo': '記錄',     // 英文
    
    // 測試功能  
    '测试': '測試',     // 簡體
    'test': '測試',     // 英文
    
    // 幫助功能
    '帮助': '幫助',     // 簡體
    'help': '幫助',     // 英文
    'assistance': '幫助', // 英文
    
    // Threads功能
    't': 't',           // 保持原樣
    'threads': 't',     // 英文全名
    '发文': 't',        // 簡體
    '發文': 't',        // 繁體
    
    // 檔案記憶相關指令
    '分析': '分析',     // 繁體
    'analyze': '分析',  // 英文
    'analyse': '分析',  // 英文
    '查看': '查看',     // 繁體
    'view': '查看',     // 英文
    'show': '查看'      // 英文
  };
  
  // 分離指令和內容
  const parts = text.split(' ');
  const command = parts[0].toLowerCase();
  const content = parts.slice(1).join(' ');
  
  // 轉換指令
  const normalizedCommand = commandMap[command] || command;
  
  return content ? `${normalizedCommand} ${content}` : normalizedCommand;
}

// 5. 回覆訊息到 LINE
function replyMessage(replyToken, text) {
  try {
    const config = getConfig();
    
    if (!config.lineChannelAccessToken) {
      throw new Error('LINE Channel Access Token 未設定');
    }
    
    const url = 'https://api.line.me/v2/bot/message/reply';
    const payload = JSON.stringify({
      replyToken: replyToken,
      messages: [{ type: 'text', text: text }]
    });
    
    const response = UrlFetchApp.fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + config.lineChannelAccessToken
      },
      payload: payload
    });
    
    if (response.getResponseCode() !== 200) {
      throw new Error(`LINE API 回應錯誤: ${response.getContentText()}`);
    }
    
  } catch (error) {
    console.error('回覆訊息錯誤:', error);
    logActivity('回覆錯誤', error.toString());
  }
}

// 6. 記錄活動日誌
function logActivity(action, details) {
  try {
    const sheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName('活動日誌');
    if (sheet) {
      const timestamp = new Date();
      sheet.appendRow([timestamp, action, details]);
      
      // 保持日誌在合理範圍內
      if (sheet.getLastRow() > 1000) {
        sheet.deleteRows(2, 100);
      }
    }
  } catch (error) {
    console.error('記錄活動日誌錯誤:', error);
  }
}

// 7. 保存文字筆記
function saveTextNote(userId, content, sourceType) {
  try {
    const sheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName('筆記暫存');
    if (sheet) {
      const timestamp = new Date();
      sheet.appendRow([timestamp, userId, sourceType, content]);
      logActivity('儲存筆記', `用戶${userId}: ${content.substring(0, 30)}...`);
    }
  } catch (error) {
    console.error('儲存筆記錯誤:', error);
  }
}

// 8. 取得媒體類型顯示名稱
function getMediaTypeDisplay(type) {
  const typeMap = {
    'image': '圖片',
    'video': '影片', 
    'audio': '音訊',
    'file': '檔案'
  };
  return typeMap[type] || '媒體檔案';
}

// 9. 記錄檔案日誌
function logFileActivity(userId, fileName, fileUrl, fileSize, mediaType = 'file', sourceType = 'user') {
  try {
    const sheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName('檔案日誌');
    if (sheet) {
      const timestamp = new Date();
      sheet.appendRow([timestamp, userId, fileName, fileUrl, fileSize, mediaType, sourceType]);
    }
  } catch (error) {
    console.error('記錄檔案日誌錯誤:', error);
  }
}

// 10. 取得檔案類型資訊
function getFileTypeInfo(fileName, mimeType) {
  const fileExtension = fileName.split('.').pop().toLowerCase();
  
  // 檔案類型對應表
  const typeMap = {
    // 文檔類型
    'pdf': { icon: '📄', description: 'PDF 文件' },
    'docx': { icon: '📝', description: 'Word 文件' },
    'doc': { icon: '📝', description: 'Word 文件（舊版）' },
    'xlsx': { icon: '📊', description: 'Excel 試算表' },
    'xls': { icon: '📊', description: 'Excel 試算表（舊版）' },
    'pptx': { icon: '🎯', description: 'PowerPoint 簡報' },
    'ppt': { icon: '🎯', description: 'PowerPoint 簡報（舊版）' },
    
    // Google 文件
    'google-apps.document': { icon: '📝', description: 'Google 文件' },
    'google-apps.spreadsheet': { icon: '📊', description: 'Google 試算表' },
    'google-apps.presentation': { icon: '🎯', description: 'Google 簡報' },
    
    // 文字和代碼
    'txt': { icon: '📝', description: '純文字檔案' },
    'md': { icon: '📋', description: 'Markdown 文件' },
    'csv': { icon: '📊', description: 'CSV 資料檔' },
    'json': { icon: '🔧', description: 'JSON 資料檔' },
    'xml': { icon: '🔧', description: 'XML 檔案' },
    'log': { icon: '📝', description: '日誌檔案' },
    'rtf': { icon: '📝', description: 'RTF 格式文件' },
    
    // 圖片
    'jpg': { icon: '🖼️', description: 'JPEG 圖片' },
    'jpeg': { icon: '🖼️', description: 'JPEG 圖片' },
    'png': { icon: '🖼️', description: 'PNG 圖片' },
    'gif': { icon: '🖼️', description: 'GIF 圖片' },
    'bmp': { icon: '🖼️', description: 'BMP 圖片' },
    
    // 影音
    'mp4': { icon: '🎬', description: 'MP4 影片' },
    'avi': { icon: '🎬', description: 'AVI 影片' },
    'mp3': { icon: '🎵', description: 'MP3 音訊' },
    'm4a': { icon: '🎵', description: 'M4A 音訊' },
    
    // 壓縮檔
    'zip': { icon: '📦', description: 'ZIP 壓縮檔' },
    'rar': { icon: '📦', description: 'RAR 壓縮檔' },
    '7z': { icon: '📦', description: '7Z 壓縮檔' }
  };
  
  // 先檢查 MIME 類型
  if (mimeType.includes('google-apps')) {
    const appType = mimeType.split('.').pop();
    return typeMap[`google-apps.${appType}`] || { icon: '📁', description: 'Google Apps 檔案' };
  }
  
  // 再檢查副檔名
  return typeMap[fileExtension] || { icon: '📁', description: '未知檔案類型' };
}

// 11. 獲取版本資訊（統一從 Code.gs 獲取）
function getVersionInfo() {
  return {
    version: SCRIPT_VERSION,  // 這個變數現在定義在 Code.gs 頂部
  };
}
