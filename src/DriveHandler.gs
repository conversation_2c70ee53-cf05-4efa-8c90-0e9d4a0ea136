// == Google Drive 處理模組 ==
// 專門處理 Google Drive 連結的讀取、分析和管理

// 1. 處理 Google Drive 連結主函數
function handleGoogleDriveLink(text, userId, sourceType) {
  try {
    // 提取 Google Drive 檔案 ID
    const driveRegex = /(?:drive\.google\.com\/(?:file\/d\/|open\?id=)|docs\.google\.com\/(?:document|spreadsheets|presentation)\/d\/)([a-zA-Z0-9-_]+)/;
    const match = text.match(driveRegex);
    
    if (!match) {
      return generateDriveLinkErrorMessage();
    }
    
    const fileId = match[1];
    logActivity('Google Drive連結', `用戶${userId}(${sourceType})分享檔案ID: ${fileId}`);
    
    // 嘗試讀取檔案資訊
    const fileResult = processDriveFile(fileId, userId, sourceType);
    return fileResult;
    
  } catch (error) {
    console.error('處理 Google Drive 連結錯誤:', error);
    return `⚠️ 處理 Google Drive 連結時發生錯誤：${error.message}`;
  }
}

// 2. 處理 Drive 檔案
function processDriveFile(fileId, userId, sourceType) {
  try {
    const file = DriveApp.getFileById(fileId);
    const fileName = file.getName();
    const fileSize = (file.getSize() / 1024).toFixed(2) + ' KB';
    const mimeType = file.getBlob().getContentType();
    const fileUrl = file.getUrl();
    
    // 詳細的檔案類型判斷
    const fileTypeInfo = getFileTypeInfo(fileName, mimeType);
    
    // 生成簡短描述和關鍵特徵
    let description = `Google Drive檔案：${fileTypeInfo.description}`;
    let features = `來源:GoogleDrive, 類型:${mimeType}, 大小:${fileSize}`;
    
    let analysisResult = '';
    const config = getConfig();
    
    // 進行檔案分析
    if (config.geminiApiKey) {
      analysisResult = performDriveFileAnalysis(file, fileName, mimeType);
      
      if (analysisResult) {
        description = analysisResult.substring(0, 100) + (analysisResult.length > 100 ? '...' : '');
        features += `, AI分析:${analysisResult.substring(0, 50)}`;
      }
    }
    
    // 記錄到檔案記憶庫（使用 Memory_System.gs 的記憶中間件）
    memoryMiddleware('record_file', {
      userId: userId,
      fileId: fileId,
      fileName: fileName,
      fileType: mimeType,
      fileSize: fileSize,
      description: description,
      features: features,
      fileUrl: fileUrl,
      sourceType: sourceType
    });
    
    return generateDriveFileResponse(fileName, fileSize, fileTypeInfo, mimeType, analysisResult);
    
  } catch (fileError) {
    return generateDriveAccessErrorMessage();
  }
}

// 3. 執行 Drive 檔案分析
function performDriveFileAnalysis(file, fileName, mimeType) {
  try {
    if (mimeType.includes('image/')) {
      // 圖片分析
      return callGemini("請用繁體中文分析這張圖片的內容。描述你看到的主要物件、場景、文字、顏色、構圖等重要元素。請簡潔明瞭，限制在150字以內。", 'vision', file.getBlob());
    } else {
      // 其他檔案類型使用專用分析模組（File_Analysis.gs）
      return processSpecialFileTypes(file, fileName, mimeType);
    }
  } catch (analysisError) {
    console.error('Drive 檔案分析錯誤:', analysisError);
    logActivity('檔案分析錯誤', `檔案: ${fileName}, 錯誤: ${analysisError.toString()}`);
    return `分析過程中發生錯誤：${analysisError.message}`;
  }
}

// 4. 生成 Drive 檔案回應訊息
function generateDriveFileResponse(fileName, fileSize, fileTypeInfo, mimeType, analysisResult) {
  let responseText = `✅ 成功讀取 Google Drive 檔案！\n\n📁 檔案名稱：${fileName}\n📊 檔案大小：${fileSize}\n${fileTypeInfo.icon} 檔案類型：${fileTypeInfo.description}\n🔄 MIME 類型：${mimeType}\n🧠 已記錄到檔案記憶庫`;
  
  if (analysisResult) {
    responseText += `\n\n🤖 AI 分析：${analysisResult}`;
  } else {
    responseText += `\n\n💡 稍後可詢問：「剛才那個${fileTypeInfo.description}如何？」`;
  }
  
  return responseText;
}

// 5. 生成 Drive 連結錯誤訊息
function generateDriveLinkErrorMessage() {
  return '❌ 無法識別 Google Drive 連結格式\n\n💡 請確認連結格式正確：\n• drive.google.com/file/d/檔案ID\n• docs.google.com/document/d/檔案ID\n\n🔧 支援的檔案類型：\n• Google 文件、試算表、簡報\n• PDF、Word、Excel、PowerPoint\n• 圖片、影片、音訊檔案\n• 純文字檔案（TXT、CSV、JSON等）';
}

// 6. 生成 Drive 存取錯誤訊息
function generateDriveAccessErrorMessage() {
  return `❌ 無法存取 Google Drive 檔案\n\n可能原因：\n• 檔案權限不足（請設為「知道連結的使用者」）\n• 檔案 ID 不正確\n• 檔案已被刪除\n• 檔案位於私人資料夾中\n\n🔧 解決方案：\n1. 在 Google Drive 中右鍵點選檔案\n2. 選擇「共用」→「變更為知道連結的使用者」\n3. 確認檔案權限設為「檢視者」或「編輯者」\n4. 複製連結重新傳送\n\n💡 提示：企業帳號可能有額外的共用限制`;
}

// 7. 批次處理 Drive 檔案
function batchProcessDriveLinks(driveLinks, userId, sourceType) {
  try {
    const results = [];
    
    driveLinks.forEach((link, index) => {
      try {
        logActivity('批次處理Drive檔案', `處理第 ${index + 1}/${driveLinks.length} 個連結`);
        
        const result = handleGoogleDriveLink(link, userId, sourceType);
        results.push({
          link: link,
          status: 'success',
          result: result
        });
        
      } catch (error) {
        results.push({
          link: link,
          status: 'error',
          error: error.message
        });
      }
    });
    
    const successCount = results.filter(r => r.status === 'success').length;
    const errorCount = results.filter(r => r.status === 'error').length;
    
    return {
      total: driveLinks.length,
      successful: successCount,
      failed: errorCount,
      results: results
    };
    
  } catch (error) {
    console.error('批次處理 Drive 連結錯誤:', error);
    return {
      total: driveLinks.length,
      successful: 0,
      failed: driveLinks.length,
      error: error.message
    };
  }
}

// 8. 檢查 Drive 檔案權限
function checkDriveFilePermission(fileId) {
  try {
    const file = DriveApp.getFileById(fileId);
    
    const permissionInfo = {
      canAccess: true,
      fileName: file.getName(),
      fileSize: file.getSize(),
      mimeType: file.getBlob().getContentType(),
      sharing: 'unknown',
      warnings: []
    };
    
    // 檢查檔案大小
    if (permissionInfo.fileSize > 100 * 1024 * 1024) { // 100MB
      permissionInfo.warnings.push('檔案大小超過100MB，處理可能較慢');
    }
    
    // 檢查 MIME 類型
    if (permissionInfo.mimeType.includes('application/octet-stream')) {
      permissionInfo.warnings.push('檔案類型無法確定，可能影響分析結果');
    }
    
    return permissionInfo;
    
  } catch (error) {
    return {
      canAccess: false,
      error: error.message,
      suggestions: [
        '檢查檔案 ID 是否正確',
        '確認檔案共用設定',
        '檢查檔案是否存在'
      ]
    };
  }
}

// 9. 取得 Drive 檔案詳細資訊
function getDriveFileDetails(fileId) {
  try {
    const file = DriveApp.getFileById(fileId);
    
    const details = {
      id: fileId,
      name: file.getName(),
      size: file.getSize(),
      sizeFormatted: (file.getSize() / 1024).toFixed(2) + ' KB',
      mimeType: file.getBlob().getContentType(),
      url: file.getUrl(),
      dateCreated: file.getDateCreated(),
      lastUpdated: file.getLastUpdated(),
      description: file.getDescription(),
      parents: file.getParents(),
      downloadUrl: null
    };
    
    // 嘗試取得下載連結（如果可用）
    try {
      details.downloadUrl = file.getDownloadUrl();
    } catch (downloadError) {
      // 某些檔案類型可能無法直接下載
      details.downloadUrl = null;
    }
    
    // 檔案類型詳細資訊
    details.typeInfo = getFileTypeInfo(details.name, details.mimeType);
    
    return details;
    
  } catch (error) {
    console.error('取得 Drive 檔案詳細資訊錯誤:', error);
    return {
      error: error.message,
      id: fileId
    };
  }
}

// 10. 生成 Drive 檔案摘要報告
function generateDriveFileReport(userId, timeRange = null) {
  try {
    const recentFiles = getRecentFiles(userId, timeRange, 50);
    const driveFiles = recentFiles.filter(file => 
      file.features && file.features.includes('來源:GoogleDrive')
    );
    
    if (driveFiles.length === 0) {
      return '📊 Google Drive 檔案報告：目前沒有處理過的 Drive 檔案';
    }
    
    // 統計資料
    const stats = {
      totalFiles: driveFiles.length,
      byType: {},
      totalSizeMB: 0,
      avgAnalysisTime: 0
    };
    
    // 按檔案類型統計
    driveFiles.forEach(file => {
      const type = file.fileType;
      stats.byType[type] = (stats.byType[type] || 0) + 1;
      
      // 計算總大小
      const sizeKB = parseFloat(file.fileSize.replace(' KB', ''));
      if (!isNaN(sizeKB)) {
        stats.totalSizeMB += sizeKB / 1024;
      }
    });
    
    // 生成報告
    let report = `📊 Google Drive 檔案報告\n\n`;
    report += `📁 總檔案數：${stats.totalFiles}\n`;
    report += `💾 總大小：${stats.totalSizeMB.toFixed(2)} MB\n`;
    report += `📏 平均大小：${(stats.totalSizeMB / stats.totalFiles).toFixed(2)} MB\n\n`;
    
    report += `📄 檔案類型分布：\n`;
    Object.entries(stats.byType)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .forEach(([type, count]) => {
        const percentage = ((count / stats.totalFiles) * 100).toFixed(1);
        report += `• ${type}: ${count}個 (${percentage}%)\n`;
      });
    
    // 最近處理的檔案
    if (driveFiles.length > 0) {
      report += `\n📋 最近處理的 Drive 檔案：\n`;
      driveFiles.slice(0, 3).forEach((file, index) => {
        const timeAgo = formatTimeAgo(file.timestamp);
        report += `${index + 1}. ${file.fileName} (${timeAgo})\n`;
      });
    }
    
    return report;
    
  } catch (error) {
    console.error('生成 Drive 檔案報告錯誤:', error);
    return `📊 報告生成失敗：${error.message}`;
  }
}

// 11. Drive 檔案快取管理
function manageDriveFileCache() {
  try {
    const sheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName('檔案記憶庫');
    if (!sheet) return { error: '找不到檔案記憶庫工作表' };
    
    const data = sheet.getDataRange().getValues();
    const driveFiles = data.slice(1) // 跳過標題行
      .filter(row => row[7] && row[7].includes('來源:GoogleDrive'))
      .map((row, index) => ({
        rowIndex: index + 2, // +2 因為陣列索引從0開始，且跳過標題行
        timestamp: row[0],
        fileId: row[2],
        fileName: row[3],
        features: row[7]
      }));
    
    // 清理超過30天的舊記錄
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const oldFiles = driveFiles.filter(file => new Date(file.timestamp) < thirtyDaysAgo);
    
    let cleanupCount = 0;
    oldFiles.forEach(file => {
      try {
        sheet.deleteRow(file.rowIndex - cleanupCount);
        cleanupCount++;
      } catch (deleteError) {
        console.error('刪除舊記錄錯誤:', deleteError);
      }
    });
    
    return {
      totalDriveFiles: driveFiles.length,
      cleanedOldFiles: cleanupCount,
      message: `清理了 ${cleanupCount} 個超過30天的 Drive 檔案記錄`
    };
    
  } catch (error) {
    console.error('管理 Drive 檔案快取錯誤:', error);
    return { error: error.message };
  }
}

// 12. 🧪 測試函數：Drive 連結解析
function testDriveLinkParsing() {
  console.log('=== 測試 Drive 連結解析 ===');
  
  const testLinks = [
    'https://drive.google.com/file/d/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms/view',
    'https://docs.google.com/document/d/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms/edit',
    'https://drive.google.com/open?id=1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms',
    'https://docs.google.com/spreadsheets/d/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms/edit#gid=0',
    'invalid-link-format'
  ];
  
  const driveRegex = /(?:drive\.google\.com\/(?:file\/d\/|open\?id=)|docs\.google\.com\/(?:document|spreadsheets|presentation)\/d\/)([a-zA-Z0-9-_]+)/;
  
  testLinks.forEach((link, index) => {
    const match = link.match(driveRegex);
    console.log(`測試 ${index + 1}: ${link}`);
    console.log(`  結果: ${match ? `✅ 檔案ID: ${match[1]}` : '❌ 無法解析'}`);
    console.log('---');
  });
  
  console.log('✅ Drive 連結解析測試完成');
}

// 13. 🧪 測試函數：Drive 檔案權限檢查
function testDriveFilePermission() {
  console.log('=== 測試 Drive 檔案權限檢查 ===');
  
  try {
    // 使用一個公開的 Google Sheets 範例檔案 ID 進行測試
    const testFileId = '1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms';
    
    console.log(`測試檔案 ID: ${testFileId}`);
    
    const permissionResult = checkDriveFilePermission(testFileId);
    
    if (permissionResult.canAccess) {
      console.log('✅ 檔案可以存取');
      console.log(`  檔案名稱: ${permissionResult.fileName}`);
      console.log(`  檔案大小: ${(permissionResult.fileSize / 1024).toFixed(2)} KB`);
      console.log(`  MIME 類型: ${permissionResult.mimeType}`);
      
      if (permissionResult.warnings.length > 0) {
        console.log(`  警告: ${permissionResult.warnings.join(', ')}`);
      }
    } else {
      console.log('❌ 檔案無法存取');
      console.log(`  錯誤: ${permissionResult.error}`);
      if (permissionResult.suggestions) {
        console.log(`  建議: ${permissionResult.suggestions.join(', ')}`);
      }
    }
    
    console.log('✅ Drive 檔案權限檢查測試完成');
    
  } catch (error) {
    console.error('❌ Drive 檔案權限檢查測試失敗:', error);
  }
}
