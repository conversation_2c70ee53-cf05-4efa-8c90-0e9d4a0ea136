// 🔧 群組聊天記錄功能診斷與修復工具

/**
 * 🩺 診斷群組聊天記錄功能問題
 * 這個函數會檢查所有可能導致記錄失敗的原因
 */
function diagnoseGroupChatIssues() {
  console.log('🩺 開始診斷群組聊天記錄功能...');
  
  const diagnosis = {
    timestamp: new Date(),
    issues: [],
    fixes: [],
    status: 'unknown'
  };
  
  try {
    // 1. 檢查工作表是否存在
    const ss = SpreadsheetApp.getActiveSpreadsheet();
    let sheet = ss.getSheetByName('群組發言記錄');
    
    if (!sheet) {
      diagnosis.issues.push('❌ 「群組發言記錄」工作表不存在');
      diagnosis.fixes.push('建立工作表');
      
      try {
        sheet = ss.insertSheet('群組發言記錄');
        const headers = ['時間戳', '群組ID', '用戶ID', '用戶顯示名稱', '訊息內容', '訊息類型', '回應狀態'];
        sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
        sheet.getRange(1, 1, 1, headers.length).setFontWeight('bold');
        sheet.getRange(1, 1, 1, headers.length).setBackground('#e1f5fe');
        diagnosis.fixes.push('✅ 已建立「群組發言記錄」工作表');
      } catch (error) {
        diagnosis.issues.push(`❌ 建立工作表失敗: ${error.message}`);
      }
    } else {
      diagnosis.fixes.push('✅ 「群組發言記錄」工作表存在');
    }
    
    // 2. 檢查依賴函數是否存在
    const requiredFunctions = [
      'normalizeText',
      'getMediaTypeDisplay', 
      'logActivity',
      'getConfig'
    ];
    
    requiredFunctions.forEach(funcName => {
      try {
        if (typeof eval(funcName) === 'function') {
          diagnosis.fixes.push(`✅ 函數 ${funcName} 可用`);
        } else {
          diagnosis.issues.push(`❌ 函數 ${funcName} 不存在`);
        }
      } catch (error) {
        diagnosis.issues.push(`❌ 函數 ${funcName} 檢查失敗: ${error.message}`);
      }
    });
    
    // 3. 檢查配置
    try {
      const config = getConfig();
      if (config) {
        diagnosis.fixes.push('✅ 配置讀取正常');
        if (config.lineChannelAccessToken) {
          diagnosis.fixes.push('✅ LINE Channel Access Token 已設定');
        } else {
          diagnosis.issues.push('⚠️ LINE Channel Access Token 未設定，無法獲取用戶顯示名稱');
        }
      } else {
        diagnosis.issues.push('❌ 無法讀取配置');
      }
    } catch (error) {
      diagnosis.issues.push(`❌ 配置檢查失敗: ${error.message}`);
    }
    
    // 4. 測試記錄函數
    try {
      const testEvent = {
        source: { groupId: 'test-group-123' },
        message: { type: 'text', text: '測試訊息' }
      };
      
      recordGroupMessageFixed(testEvent, 'test-user-123', 'group');
      diagnosis.fixes.push('✅ 記錄函數測試成功');
      
      // 檢查是否真的寫入了
      if (sheet && sheet.getLastRow() > 1) {
        diagnosis.fixes.push('✅ 測試資料已寫入工作表');
      } else {
        diagnosis.issues.push('❌ 測試資料沒有寫入工作表');
      }
    } catch (error) {
      diagnosis.issues.push(`❌ 記錄函數測試失敗: ${error.message}`);
    }
    
    // 5. 綜合評估
    if (diagnosis.issues.length === 0) {
      diagnosis.status = 'healthy';
    } else if (diagnosis.issues.filter(issue => issue.startsWith('❌')).length === 0) {
      diagnosis.status = 'warning';
    } else {
      diagnosis.status = 'error';
    }
    
    console.log('🩺 診斷完成:', diagnosis);
    return diagnosis;
    
  } catch (error) {
    diagnosis.status = 'critical_error';
    diagnosis.issues.push(`❌ 診斷過程失敗: ${error.message}`);
    console.error('診斷錯誤:', error);
    return diagnosis;
  }
}


/**
 * 🔄 替換現有的群組發言記錄函數
 * 運行此函數來更新 Webhook.gs 中的函數調用
 */
function fixGroupChatRecording() {
  console.log('🔄 修復群組聊天記錄功能...');
  
  try {
    // 1. 先運行診斷
    const diagnosis = diagnoseGroupChatIssues();
    console.log('診斷結果:', diagnosis);
    
    // 2. 如果有錯誤，嘗試修復
    if (diagnosis.status === 'error' || diagnosis.status === 'critical_error') {
      console.log('⚠️ 發現問題，正在嘗試修復...');
      
      // 確保工作表存在
      const ss = SpreadsheetApp.getActiveSpreadsheet();
      let sheet = ss.getSheetByName('群組發言記錄');
      
      if (!sheet) {
        sheet = ss.insertSheet('群組發言記錄');
        const headers = ['時間戳', '群組ID', '用戶ID', '用戶顯示名稱', '訊息內容', '訊息類型', '回應狀態'];
        sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
        sheet.getRange(1, 1, 1, headers.length).setFontWeight('bold');
        sheet.getRange(1, 1, 1, headers.length).setBackground('#e1f5fe');
        console.log('✅ 工作表已建立');
      }
    }
    
    // 3. 添加測試資料
    const testEvent = {
      source: { groupId: 'test-group-diagnosis' },
      message: { type: 'text', text: '🔧 群組聊天記錄功能診斷測試' }
    };
    
    const result = recordGroupMessageFixed(testEvent, 'diagnostic-user', 'group');
    
    if (result) {
      console.log('✅ 群組聊天記錄功能修復成功！');
      return '✅ 群組聊天記錄功能已修復並測試成功！現在應該可以正常記錄群組發言了。';
    } else {
      console.log('❌ 修復失敗');
      return '❌ 修復失敗，請檢查錯誤日誌';
    }
    
  } catch (error) {
    console.error('修復過程錯誤:', error);
    return `❌ 修復失敗: ${error.message}`;
  }
}

/**
 * 📊 檢查群組聊天記錄狀態
 */
function checkGroupChatStatus() {
  try {
    const ss = SpreadsheetApp.getActiveSpreadsheet();
    const sheet = ss.getSheetByName('群組發言記錄');
    
    if (!sheet) {
      return {
        exists: false,
        records: 0,
        lastUpdate: null,
        status: '❌ 工作表不存在'
      };
    }
    
    const lastRow = sheet.getLastRow();
    const records = Math.max(0, lastRow - 1); // 扣除標題行
    
    let lastUpdate = null;
    if (lastRow > 1) {
      const lastEntry = sheet.getRange(lastRow, 1).getValue();
      lastUpdate = new Date(lastEntry);
    }
    
    return {
      exists: true,
      records: records,
      lastUpdate: lastUpdate,
      status: records > 0 ? '✅ 正常運作' : '⚠️ 沒有記錄'
    };
    
  } catch (error) {
    return {
      exists: false,
      records: 0,
      lastUpdate: null,
      status: `❌ 檢查失敗: ${error.message}`
    };
  }
}

/**
 * 🧪 手動測試群組發言記錄
 */
function manualTestGroupRecording() {
  console.log('🧪 手動測試群組發言記錄...');
  
  const testCases = [
    {
      event: {
        source: { groupId: 'test-group-001' },
        message: { type: 'text', text: '普通發言測試' }
      },
      userId: 'user-001',
      sourceType: 'group'
    },
    {
      event: {
        source: { groupId: 'test-group-001' },
        message: { type: 'text', text: '!指令測試' }
      },
      userId: 'user-002',
      sourceType: 'group'
    },
    {
      event: {
        source: { groupId: 'test-group-001' },
        message: { type: 'image' }
      },
      userId: 'user-003',
      sourceType: 'group'
    }
  ];
  
  const results = [];
  
  testCases.forEach((testCase, index) => {
    try {
      console.log(`測試案例 ${index + 1}:`);
      const result = recordGroupMessageFixed(testCase.event, testCase.userId, testCase.sourceType);
      results.push({
        case: index + 1,
        success: result,
        type: testCase.event.message?.type || 'unknown'
      });
      console.log(`  結果: ${result ? '✅ 成功' : '❌ 失敗'}`);
    } catch (error) {
      console.error(`  錯誤: ${error.message}`);
      results.push({
        case: index + 1,
        success: false,
        error: error.message
      });
    }
  });
  
  const successCount = results.filter(r => r.success).length;
  console.log(`\n📊 測試結果: ${successCount}/${results.length} 成功`);
  
  return {
    totalTests: results.length,
    successCount: successCount,
    results: results,
    status: successCount === results.length ? '✅ 全部通過' : '❌ 部分失敗'
  };
}
