// == 檔案分析專用模組 ==
// 專門處理各種檔案類型的 AI 分析功能（使用統一模型配置）

// 1. 處理 PDF 檔案（使用統一模型配置）
function processPDFFile(file) {
  try {
    const fileName = file.getName();
    const fileSize = file.getSize();
    
    const prompt = `這是一個 PDF 檔案「${fileName}」，檔案大小為 ${(fileSize/1024).toFixed(2)} KB。

請根據以下資訊用繁體中文提供詳細分析：
1. 根據檔案名稱推測內容主題
2. 估計可能的文件類型（學術論文、報告、手冊、書籍等）
3. 建議的使用場景
4. 可能包含的重要資訊類型

請提供實用且具體的分析，限制在200字以內。`;

    return callGemini(prompt, 'general');
    
  } catch (error) {
    console.error('PDF 處理錯誤:', error);
    return `PDF 分析失敗：${error.message}`;
  }
}

// 2. 處理 Google Apps 檔案（使用統一模型配置）
function processGoogleAppsFile(file, mimeType) {
  try {
    const fileName = file.getName();
    const appType = mimeType.includes('document') ? '文件' : 
                   mimeType.includes('spreadsheet') ? '試算表' : 
                   mimeType.includes('presentation') ? '簡報' : 'Google Apps 檔案';
    
    const prompt = `這是一個 Google ${appType}「${fileName}」。

請用繁體中文分析：
1. 根據檔案名稱推測內容和用途
2. 這類 ${appType} 通常包含什麼類型的資訊
3. 建議的應用場景
4. 可能的目標受眾

請提供實用的分析，限制在150字以內。`;

    return callGemini(prompt, 'general');
    
  } catch (error) {
    console.error('Google Apps 檔案處理錯誤:', error);
    return `Google Apps 檔案分析失敗：${error.message}`;
  }
}

// 3. 處理 Office 檔案（使用統一模型配置）
function processOfficeFile(file, fileName, mimeType) {
  try {
    const fileType = mimeType.includes('word') || fileName.includes('.doc') ? 'Word 文件' :
                     mimeType.includes('excel') || fileName.includes('.xls') ? 'Excel 試算表' :
                     mimeType.includes('powerpoint') || fileName.includes('.ppt') ? 'PowerPoint 簡報' :
                     'Office 檔案';
    
    const prompt = `這是一個 ${fileType}「${fileName}」。

請用繁體中文提供分析：
1. 根據檔案名稱推測主要內容
2. 這類 ${fileType} 的典型用途和特點
3. 可能包含的資訊類型
4. 適用的工作場景

請簡潔明瞭，限制在150字以內。`;

    return callGemini(prompt, 'general');
    
  } catch (error) {
    console.error('Office 檔案處理錯誤:', error);
    return `Office 檔案分析失敗：${error.message}`;
  }
}

// 4. Gemini 文字分析功能（使用統一模型配置）
function callGeminiTextAnalysis(textContent, fileName) {
  try {
    const prompt = `請分析以下文字內容，檔案名稱是「${fileName}」：

內容：
${textContent.substring(0, 2000)}${textContent.length > 2000 ? '...(內容已截斷)' : ''}

請用繁體中文提供：
1. 內容摘要（100字以內）
2. 主要重點
3. 檔案類型判斷
4. 建議用途

請簡潔回答，總字數控制在200字以內。`;
    
    return callGemini(prompt, 'general');
    
  } catch (error) {
    console.error('Gemini 文字分析錯誤:', error);
    throw new Error(`文字內容分析失敗: ${error.message}`);
  }
}

// 5. 檔案類型特殊處理器（使用統一模型配置）
function processSpecialFileTypes(file, fileName, mimeType) {
  try {
    let analysisResult = '';
    
    // 根據檔案類型選擇處理方法
    if (mimeType.includes('application/pdf')) {
      analysisResult = processPDFFile(file);
    } else if (mimeType.includes('text/') || fileName.endsWith('.md') || fileName.endsWith('.txt') || fileName.endsWith('.log')) {
      // 文字檔案讀取內容分析
      const content = file.getBlob().getDataAsString('UTF-8');
      analysisResult = callGeminiTextAnalysis(content, fileName);
    } else if (mimeType.includes('application/vnd.google-apps')) {
      // Google 文件、試算表、簡報處理
      analysisResult = processGoogleAppsFile(file, mimeType);
    } else if (mimeType.includes('application/vnd.openxmlformats') || 
               mimeType.includes('application/vnd.ms-')) {
      // Office 檔案處理
      analysisResult = processOfficeFile(file, fileName, mimeType);
    } else if (mimeType.includes('application/json')) {
      // JSON 檔案處理
      analysisResult = processJSONFile(file, fileName);
    } else if (mimeType.includes('text/csv') || fileName.endsWith('.csv')) {
      // CSV 檔案處理
      analysisResult = processCSVFile(file, fileName);
    } else if (mimeType.includes('application/xml') || fileName.endsWith('.xml')) {
      // XML 檔案處理
      analysisResult = processXMLFile(file, fileName);
    } else {
      // 其他類型檔案用檔名推測
      analysisResult = generateFilenameSummary(fileName);
    }
    
    return analysisResult;
    
  } catch (error) {
    console.error('特殊檔案類型處理錯誤:', error);
    return `檔案分析失敗：${error.message}`;
  }
}

// 6. 處理 JSON 檔案（使用統一模型配置）
function processJSONFile(file, fileName) {
  try {
    const content = file.getBlob().getDataAsString('UTF-8');
    
    // 嘗試解析 JSON 結構
    let jsonStructure = '';
    try {
      const jsonData = JSON.parse(content.substring(0, 1000)); // 只解析前1000字符
      const keys = Object.keys(jsonData);
      jsonStructure = `主要欄位：${keys.slice(0, 5).join(', ')}${keys.length > 5 ? '...' : ''}`;
    } catch (parseError) {
      jsonStructure = '複雜的 JSON 結構';
    }
    
    const prompt = `這是一個 JSON 檔案「${fileName}」。

JSON 結構分析：${jsonStructure}

檔案內容片段：
${content.substring(0, 500)}${content.length > 500 ? '...' : ''}

請用繁體中文分析：
1. JSON 資料的可能用途
2. 資料結構特點
3. 適用的應用場景
4. 建議的處理方式

限制在150字以內。`;

    return callGemini(prompt, 'general');
    
  } catch (error) {
    console.error('JSON 檔案處理錯誤:', error);
    return `JSON 檔案分析失敗：${error.message}`;
  }
}

// 7. 處理 CSV 檔案（使用統一模型配置）
function processCSVFile(file, fileName) {
  try {
    const content = file.getBlob().getDataAsString('UTF-8');
    const lines = content.split('\n');
    const header = lines[0] || '';
    const sampleRows = lines.slice(1, 4).join('\n'); // 取樣前3行資料
    
    const prompt = `這是一個 CSV 檔案「${fileName}」。

標題行：${header}
樣本資料：
${sampleRows}

請用繁體中文分析：
1. 資料表的主要內容和用途
2. 欄位結構特點
3. 可能的應用場景
4. 資料品質評估

限制在150字以內。`;

    return callGemini(prompt, 'general');
    
  } catch (error) {
    console.error('CSV 檔案處理錯誤:', error);
    return `CSV 檔案分析失敗：${error.message}`;
  }
}

// 8. 處理 XML 檔案（使用統一模型配置）
function processXMLFile(file, fileName) {
  try {
    const content = file.getBlob().getDataAsString('UTF-8');
    
    const prompt = `這是一個 XML 檔案「${fileName}」。

檔案內容片段：
${content.substring(0, 800)}${content.length > 800 ? '...' : ''}

請用繁體中文分析：
1. XML 結構的主要用途
2. 資料組織方式
3. 可能的應用場景
4. 建議的處理方法

限制在150字以內。`;

    return callGemini(prompt, 'general');
    
  } catch (error) {
    console.error('XML 檔案處理錯誤:', error);
    return `XML 檔案分析失敗：${error.message}`;
  }
}

// 9. 根據檔名生成摘要（使用統一模型配置）
function generateFilenameSummary(fileName) {
  const prompt = `這是一個檔案「${fileName}」，請用繁體中文根據檔案名稱推測檔案內容並生成簡短摘要。請包含：1.檔案類型判斷 2.可能的內容主題 3.建議用途。限制在100字以內。`;

  try {
    return callGemini(prompt, 'general');
  } catch (error) {
    console.error('檔名摘要生成錯誤:', error);
    return `檔名摘要生成失敗：${error.message}`;
  }
}

// 10. 檔案內容安全檢查
function checkFileContentSafety(fileName, content, mimeType) {
  try {
    const securityFlags = {
      isSafe: true,
      warnings: [],
      risks: []
    };
    
    // 檔案大小檢查
    if (content.length > 10 * 1024 * 1024) { // 10MB
      securityFlags.warnings.push('檔案大小較大，分析可能較慢');
    }
    
    // 檔案類型安全檢查
    const riskyTypes = [
      'application/x-executable',
      'application/x-msdownload',
      'application/x-msdos-program'
    ];
    
    if (riskyTypes.some(type => mimeType.includes(type))) {
      securityFlags.isSafe = false;
      securityFlags.risks.push('可執行檔案類型，不建議分析');
    }
    
    // 內容安全檢查（簡單關鍵字檢測）
    const sensitiveKeywords = [
      'password', 'secret', 'private_key', 'api_key',
      '密碼', '機密', '私鑰', 'token'
    ];
    
    const contentLower = content.toLowerCase();
    const foundSensitive = sensitiveKeywords.filter(keyword => 
      contentLower.includes(keyword.toLowerCase())
    );
    
    if (foundSensitive.length > 0) {
      securityFlags.warnings.push(`可能包含敏感資訊：${foundSensitive.join(', ')}`);
    }
    
    return securityFlags;
    
  } catch (error) {
    console.error('檔案安全檢查錯誤:', error);
    return {
      isSafe: true,
      warnings: ['安全檢查過程出現錯誤'],
      risks: []
    };
  }
}

// 11. 批次檔案分析（使用統一模型配置）
function batchAnalyzeFiles(userId, fileIds) {
  try {
    const results = [];
    
    fileIds.forEach(fileId => {
      try {
        const file = DriveApp.getFileById(fileId);
        const fileName = file.getName();
        const mimeType = file.getBlob().getContentType();
        
        // 進行檔案分析
        const analysisResult = processSpecialFileTypes(file, fileName, mimeType);
        
        results.push({
          fileId: fileId,
          fileName: fileName,
          status: 'success',
          analysis: analysisResult
        });
        
        // 更新檔案分析狀態
        updateFileAnalysisStatus(fileId, '已分析', analysisResult);
        
      } catch (error) {
        results.push({
          fileId: fileId,
          fileName: '未知',
          status: 'error',
          error: error.message
        });
      }
    });
    
    return {
      total: fileIds.length,
      successful: results.filter(r => r.status === 'success').length,
      failed: results.filter(r => r.status === 'error').length,
      results: results
    };
    
  } catch (error) {
    console.error('批次檔案分析錯誤:', error);
    return {
      total: 0,
      successful: 0,
      failed: fileIds.length,
      error: error.message
    };
  }
}

// 12. 檔案相似度比較（使用統一模型配置）
function compareFiles(file1Info, file2Info) {
  try {
    const prompt = `請比較以下兩個檔案的相似性：

檔案1：
- 名稱：${file1Info.fileName}
- 類型：${file1Info.fileType}
- 描述：${file1Info.description}

檔案2：
- 名稱：${file2Info.fileName}
- 類型：${file2Info.fileType}  
- 描述：${file2Info.description}

請用繁體中文分析：
1. 內容相似度（1-10分）
2. 檔案類型關聯性
3. 可能的關係（重複、版本、相關等）
4. 建議的處理方式

限制在200字以內。`;

    return callGemini(prompt, 'general');
    
  } catch (error) {
    console.error('檔案比較錯誤:', error);
    return `檔案比較失敗：${error.message}`;
  }
}

// 13. 檔案分析報告生成
function generateFileAnalysisReport(userId, timeRange = null) {
  try {
    const userFiles = getRecentFiles(userId, timeRange, 50);
    
    if (userFiles.length === 0) {
      return '📊 分析報告：目前沒有檔案記錄';
    }
    
    // 統計資料
    const stats = {
      totalFiles: userFiles.length,
      fileTypes: {},
      sizeTotalKB: 0,
      analysisStatus: { '已分析': 0, '已儲存': 0 }
    };
    
    userFiles.forEach(file => {
      // 檔案類型統計
      const type = file.fileType;
      stats.fileTypes[type] = (stats.fileTypes[type] || 0) + 1;
      
      // 檔案大小統計
      const sizeKB = parseFloat(file.fileSize.replace(' KB', ''));
      if (!isNaN(sizeKB)) {
        stats.sizeTotalKB += sizeKB;
      }
      
      // 分析狀態統計
      const status = file.analysisStatus || '已儲存';
      stats.analysisStatus[status] = (stats.analysisStatus[status] || 0) + 1;
    });
    
    // 生成報告
    let report = `📊 檔案分析報告\n\n`;
    report += `📁 總檔案數：${stats.totalFiles}\n`;
    report += `💾 總大小：${(stats.sizeTotalKB / 1024).toFixed(2)} MB\n\n`;
    
    report += `📄 檔案類型分布：\n`;
    Object.entries(stats.fileTypes)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .forEach(([type, count]) => {
        report += `• ${type}: ${count}個\n`;
      });
    
    report += `\n🔍 分析狀態：\n`;
    Object.entries(stats.analysisStatus).forEach(([status, count]) => {
      report += `• ${status}: ${count}個\n`;
    });
    
    return report;
    
  } catch (error) {
    console.error('生成檔案分析報告錯誤:', error);
    return `📊 報告生成失敗：${error.message}`;
  }
}

// 14. 檔案分析效能監控
function monitorFileAnalysisPerformance() {
  try {
    const sheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName('分析任務佇列');
    if (!sheet) return null;
    
    const data = sheet.getDataRange().getValues();
    const tasks = data.slice(1); // 跳過標題行
    
    const performance = {
      totalTasks: tasks.length,
      completedTasks: tasks.filter(row => row[4] === '已完成').length,
      failedTasks: tasks.filter(row => row[4] === '失敗').length,
      pendingTasks: tasks.filter(row => row[4] === '處理中').length,
      averageTime: 0
    };
    
    // 計算平均處理時間（近似值）
    const recentTasks = tasks.filter(row => {
      const taskTime = new Date(row[0]);
      const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
      return taskTime > oneHourAgo && row[4] === '已完成';
    });
    
    if (recentTasks.length > 0) {
      performance.averageTime = Math.round(3600 / recentTasks.length); // 簡化計算
    }
    
    return performance;
    
  } catch (error) {
    console.error('檔案分析效能監控錯誤:', error);
    return null;
  }
}

// 15. 高級檔案內容提取（使用統一模型配置）
function extractAdvancedFileContent(file, fileName, mimeType) {
  try {
    let extractedContent = '';
    let extractionMethod = '';
    
    if (mimeType.includes('image/')) {
      // 圖片 OCR 文字提取
      const prompt = "請從這張圖片中提取所有可見的文字內容，包括標題、段落、標籤等。如果沒有文字內容，請描述圖片的主要視覺元素。用繁體中文回答。";
      extractedContent = callGemini(prompt, 'vision', file.getBlob());
      extractionMethod = '圖片OCR文字提取';
      
    } else if (mimeType.includes('text/') || fileName.endsWith('.txt') || fileName.endsWith('.md')) {
      // 純文字檔案直接讀取
      extractedContent = file.getBlob().getDataAsString('UTF-8');
      extractionMethod = '直接文字讀取';
      
    } else if (mimeType.includes('application/pdf')) {
      // PDF 檔案結構分析
      const prompt = `分析這個PDF檔案「${fileName}」的結構和內容特徵：
1. 推測文件類型和主要主題
2. 估計頁數和內容密度
3. 可能包含的章節結構
4. 適合的讀者群體
用繁體中文回答，限制在300字以內。`;
      
      extractedContent = callGemini(prompt, 'general');
      extractionMethod = 'PDF結構分析';
      
    } else {
      // 其他檔案類型的深度分析
      const prompt = `對檔案「${fileName}」(${mimeType})進行深度分析：
1. 根據檔案名稱和類型推測用途
2. 分析可能的內容結構
3. 評估檔案的重要性和價值
4. 建議最佳的使用方式
用繁體中文回答，限制在250字以內。`;
      
      extractedContent = callGemini(prompt, 'general');
      extractionMethod = '深度檔案分析';
    }
    
    return {
      success: true,
      content: extractedContent,
      method: extractionMethod,
      contentLength: extractedContent.length
    };
    
  } catch (error) {
    console.error('高級檔案內容提取錯誤:', error);
    return {
      success: false,
      error: error.message,
      method: '提取失敗'
    };
  }
}

// 16. 智能檔案標籤生成（使用統一模型配置）
function generateSmartFileTags(fileName, fileType, description, features) {
  try {
    const prompt = `根據以下檔案資訊，生成5-8個適合的分類標籤：

檔案名稱：${fileName}
檔案類型：${fileType}
描述：${description}
特徵：${features}

請生成具體、實用的標籤，用於檔案分類和搜尋。
標籤應該包含：
1. 檔案類型標籤（如：文件、圖片、資料等）
2. 內容主題標籤（如：工作、學習、個人等）
3. 功能用途標籤（如：參考、存檔、分享等）

只回答標籤清單，用逗號分隔，不要解釋。
例如：文件,工作,報告,重要,PDF,商業,分析,參考`;
    
    const response = callGemini(prompt, 'general');
    
    // 解析標籤
    const tags = response.split(/[,，]/).map(tag => tag.trim()).filter(tag => tag.length > 0);
    
    return tags.length > 0 ? tags : ['未分類'];
    
  } catch (error) {
    console.error('智能檔案標籤生成錯誤:', error);
    return ['分析失敗'];
  }
}

// 17. 檔案重要性評估（使用統一模型配置）
function assessFileImportance(fileName, fileType, fileSize, description, userContext = '') {
  try {
    const prompt = `評估以下檔案的重要性程度（1-10分）：

檔案資訊：
- 名稱：${fileName}
- 類型：${fileType}
- 大小：${fileSize}
- 描述：${description}
${userContext ? `- 用戶背景：${userContext}` : ''}

評估標準：
- 檔案類型的一般重要性
- 檔案名稱暗示的內容價值
- 檔案大小與內容豐富度
- 可能的使用頻率
- 替代性和稀有性

請用JSON格式回答：
{
  "score": 數字(1-10),
  "reason": "評估原因說明",
  "category": "重要性類別(低/中/高)",
  "recommendations": "使用建議"
}

只回答JSON，不要其他說明。`;
    
    const response = callGemini(prompt, 'general');
    
    try {
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const result = JSON.parse(jsonMatch[0]);
        return {
          score: result.score || 5,
          reason: result.reason || '無法評估',
          category: result.category || '中',
          recommendations: result.recommendations || '建議保留'
        };
      }
    } catch (parseError) {
      console.error('重要性評估結果解析錯誤:', parseError);
    }
    
    return { score: 5, reason: '評估解析失敗', category: '中', recommendations: '建議保留' };
    
  } catch (error) {
    console.error('檔案重要性評估錯誤:', error);
    return { score: 5, reason: '評估過程錯誤', category: '中', recommendations: '建議保留' };
  }
}

// 18. 延遲分析功能（從舊檔案移植）
function performDelayedAnalysis(fileInfo, userQuery, apiKey) {
  try {
    const prompt = `用戶詢問關於檔案「${fileInfo.fileName}」的問題：「${userQuery}」

檔案資訊：
- 檔案類型：${fileInfo.fileType}
- 檔案大小：${fileInfo.fileSize}
- 簡短描述：${fileInfo.description}
- 關鍵特徵：${fileInfo.features}

請根據用戶的具體問題，用繁體中文提供針對性的分析和回答。限制在200字以內。`;

    const payload = JSON.stringify({
      contents: [{
        parts: [{ text: prompt }]
      }]
    });

    const response = UrlFetchApp.fetch(
      `https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${apiKey}`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        payload: payload
      }
    );

    if (response.getResponseCode() !== 200) {
      throw new Error(`Gemini API 錯誤 ${response.getResponseCode()}`);
    }

    const result = JSON.parse(response.getContentText());
    return result.candidates[0].content.parts[0].text || '無法進行分析';
    
  } catch (error) {
    console.error('延遲分析錯誤:', error);
    return `分析過程中發生錯誤：${error.message}`;
  }
}

// 19. 更新檔案分析狀態（從舊檔案移植）
function updateFileAnalysisStatus(fileId, status, analysisResult = '') {
  try {
    const sheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName('檔案記憶庫');
    if (!sheet) return;
    
    const data = sheet.getDataRange().getValues();
    
    for (let i = 1; i < data.length; i++) { // 從第2行開始（跳過標題）
      if (data[i][2] === fileId) { // 檔案ID在第3列（索引2）
        sheet.getRange(i + 1, 10).setValue(status); // 分析狀態在第10列
        
        // 如果有分析結果，記錄到分析任務佇列
        if (analysisResult) {
          recordAnalysisTask(data[i][1], fileId, '延遲分析', '已完成', analysisResult, '');
        }
        
        break;
      }
    }
  } catch (error) {
    console.error('更新檔案分析狀態錯誤:', error);
  }
}

// 20. 記錄分析任務（從舊檔案移植）
function recordAnalysisTask(userId, fileId, analysisType, status, result, userQuery) {
  try {
    const sheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName('分析任務佇列');
    if (sheet) {
      const timestamp = new Date();
      sheet.appendRow([
        timestamp,      // 時間戳
        userId,         // 用戶ID
        fileId,         // 檔案ID
        analysisType,   // 分析類型
        status,         // 任務狀態
        result,         // 分析結果
        userQuery       // 用戶查詢
      ]);
    }
  } catch (error) {
    console.error('記錄分析任務錯誤:', error);
  }
}
