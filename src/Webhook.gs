// == LINE Webhook 主要入口與訊息路由模組 ==
// 專門處理 LINE Webhook 接收和訊息分發邏輯

// ===== HTTP 請求入口點 =====

/**
 * 🌐 處理 HTTP GET 請求（瀏覽器直接訪問）
 * 當用戶在瀏覽器中訪問 Web App URL 時會觸發此函數
 */
function doGet(e) {
  try {
    // 檢查系統狀態
    const healthCheck = systemHealthCheck();
    const config = getConfig();
    const versionInfo = getVersionInfo();
    
    // 生成 HTML 響應頁面
    const htmlContent = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LINE Bot 智能助理 - 系統狀態</title>
    <style>
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px; 
            margin: 40px auto; 
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            line-height: 1.6;
        }
        .container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
        }
        .status-badge {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            margin: 5px;
        }
        .status-healthy { background-color: #d4edda; color: #155724; }
        .status-warning { background-color: #fff3cd; color: #856404; }
        .status-error { background-color: #f8d7da; color: #721c24; }
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .info-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }
        .version { font-family: monospace; background: #e9ecef; padding: 4px 8px; border-radius: 4px; }
        .footer { 
            text-align: center; 
            margin-top: 30px; 
            padding-top: 20px; 
            border-top: 1px solid #e9ecef;
            color: #6c757d;
        }
        h1 { color: #333; margin: 0; }
        h3 { color: #495057; margin-top: 0; }
        .emoji { font-size: 1.2em; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><span class="emoji">🤖</span> LINE Bot 智能助理</h1>
            <p>由 AI 驅動的智能對話助手</p>
            <div class="status-badge status-${healthCheck.overall === 'healthy' ? 'healthy' : healthCheck.overall === 'warning' ? 'warning' : 'error'}">
                ${healthCheck.overall === 'healthy' ? '✅ 系統正常運行' : healthCheck.overall === 'warning' ? '⚠️ 系統警告' : '❌ 系統異常'}
            </div>
        </div>
        
        <div class="info-grid">
            <div class="info-card">
                <h3><span class="emoji">📊</span> 系統資訊</h3>
                <p><strong>版本：</strong> <span class="version">${versionInfo.version}</span></p>
                <p><strong>檢查時間：</strong> ${new Date().toLocaleString('zh-TW')}</p>
                <p><strong>架構：</strong> 模組化設計</p>
            </div>
            
            <div class="info-card">
                <h3><span class="emoji">🔧</span> 核心模組</h3>
                <p><strong>Webhook：</strong> ${healthCheck.modules.core === 'healthy' ? '✅' : '❌'} 運行中</p>
                <p><strong>工作表：</strong> ${healthCheck.modules.sheets === 'healthy' ? '✅' : '❌'} 運行中</p>
                <p><strong>記憶系統：</strong> ${healthCheck.modules.memory === 'healthy' ? '✅' : '❌'} 運行中</p>
            </div>
            
            <div class="info-card">
                <h3><span class="emoji">🤖</span> AI 功能</h3>
                <p><strong>Gemini API：</strong> ${config.geminiApiKey ? '✅ 已配置' : '❌ 未配置'}</p>
                <p><strong>一般模型：</strong> ${config.generalModel}</p>
                <p><strong>視覺模型：</strong> ${config.visionModel}</p>
            </div>
            
            <div class="info-card">
                <h3><span class="emoji">📱</span> LINE 整合</h3>
                <p><strong>Channel Token：</strong> ${config.lineChannelAccessToken ? '✅ 已配置' : '❌ 未配置'}</p>
                <p><strong>Webhook URL：</strong> 此頁面的 URL</p>
                <p><strong>群組支援：</strong> ✅ 已啟用</p>
            </div>
        </div>
        
        ${healthCheck.errors && healthCheck.errors.length > 0 ? `
        <div class="info-card" style="border-left-color: #dc3545; margin-top: 20px;">
            <h3><span class="emoji">❌</span> 錯誤資訊</h3>
            ${healthCheck.errors.map(error => `<p style="color: #dc3545;">• ${error}</p>`).join('')}
        </div>
        ` : ''}
        
        <div class="footer">
            <p><strong>💡 如何使用：</strong></p>
            <p>將此頁面的 URL 設定為 LINE Developers Console 中的 Webhook URL</p>
            <p>系統會自動處理來自 LINE 平台的所有訊息和事件</p>
            <p style="margin-top: 20px; font-size: 0.9em;">
                <a href="https://script.google.com/home/<USER>/${ScriptApp.getScriptId()}/edit" target="_blank" style="color: #667eea; text-decoration: none;">
                    🔧 編輯此專案
                </a>
            </p>
        </div>
    </div>
</body>
</html>
    `;
    
    // 返回 HTML 內容
    return HtmlService.createHtmlOutput(htmlContent)
      .setTitle('LINE Bot 智能助理 - 系統狀態')
      .setXFrameOptionsMode(HtmlService.XFrameOptionsMode.ALLOWALL);
      
  } catch (error) {
    console.error('doGet 錯誤:', error);
    
    // 錯誤時返回簡單的錯誤頁面
    const errorHtml = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>LINE Bot - 系統錯誤</title>
    <style>
        body { font-family: Arial, sans-serif; text-align: center; padding: 50px; background-color: #f8f9fa; }
        .error-box { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); display: inline-block; }
        .error-icon { font-size: 4em; margin-bottom: 20px; }
    </style>
</head>
<body>
    <div class="error-box">
        <div class="error-icon">⚠️</div>
        <h2>系統初始化錯誤</h2>
        <p>LINE Bot 遇到了一些問題，請檢查系統配置</p>
        <p style="color: #6c757d; font-size: 0.9em;">錯誤詳情：${error.toString()}</p>
        <p style="margin-top: 30px;">
            <a href="https://script.google.com/home/<USER>/${ScriptApp.getScriptId()}/edit" 
               style="color: #007bff; text-decoration: none;">前往編輯器檢查</a>
        </p>
    </div>
</body>
</html>
    `;
    
    return HtmlService.createHtmlOutput(errorHtml)
      .setTitle('LINE Bot - 系統錯誤');
  }
}

/**
 * 🔌 處理 LINE Webhook POST 請求
 * LINE 平台會發送用戶訊息到此函數
 */
function doPost(e) {
  try {
    // 初始化工作表（確保所有必要的工作表都存在）
    initializeSheets();
    
    // 解析 LINE Webhook 請求
    const json = JSON.parse(e.postData.contents);
    const event = json.events[0];
    
    if (!event) {
      console.log('沒有收到事件');
      return ContentService.createTextOutput('OK').setMimeType(ContentService.MimeType.TEXT);
    }
    
    const userId = event.source?.userId || 'unknown';
    const groupId = event.source?.groupId || '';
    const roomId = event.source?.roomId || '';
    const replyToken = event.replyToken;
    
    // 判斷來源類型
    const sourceType = groupId ? 'group' : roomId ? 'room' : 'user';
    
    // 記錄活動日誌，包含來源資訊
    logActivity('收到訊息', `來自${sourceType}: ${userId}, 事件類型: ${event.type}, 訊息類型: ${event.message?.type || 'N/A'}, 群組ID: ${groupId}`);
    
    // 🔧 群組邏輯：分別處理文字和媒體
    if (groupId || roomId) {
      handleGroupMessage(event, replyToken, userId, sourceType);
    } else {
      handlePrivateMessage(event, replyToken, userId, sourceType);
    }
    
    // 返回成功響應
    return ContentService.createTextOutput('OK').setMimeType(ContentService.MimeType.TEXT);
    
  } catch (error) {
    console.error('doPost 錯誤:', error);
    logActivity('系統錯誤', error.toString());
    
    // 即使出錯也要返回成功響應，避免 LINE 平台重複發送
    return ContentService.createTextOutput('ERROR').setMimeType(ContentService.MimeType.TEXT);
  }
}

// ===== 訊息處理路由 =====

// 2. 處理群組訊息 - 🔧 使用修復版記錄函數
function handleGroupMessage(event, replyToken, userId, sourceType) {
  try {
    // 🆕 記錄所有群組發言（使用修復版函數）
    try {
      recordGroupMessage(event, userId, sourceType);
      console.log(`✅ 群組發言已記錄: 用戶=${userId}, 類型=${event.message?.type || 'unknown'}`);
    } catch (recordError) {
      console.error('❌ 群組發言記錄失敗:', recordError);
      // 記錄失敗不影響其他功能繼續執行
    }
    
    if (event.message?.type === 'text') {
      // 群組文字訊息：只處理 ! 開頭的
      const normalizedText = normalizeText(event.message.text);
      if (!normalizedText.startsWith('!')) {
        logActivity('群組靜默', `群組中非!開頭訊息，不回應: ${normalizedText.substring(0, 20)}...`);
        return; // 不處理不回應
      }
      // 處理 ! 開頭的文字訊息
      handleTextMessage(event.message.text, replyToken, userId, sourceType);
      
    } else if (['image', 'video', 'audio', 'file'].includes(event.message?.type)) {
      // 群組媒體檔案：處理和記錄，但不回應
      logActivity('群組媒體處理', `群組中媒體檔案，處理但不回應: 類型=${event.message.type}`);
      handleMediaMessage(event.message, null, userId, sourceType); // replyToken = null 表示不回應
      
    } else {
      // 群組其他訊息類型：完全忽略
      const messageType = event.message?.type || '未知';
      logActivity('群組靜默', `群組中其他訊息類型，忽略: ${messageType}`);
    }
  } catch (error) {
    console.error('處理群組訊息錯誤:', error);
    logActivity('群組訊息錯誤', error.toString());
    
    // 發生錯誤時發送通用錯誤訊息
    if (replyToken) {
      replyMessage(replyToken, '⚠️ 處理訊息時發生錯誤，請稍後再試或聯繫管理員');
    }
  }
}

// 3. 處理個人訊息
function handlePrivateMessage(event, replyToken, userId, sourceType) {
  try {
    if (event.message && event.message.type === 'text') {
      handleTextMessage(event.message.text, replyToken, userId, sourceType);
    } else if (['image', 'video', 'audio', 'file'].includes(event.message?.type)) {
      handleMediaMessage(event.message, replyToken, userId, sourceType);
    } else {
      // 其他訊息類型靜默處理
      const messageType = event.message?.type || '未知';
      logActivity('靜默處理', `類型: ${messageType}, 來自: ${userId}(${sourceType})`);
    }
  } catch (error) {
    console.error('處理個人訊息錯誤:', error);
    logActivity('個人訊息錯誤', error.toString());
    
    // 發生錯誤時發送通用錯誤訊息
    if (replyToken) {
      replyMessage(replyToken, '⚠️ 處理訊息時發生錯誤，請稍後再試或聯繫管理員');
    }
  }
}

// 4. 訊息類型路由器
function routeMessageByType(messageType, event, replyToken, userId, sourceType) {
  try {
    switch(messageType) {
      case 'text':
        return handleTextMessage(event.message.text, replyToken, userId, sourceType);
      
      case 'image':
      case 'video':
      case 'audio':
      case 'file':
        return handleMediaMessage(event.message, replyToken, userId, sourceType);
      
      case 'location':
        return handleLocationMessage(event.message, replyToken, userId, sourceType);
      
      case 'sticker':
        return handleStickerMessage(event.message, replyToken, userId, sourceType);
      
      case 'postback':
        return handlePostbackMessage(event.postback, replyToken, userId, sourceType);
      
      default:
        logActivity('未知訊息類型', `類型: ${messageType}, 來自: ${userId}(${sourceType})`);
        return;
    }
  } catch (error) {
    console.error('訊息路由錯誤:', error);
    logActivity('訊息路由錯誤', error.toString());
    
    if (replyToken) {
      replyMessage(replyToken, '⚠️ 處理訊息時發生錯誤');
    }
  }
}

// 5. 處理位置訊息
function handleLocationMessage(message, replyToken, userId, sourceType) {
  try {
    const { title, address, latitude, longitude } = message;
    
    // 記錄位置資訊
    const sheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName('地點日誌');
    if (sheet) {
      const timestamp = new Date();
      sheet.appendRow([timestamp, userId, title || '未知位置', longitude, latitude, address || '']);
    }
    
    logActivity('位置訊息', `用戶${userId}(${sourceType})分享位置: ${title || address}`);
    
    // 只在個人對話中回應位置訊息
    if (replyToken && sourceType === 'user') {
      const responseText = `📍 已收到您的位置資訊！\n\n🏷️ 位置：${title || '未知位置'}\n📬 地址：${address || '未提供地址'}\n🗺️ 座標：${latitude}, ${longitude}\n\n💡 您可以說「推薦附近的美食」來獲得建議`;
      replyMessage(replyToken, responseText);
    }
    
  } catch (error) {
    console.error('處理位置訊息錯誤:', error);
    logActivity('位置訊息錯誤', error.toString());
  }
}

// 6. 處理貼圖訊息
function handleStickerMessage(message, replyToken, userId, sourceType) {
  try {
    const { stickerId, packageId } = message;
    logActivity('貼圖訊息', `用戶${userId}(${sourceType})發送貼圖: packageId=${packageId}, stickerId=${stickerId}`);
    
    // 貼圖訊息通常不需要回應，只記錄即可
    // 除非是特殊的貼圖互動邏輯
    
  } catch (error) {
    console.error('處理貼圖訊息錯誤:', error);
    logActivity('貼圖訊息錯誤', error.toString());
  }
}

// 7. 處理 Postback 訊息
function handlePostbackMessage(postback, replyToken, userId, sourceType) {
  try {
    const { data, params } = postback;
    logActivity('Postback訊息', `用戶${userId}(${sourceType})觸發postback: ${data}`);
    
    // 根據 postback data 執行不同邏輯
    switch(data) {
      case 'help_menu':
        const helpText = generateHelpMessage(sourceType);
        if (replyToken) replyMessage(replyToken, helpText);
        break;
        
      case 'file_analysis':
        // 檔案分析相關的 postback 處理
        if (replyToken) replyMessage(replyToken, '🔍 請上傳檔案或提供 Google Drive 連結進行分析');
        break;
        
      default:
        logActivity('未知Postback', `data: ${data}, 來自: ${userId}(${sourceType})`);
    }
    
  } catch (error) {
    console.error('處理Postback訊息錯誤:', error);
    logActivity('Postback訊息錯誤', error.toString());
  }
}

// 8. 事件類型路由器（處理非訊息事件）
function routeEventByType(eventType, event, userId, sourceType) {
  try {
    switch(eventType) {
      case 'follow':
        return handleFollowEvent(event, userId);
      
      case 'unfollow':
        return handleUnfollowEvent(event, userId);
      
      case 'join':
        return handleJoinEvent(event, userId, sourceType);
      
      case 'leave':
        return handleLeaveEvent(event, userId, sourceType);
      
      case 'memberJoined':
        return handleMemberJoinedEvent(event, sourceType);
      
      case 'memberLeft':
        return handleMemberLeftEvent(event, sourceType);
      
      default:
        logActivity('未知事件類型', `事件: ${eventType}, 來自: ${userId}(${sourceType})`);
    }
  } catch (error) {
    console.error('事件路由錯誤:', error);
    logActivity('事件路由錯誤', error.toString());
  }
}

// 9. 處理用戶關注事件
function handleFollowEvent(event, userId) {
  try {
    logActivity('用戶關注', `新用戶關注: ${userId}`);
    
    const welcomeText = `🎉 歡迎使用 LINE 智能助理！\n\n🤖 我是您的 AI 助手，可以幫您：\n• 📝 記錄筆記和想法\n• 📄 分析檔案和圖片\n• 🔍 智能搜尋和問答\n• 🧠 記住我們的對話\n• 🆕 群組聊天記錄與查詢\n\n💡 輸入「help」查看完整功能說明\n\n讓我們開始吧！✨`;
    
    if (event.replyToken) {
      replyMessage(event.replyToken, welcomeText);
    }
    
  } catch (error) {
    console.error('處理關注事件錯誤:', error);
    logActivity('關注事件錯誤', error.toString());
  }
}

// 10. 處理用戶取消關注事件
function handleUnfollowEvent(event, userId) {
  try {
    logActivity('用戶取消關注', `用戶取消關注: ${userId}`);
    // 可以在這裡清理用戶數據或記錄統計資訊
  } catch (error) {
    console.error('處理取消關注事件錯誤:', error);
    logActivity('取消關注事件錯誤', error.toString());
  }
}

// 11. 處理加入群組事件
function handleJoinEvent(event, userId, sourceType) {
  try {
    const groupId = event.source.groupId || event.source.roomId;
    logActivity('加入群組', `BOT加入群組: ${groupId}, 邀請者: ${userId}`);
    
    const groupWelcomeText = `👋 大家好！我是智能助理 BOT\n\n🔧 在群組中使用方式：\n• 所有指令都要加「!」前綴\n• 例如：!help、!記錄、!測試\n• 上傳檔案會自動分析但不回應\n• 🆕 自動記錄群組發言，可查詢成員發言記錄\n\n💡 輸入「!help」查看完整功能\n\n期待為大家服務！🚀`;
    
    if (event.replyToken) {
      replyMessage(event.replyToken, groupWelcomeText);
    }
    
  } catch (error) {
    console.error('處理加入群組事件錯誤:', error);
    logActivity('加入群組事件錯誤', error.toString());
  }
}

// 12. 處理離開群組事件
function handleLeaveEvent(event, userId, sourceType) {
  try {
    const groupId = event.source.groupId || event.source.roomId;
    logActivity('離開群組', `BOT離開群組: ${groupId}`);
    // 可以在這裡清理群組相關數據
  } catch (error) {
    console.error('處理離開群組事件錯誤:', error);
    logActivity('離開群組事件錯誤', error.toString());
  }
}

// 13. 處理群組成員加入事件
function handleMemberJoinedEvent(event, sourceType) {
  try {
    const joinedUsers = event.joined?.members || [];
    joinedUsers.forEach(member => {
      logActivity('成員加入', `新成員加入群組: ${member.userId}`);
    });
  } catch (error) {
    console.error('處理成員加入事件錯誤:', error);
    logActivity('成員加入事件錯誤', error.toString());
  }
}

// 14. 處理群組成員離開事件
function handleMemberLeftEvent(event, sourceType) {
  try {
    const leftUsers = event.left?.members || [];
    leftUsers.forEach(member => {
      logActivity('成員離開', `成員離開群組: ${member.userId}`);
    });
  } catch (error) {
    console.error('處理成員離開事件錯誤:', error);
    logActivity('成員離開事件錯誤', error.toString());
  }
}

// ===== 🧪 測試函數 =====

// 15. 測試函數：模擬不同類型的訊息
function testWebhookRouting() {
  console.log('=== 測試 Webhook 路由 ===');
  
  // 模擬測試數據
  const testEvents = [
    {
      type: 'message',
      message: { type: 'text', text: '!測試' },
      source: { userId: 'test-user-1' },
      replyToken: 'test-reply-token'
    },
    {
      type: 'message', 
      message: { type: 'text', text: '測試' },
      source: { userId: 'test-user-2', groupId: 'test-group' },
      replyToken: 'test-reply-token'
    },
    {
      type: 'follow',
      source: { userId: 'test-user-3' },
      replyToken: 'test-reply-token'
    }
  ];
  
  testEvents.forEach((event, index) => {
    console.log(`測試事件 ${index + 1}: ${event.type} - ${event.message?.type || '非訊息事件'}`);
    
    const userId = event.source?.userId || 'unknown';
    const groupId = event.source?.groupId || '';
    const sourceType = groupId ? 'group' : 'user';
    
    console.log(`  來源類型: ${sourceType}, 用戶: ${userId}`);
  });
  
  console.log('✅ Webhook 路由測試完成');
}

// 🔧 群組聊天記錄快速修復函數
function quickFixGroupChat() {
  console.log('🔧 快速修復群組聊天記錄功能...');
  try {
    return fixGroupChatRecording();
  } catch (error) {
    console.error('快速修復失敗:', error);
    return `❌ 快速修復失敗: ${error.message}`;
  }
}
