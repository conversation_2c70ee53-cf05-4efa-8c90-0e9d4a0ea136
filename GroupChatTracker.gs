// == 群組聊天記錄與查詢系統 ==
// 🚀 AI First + 高性能緩存設計
// 🎯 智能用戶映射 + AI理解數量限制

// === 全局緩存變量 ===
let userMappingCache = {}; // 用戶名映射緩存
let uniqueUsersCache = []; // 唯一用戶列表緩存
let cacheInitialized = false; // 緩存初始化狀態

// 1. 設定群組聊天記錄工作表標題行
function setupGroupChatSheetHeaders() {
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  let sheet = ss.getSheetByName('群組發言記錄');
  
  if (!sheet) {
    sheet = ss.insertSheet('群組發言記錄');
    console.log('已建立群組發言記錄工作表');
  }
  
  // 檢查是否已有標題行
  if (sheet.getRange('A1').getValue() === '') {
    const headers = [
      '時間戳', '群組ID', '用戶ID', '用戶顯示名稱', '訊息內容', '訊息類型', '回應狀態'
    ];
    sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
    sheet.getRange(1, 1, 1, headers.length).setFontWeight('bold');
    sheet.getRange(1, 1, 1, headers.length).setBackground('#e1f5fe');
    console.log('已設定群組發言記錄工作表標題行');
  }
  
  return sheet;
}

// 2. 記錄群組發言
function recordGroupMessage(event, userId, sourceType) {
  try {
    console.log(`🔄 開始記錄群組發言: 用戶=${userId}, 來源=${sourceType}`);

    // 1. 確保工作表存在
    const ss = SpreadsheetApp.getActiveSpreadsheet();
    let sheet = ss.getSheetByName('群組發言記錄');

    if (!sheet) {
      sheet = ss.insertSheet('群組發言記錄');
      const headers = ['時間戳', '群組ID', '用戶ID', '用戶顯示名稱', '訊息內容', '訊息類型', '回應狀態'];
      sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
      sheet.getRange(1, 1, 1, headers.length).setFontWeight('bold');
      sheet.getRange(1, 1, 1, headers.length).setBackground('#e1f5fe');
      console.log('✅ 已建立群組發言記錄工作表');
    }

    // 2. 提取基本資訊
    const timestamp = new Date();
    const groupId = event.source?.groupId || event.source?.roomId || 'unknown';
    const messageType = event.message?.type || event.type || 'unknown';

    // 3. 處理訊息內容
    let messageContent = '';
    let responseStatus = '已記錄';

    if (event.message?.type === 'text') {
      messageContent = event.message.text || '';
      const normalizedText = messageContent.replace(/[！？。，；：]/g, function(match) {
        return { '！': '!', '？': '?', '。': '.', '，': ',', '；': ';', '：': ':' }[match] || match;
      }).trim();

      responseStatus = normalizedText.startsWith('!') ? '已回應' : '靜默記錄';
    } else if (['image', 'video', 'audio', 'file'].includes(event.message?.type)) {
      const mediaTypeMap = {
        'image': '圖片',
        'video': '影片',
        'audio': '音訊',
        'file': '檔案'
      };
      messageContent = `[${mediaTypeMap[event.message.type] || '媒體'}]`;
      responseStatus = '媒體已處理';
    } else {
      messageContent = `[${messageType}]`;
      responseStatus = '已記錄';
    }

    // 4. 獲取用戶顯示名稱
    let displayName = userId;

    try {
      const config = getConfig();
      if (config && config.lineChannelAccessToken && groupId !== 'unknown') {
        const url = `https://api.line.me/v2/bot/group/${groupId}/member/${userId}`;
        const response = UrlFetchApp.fetch(url, {
          method: 'GET',
          headers: {
            'Authorization': 'Bearer ' + config.lineChannelAccessToken
          },
          muteHttpExceptions: true
        });

        if (response.getResponseCode() === 200) {
          const memberInfo = JSON.parse(response.getContentText());
          displayName = memberInfo.displayName || userId;
        }
      }
    } catch (nameError) {
      console.log(`無法獲取用戶顯示名稱，使用 userId: ${nameError.message}`);
    }

    // 5. 寫入資料
    const rowData = [
      timestamp,
      groupId,
      userId,
      displayName,
      messageContent,
      messageType,
      responseStatus
    ];

    sheet.appendRow(rowData);
    console.log(`✅ 成功記錄群組發言: ${displayName}(${userId}) - ${messageContent.substring(0, 30)}...`);

    // 6. 🔄 更新緩存：新用戶時重置緩存
    if (cacheInitialized && !uniqueUsersCache.includes(displayName)) {
      console.log(`🔄 發現新用戶，重置緩存: ${displayName}`);
      resetUserMappingCache();
    }

    try {
      logActivity('群組發言記錄', `群組:${groupId}, 用戶:${displayName}(${userId}), 內容:${messageContent.substring(0, 30)}...`);
    } catch (logError) {
      console.log(`活動日誌記錄失敗: ${logError.message}`);
    }

    return true;

  } catch (error) {
    console.error('❌ 記錄群組發言錯誤:', error);

    try {
      logActivity('群組發言記錄錯誤', `用戶:${userId}, 錯誤:${error.toString()}`);
    } catch (logError) {
      console.error('活動日誌記錄也失敗:', logError);
    }

    return false;
  }
}

// 3. 🚀 核心功能：初始化用戶映射緩存
function initializeUserMappingCache() {
  try {
    console.log('🚀 初始化用戶映射緩存...');
    
    if (cacheInitialized) {
      console.log('✅ 緩存已初始化，跳過');
      return true;
    }
    
    // 1. 提取所有唯一用戶名稱
    const uniqueUsers = getUniqueDisplayNames();
    
    if (uniqueUsers.length === 0) {
      console.log('❌ 沒有找到用戶數據，緩存初始化失敗');
      return false;
    }
    
    console.log(`📊 找到 ${uniqueUsers.length} 個唯一用戶`);
    uniqueUsersCache = uniqueUsers;
    
    // 2. 🤖 使用 AI 一次性建立映射表
    const mappingTable = buildUserMappingWithAI(uniqueUsers);
    
    if (mappingTable) {
      userMappingCache = mappingTable;
      cacheInitialized = true;
      console.log('✅ 用戶映射緩存初始化成功');
      console.log(`🗂️ 緩存包含 ${Object.keys(userMappingCache).length} 個映射`);
      return true;
    } else {
      console.log('❌ AI 映射建立失敗');
      return false;
    }
    
  } catch (error) {
    console.error('❌ 緩存初始化錯誤:', error);
    return false;
  }
}

// 4. 🔍 提取唯一用戶名稱列表
function getUniqueDisplayNames() {
  try {
    const sheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName('群組發言記錄');
    if (!sheet) {
      console.log('群組發言記錄工作表不存在');
      return [];
    }
    
    const lastRow = sheet.getLastRow();
    if (lastRow <= 1) {
      console.log('工作表沒有數據');
      return [];
    }
    
    // 提取第4列（用戶顯示名稱）的所有數據
    const displayNameRange = sheet.getRange(2, 4, lastRow - 1, 1);
    const displayNames = displayNameRange.getValues()
      .map(row => String(row[0] || '').trim())
      .filter(name => name !== '');
    
    // 去重並排序
    const uniqueNames = [...new Set(displayNames)].sort();
    
    console.log(`📋 提取到 ${uniqueNames.length} 個唯一用戶名稱:`, uniqueNames);
    return uniqueNames;
    
  } catch (error) {
    console.error('提取唯一用戶名稱錯誤:', error);
    return [];
  }
}

// 5. 🤖 AI 一次性建立用戶映射表 - 使用統一提示詞系統
function buildUserMappingWithAI(uniqueDisplayNames) {
  try {
    const config = getConfig();

    if (!config.geminiApiKey) {
      console.log('沒有 Gemini API Key，使用基本映射');
      return buildBasicMapping(uniqueDisplayNames);
    }

    console.log('🤖 使用 AI 建立用戶映射表...');

    const userListText = uniqueDisplayNames.map((name, index) => `${index + 1}. ${name}`).join('\n');

    try {
      // 🎯 使用統一提示詞系統
      const aiResponse = callAIWithPrompt('USER_MAPPING', {
        userList: userListText
      });

      const jsonMatch = aiResponse.match(/\{[\s\S]*\}/);

      if (jsonMatch) {
        const mappingTable = JSON.parse(jsonMatch[0]);

        // 添加原始名稱的直接映射
        uniqueDisplayNames.forEach(name => {
          mappingTable[name.toLowerCase()] = name;
          mappingTable[name] = name;
        });

        console.log(`🤖 AI 建立了 ${Object.keys(mappingTable).length} 個映射關係`);
        return mappingTable;
      }
    } catch (parseError) {
      console.error('AI 映射解析失敗:', parseError);
    }

    // AI 失敗時使用基本映射
    return buildBasicMapping(uniqueDisplayNames);

  } catch (error) {
    console.error('AI 建立映射表錯誤:', error);
    return buildBasicMapping(uniqueDisplayNames);
  }
}

// 6. 📝 基本映射（AI 失敗時的備用方案）
function buildBasicMapping(uniqueDisplayNames) {
  const basicMapping = {};
  
  uniqueDisplayNames.forEach(name => {
    const cleanName = name.toLowerCase()
      .replace(/[⚡️📱💻🔥✨🌟]/g, '')
      .replace(/[（）()]/g, '')
      .replace(/[/\\]/g, '');
    
    basicMapping[name.toLowerCase()] = name;
    basicMapping[name] = name;
    basicMapping[cleanName] = name;
    
    // 提取英文名稱
    const englishMatch = name.match(/[A-Za-z]+/g);
    if (englishMatch) {
      englishMatch.forEach(en => {
        basicMapping[en.toLowerCase()] = name;
      });
    }
  });
  
  console.log(`📝 建立基本映射 ${Object.keys(basicMapping).length} 個關係`);
  return basicMapping;
}

// 7. 🔍 智能用戶名稱解析（帶緩存）
function resolveUserNameWithCache(queryUserName) {
  try {
    // 確保緩存已初始化
    if (!cacheInitialized) {
      const initSuccess = initializeUserMappingCache();
      if (!initSuccess) {
        console.log('❌ 緩存初始化失敗，使用直接匹配');
        return queryUserName;
      }
    }
    
    const queryKey = queryUserName.toLowerCase();
    
    // 檢查緩存
    if (userMappingCache[queryKey]) {
      console.log(`✅ 緩存命中: "${queryUserName}" → "${userMappingCache[queryKey]}"`);
      return userMappingCache[queryKey];
    }
    
    // 緩存未命中，使用 AI 判斷一次
    console.log(`🤖 緩存未命中，AI 判斷: "${queryUserName}"`);
    
    const config = getConfig();
    if (config.geminiApiKey) {
      const resolvedName = resolveUserNameWithAI(queryUserName, uniqueUsersCache);
      
      if (resolvedName) {
        // 更新緩存
        userMappingCache[queryKey] = resolvedName;
        userMappingCache[queryUserName] = resolvedName;
        console.log(`🔄 緩存更新: "${queryUserName}" → "${resolvedName}"`);
        return resolvedName;
      }
    }
    
    // 最終備用：返回原查詢名稱
    console.log(`❌ 無法解析用戶名稱: "${queryUserName}"`);
    return queryUserName;
    
  } catch (error) {
    console.error('用戶名稱解析錯誤:', error);
    return queryUserName;
  }
}

// 8. 🤖 AI 單次用戶名稱解析 - 使用統一提示詞系統
function resolveUserNameWithAI(queryUserName, availableUsers) {
  try {
    const userListText = availableUsers.map((name, index) => `${index + 1}. ${name}`).join('\n');

    // 🎯 使用統一提示詞系統
    const aiResponse = callAIWithPrompt('USER_NAME_RESOLVE', {
      queryUserName: queryUserName,
      availableUsers: userListText
    }).trim();

    // 檢查AI回應是否在可用用戶列表中
    const foundUser = availableUsers.find(user =>
      aiResponse.includes(user) || user.includes(aiResponse)
    );

    if (foundUser) {
      console.log(`🤖 AI 解析成功: "${queryUserName}" → "${foundUser}"`);
      return foundUser;
    }

    console.log(`🤖 AI 未找到匹配: "${queryUserName}"`);
    return null;

  } catch (error) {
    console.error('AI 用戶名稱解析錯誤:', error);
    return null;
  }
}

// 9. 🤖 AI 理解查詢限制（數量 + 用戶名）- 使用統一提示詞系統
function parseQueryWithAI(userQuery) {
  try {
    const config = getConfig();

    if (!config.geminiApiKey) {
      return parseQueryBasic(userQuery);
    }

    try {
      // 🎯 使用統一提示詞系統
      const aiResponse = callAIWithPrompt('QUERY_PARSING', {
        userQuery: userQuery
      });

      const jsonMatch = aiResponse.match(/\{[\s\S]*\}/);

      if (jsonMatch) {
        const parseResult = JSON.parse(jsonMatch[0]);

        // 驗證和補充數據
        if (!parseResult.recordLimit || parseResult.recordLimit === null) {
          parseResult.recordLimit = 50; // 預設50筆
        }

        console.log('🤖 AI 查詢解析結果:', parseResult);
        return parseResult;
      }
    } catch (parseError) {
      console.error('AI 查詢解析失敗:', parseError);
    }

    // AI 失敗時使用基本解析
    return parseQueryBasic(userQuery);

  } catch (error) {
    console.error('AI 查詢解析錯誤:', error);
    return parseQueryBasic(userQuery);
  }
}

// 10. 📝 基本查詢解析（備用方案）
function parseQueryBasic(userQuery) {
  const result = {
    targetUser: '',
    recordLimit: 50,
    queryType: 'summary',
    isValidQuery: false,
    analysisPrompt: ''
  };
  
  // 提取數字
  const numberMatch = userQuery.match(/(\d+)筆|(\d+)條|(\d+)個/);
  if (numberMatch) {
    result.recordLimit = parseInt(numberMatch[1] || numberMatch[2] || numberMatch[3]);
  }
  
  // 檢查是否要求全部
  if (userQuery.includes('所有') || userQuery.includes('全部')) {
    result.recordLimit = 'all';
  }
  
  // 提取用戶名（簡單方式）
  const patterns = [
    /把群[裡裏](.+?)的對話/,
    /(.+?)都講了/,
    /(.+?)說了什麼/,
    /總結(.+?)的/,
    /分析(.+?)的/
  ];
  
  for (const pattern of patterns) {
    const match = userQuery.match(pattern);
    if (match) {
      result.isValidQuery = true;
      result.targetUser = match[1].trim();
      result.queryType = userQuery.includes('你覺得') || userQuery.includes('分析') ? 'analysis' : 'summary';
      if (result.queryType === 'analysis') {
        result.analysisPrompt = userQuery;
      }
      break;
    }
  }
  
  console.log('📝 基本查詢解析結果:', result);
  return result;
}

// 11. 🚀 高效能群組發言搜索（核心優化版）
function searchUserMessages(targetUser, groupId = null, timeRange = '7days', recordLimit = 50) {
  try {
    console.log(`🔍 搜尋用戶發言: 用戶=${targetUser}, 限制=${recordLimit}筆`);
    
    const sheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName('群組發言記錄');
    if (!sheet) {
      console.log('群組發言記錄工作表不存在');
      return [];
    }
    
    // 🚀 Step 1: 智能用戶名稱解析（使用緩存）
    const resolvedUserName = resolveUserNameWithCache(targetUser);
    console.log(`👤 解析用戶名稱: "${targetUser}" → "${resolvedUserName}"`);
    
    // Step 2: 計算時間範圍
    let cutoffTime = new Date();
    switch(timeRange) {
      case '24h':
        cutoffTime.setHours(cutoffTime.getHours() - 24);
        break;
      case '7days':
        cutoffTime.setDate(cutoffTime.getDate() - 7);
        break;
      case '30days':
        cutoffTime.setDate(cutoffTime.getDate() - 30);
        break;
      case 'all':
        cutoffTime = new Date(0);
        break;
      default:
        cutoffTime.setDate(cutoffTime.getDate() - 7);
    }
    
    // Step 3: 高效數據過濾（精確字符串匹配）
    const data = sheet.getDataRange().getValues();
    const userMessages = data.slice(1) // 跳過標題行
      .filter(row => {
        try {
          const timestamp = new Date(row[0]);
          const displayName = String(row[3] || '');
          const messageContent = String(row[4] || '');
          const messageType = String(row[5] || 'text');
          
          // 時間篩選
          if (timestamp < cutoffTime) return false;
          
          // 只包含文字訊息
          if (messageType !== 'text') return false;
          
          // 檢查內容有效性
          if (!messageContent || typeof messageContent !== 'string') return false;
          
          // 排除 Bot 命令
          if (messageContent.startsWith('!')) return false;
          
          // 🚀 精確用戶名匹配（毫秒級）
          return displayName === resolvedUserName;
          
        } catch (rowError) {
          console.error(`處理行數據錯誤: ${rowError.message}`);
          return false;
        }
      })
      .sort((a, b) => new Date(b[0]) - new Date(a[0])); // 按時間倒序
    
    // Step 4: 🎯 應用記錄限制
    let limitedMessages;
    if (recordLimit === 'all') {
      limitedMessages = userMessages;
    } else {
      const limit = typeof recordLimit === 'number' ? recordLimit : 50;
      limitedMessages = userMessages.slice(0, limit);
    }
    
    // Step 5: 格式化結果
    const result = limitedMessages.map(row => ({
      timestamp: row[0],
      groupId: row[1],
      userId: row[2],
      displayName: String(row[3] || ''),
      messageContent: String(row[4] || ''),
      messageType: String(row[5] || 'text')
    }));
    
    console.log(`✅ 找到用戶「${targetUser}」的 ${result.length}/${userMessages.length} 條發言記錄`);
    return result;
    
  } catch (error) {
    console.error('搜尋用戶發言記錄錯誤:', error);
    return [];
  }
}

// 12. 🤖 智能查詢處理（整合版）
function handleSmartGroupQuery(userQuery, userId, sourceType) {
  try {
    console.log(`🤖 處理智能群組查詢: ${userQuery}`);
    
    // Step 1: AI 解析查詢（提取用戶名和數量限制）
    const queryData = parseQueryWithAI(userQuery);
    
    if (!queryData.isValidQuery) {
      return '🤔 我理解您想查詢群組成員的發言，但無法確定具體要查詢誰。請明確指出用戶名稱？';
    }
    
    // Step 2: 搜尋發言記錄（高效版）
    const userMessages = searchUserMessages(
      queryData.targetUser, 
      null, 
      '30days', 
      queryData.recordLimit
    );
    
    if (userMessages.length === 0) {
      return `🤔 沒有找到「${queryData.targetUser}」的發言記錄\n\n💡 可能原因：\n• 用戶名稱不完全匹配\n• 該用戶最近沒有發言\n• 記錄系統剛啟用`;
    }
    
    // Step 3: 生成智能回應
    if (queryData.queryType === 'analysis') {
      return generateUserAnalysisWithAI(queryData.targetUser, userMessages, queryData.analysisPrompt);
    } else {
      return generateUserSummaryWithAI(queryData.targetUser, userMessages, queryData.recordLimit);
    }
    
  } catch (error) {
    console.error('智能群組查詢處理錯誤:', error);
    return `⚠️ 查詢處理失敗：${error.message}`;
  }
}

// 13. 🤖 AI 生成用戶摘要（自然聊天版）
function generateUserSummaryWithAI(targetUser, userMessages, recordLimit) {
  try {
    const actualCount = userMessages.length;
    const config = getConfig();

    if (actualCount === 0) {
      return `🤔 沒找到 ${targetUser} 的發言記錄耶`;
    }

    // 🎯 使用 AI 生成自然的摘要，而不是機械式列表
    if (config.geminiApiKey && actualCount > 0) {
      const summaryMessages = userMessages.slice(0, 15); // 取前15條用於摘要
      const messageContext = summaryMessages.map((msg, index) =>
        `${index + 1}. [${formatTimeAgo(msg.timestamp)}] ${msg.messageContent}`
      ).join('\n');

      try {
        const aiSummary = callAIWithPrompt('USER_SUMMARY', {
          targetUser: targetUser,
          totalCount: actualCount,
          messageContext: messageContext
        });

        return `💬 ${targetUser} 最近都在聊：\n\n${aiSummary}\n\n📊 總共 ${actualCount} 條發言`;

      } catch (aiError) {
        console.error('AI 摘要生成失敗:', aiError);
        // 降級到簡單列表
      }
    }

    // 備用：簡單列表（AI 失敗時）
    const limitText = recordLimit === 'all' ? '全部' : `最近${recordLimit}筆`;
    let summary = `💬 ${targetUser} 的發言記錄 (${limitText})\n\n`;
    summary += `📊 找到 ${actualCount} 條發言記錄\n`;

    if (actualCount > 0) {
      summary += `⏰ 時間範圍：${formatTimeAgo(userMessages[userMessages.length - 1].timestamp)} 到 ${formatTimeAgo(userMessages[0].timestamp)}\n\n`;

      // 顯示部分發言
      const displayCount = Math.min(6, actualCount);
      summary += `📋 最近的發言：\n`;

      userMessages.slice(0, displayCount).forEach((msg, index) => {
        const timeAgo = formatTimeAgo(msg.timestamp);
        const content = msg.messageContent.length > 40
          ? msg.messageContent.substring(0, 40) + '...'
          : msg.messageContent;
        summary += `${index + 1}. ${content} (${timeAgo})\n`;
      });

      if (actualCount > displayCount) {
        summary += `\n... 還有 ${actualCount - displayCount} 條發言`;
      }
    }

    return summary;

  } catch (error) {
    console.error('生成用戶摘要錯誤:', error);
    return `⚠️ 摘要生成失敗：${error.message}`;
  }
}

// 14. 🤖 AI 生成用戶分析（智能回答版）- 解決回答品質問題
function generateUserAnalysisWithAI(targetUser, userMessages, analysisPrompt) {
  try {
    if (userMessages.length === 0) {
      return `🤔 沒有找到 ${targetUser} 的發言記錄，無法進行分析`;
    }

    const config = getConfig();
    if (!config.geminiApiKey) {
      return `🤖 AI 分析功能需要設定 Gemini API Key\n\n📊 ${targetUser} 共有 ${userMessages.length} 條發言記錄`;
    }

    // 構建分析上下文
    const analysisMessages = userMessages.slice(0, 25); // 最多分析25條
    const messageContext = analysisMessages.map((msg, index) =>
      `${index + 1}. [${formatTimeAgo(msg.timestamp)}] ${msg.messageContent}`
    ).join('\n');

    // 🎯 智能回答策略：根據問題類型選擇合適的提示詞
    const responseStrategy = getResponseStrategy(analysisPrompt, 'analysis');

    try {
      let analysis;

      if (responseStrategy === 'SIMPLE_QUERY_RESPONSE') {
        // 簡潔查詢回答（如：「Michael 有提到吃的嗎？」）
        analysis = callAIWithPrompt('SIMPLE_QUERY_RESPONSE', {
          originalQuery: analysisPrompt,
          targetUser: targetUser,
          messageContext: messageContext
        });

        // 簡潔回答不需要額外格式
        return analysis;

      } else {
        // 深度分析回答
        analysis = callAIWithPrompt('USER_ANALYSIS', {
          analysisPrompt: analysisPrompt,
          targetUser: targetUser,
          messageContext: messageContext
        });

        // 深度分析保持原有格式
        let response = `👥 關於 ${targetUser}：\n\n`;
        response += `${analysis}\n\n`;
        response += `📊 參考了 ${analysisMessages.length} 條發言記錄`;

        return response;
      }

    } catch (aiError) {
      console.error('AI 分析失敗:', aiError);
      return `⚠️ AI 分析失敗：${aiError.message}\n\n📊 ${targetUser} 共有 ${userMessages.length} 條發言記錄`;
    }

  } catch (error) {
    console.error('生成用戶分析錯誤:', error);
    return `⚠️ 分析生成失敗：${error.message}`;
  }
}

// 15. ⏰ 時間格式化輔助函數
function formatTimeAgo(timestamp) {
  const now = new Date();
  const time = new Date(timestamp);
  const diffMs = now - time;
  
  const diffMins = Math.floor(diffMs / (1000 * 60));
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
  
  if (diffMins < 1) {
    return '剛剛';
  } else if (diffMins < 60) {
    return `${diffMins}分鐘前`;
  } else if (diffHours < 24) {
    return `${diffHours}小時前`;
  } else {
    return `${diffDays}天前`;
  }
}

// 16. 🔄 重置緩存
function resetUserMappingCache() {
  userMappingCache = {};
  uniqueUsersCache = [];
  cacheInitialized = false;
  console.log('🔄 用戶映射緩存已重置');
}

// === DEBUG 測試函數 ===

// 🧪 測試緩存系統
function testUserMappingCache_debug() {
  console.log('🧪 === 測試用戶映射緩存系統 ===');
  
  try {
    // 重置緩存
    resetUserMappingCache();
    
    // 測試初始化
    console.log('1. 測試緩存初始化...');
    const initResult = initializeUserMappingCache();
    console.log(`初始化結果: ${initResult ? '✅ 成功' : '❌ 失敗'}`);
    
    // 測試解析
    console.log('\n2. 測試用戶名稱解析...');
    const testQueries = ['Michael', 'mike', '蚵仔煎', '蚵大', 'andrew'];
    
    testQueries.forEach(query => {
      const resolved = resolveUserNameWithCache(query);
      console.log(`查詢 "${query}" → "${resolved}"`);
    });
    
    // 測試性能
    console.log('\n3. 測試查詢性能...');
    const startTime = Date.now();
    
    for (let i = 0; i < 10; i++) {
      resolveUserNameWithCache('Michael');
    }
    
    const endTime = Date.now();
    console.log(`10次查詢耗時: ${endTime - startTime}ms`);
    
    console.log('\n✅ 緩存系統測試完成');
    
  } catch (error) {
    console.error('❌ 緩存系統測試失敗:', error);
  }
}

// 🧪 測試 AI 數量理解
function testAIQueryParsing_debug() {
  console.log('🧪 === 測試 AI 查詢解析 ===');
  
  const testQueries = [
    '顯示Michael最近500筆對話',
    '蚵仔煎都聊了什麼？',
    '給我看Andrew所有發言',
    '你覺得蚵大講的有道理嗎？',
    '總結一下Michael最近100條訊息'
  ];
  
  testQueries.forEach((query, index) => {
    try {
      console.log(`\n${index + 1}. 測試查詢: "${query}"`);
      const result = parseQueryWithAI(query);
      
      console.log(`  目標用戶: ${result.targetUser}`);
      console.log(`  記錄限制: ${result.recordLimit}`);
      console.log(`  查詢類型: ${result.queryType}`);
      console.log(`  有效查詢: ${result.isValidQuery ? '✅' : '❌'}`);
      
    } catch (error) {
      console.log(`  ❌ 解析失敗: ${error.message}`);
    }
  });
  
  console.log('\n✅ AI 查詢解析測試完成');
}

// 🧪 測試完整查詢流程
function testCompleteQueryFlow_debug() {
  console.log('🧪 === 測試完整查詢流程 ===');
  
  try {
    // 確保有測試數據
    addTestGroupMessages_debug();
    
    // 測試查詢
    const testQuery = '顯示Michael最近5筆對話';
    console.log(`\n測試查詢: "${testQuery}"`);
    
    const startTime = Date.now();
    const response = handleSmartGroupQuery(testQuery, 'test-user', 'group');
    const endTime = Date.now();
    
    console.log(`\n🚀 查詢完成，耗時: ${endTime - startTime}ms`);
    console.log(`📋 回應長度: ${response.length} 字符`);
    console.log(`📄 回應預覽: ${response.substring(0, 100)}...`);
    
    console.log('\n✅ 完整查詢流程測試完成');
    
  } catch (error) {
    console.error('❌ 完整查詢流程測試失敗:', error);
  }
}

// 🧪 添加測試數據
function addTestGroupMessages_debug() {
  console.log('🧪 === 添加測試群組發言記錄 ===');
  
  try {
    const ss = SpreadsheetApp.getActiveSpreadsheet();
    let sheet = ss.getSheetByName('群組發言記錄');
    
    if (!sheet) {
      sheet = setupGroupChatSheetHeaders();
    }
    
    const testMessages = [
      ['⚡Michael（台灣智能）', '大家好！今天天氣不錯', 'text'],
      ['蚵仔煎/雲林AI第一人', '是啊，很適合出門', 'text'],
      ['⚡Michael（台灣智能）', '我剛看到一個有趣的新聞', 'text'],
      ['蚵仔煎/雲林AI第一人', '什麼新聞？分享一下', 'text'],
      ['⚡Michael（台灣智能）', 'AI技術又有新突破了', 'text'],
      ['蚵仔煎/雲林AI第一人', '真的嗎？好厲害', 'text'],
      ['Andrew/台中教大/AI博士生', '我也很感興趣這個話題', 'text'],
      ['⚡Michael（台灣智能）', '別說學不完這些工具，現在連知道都知道不完', 'text'],
      ['蚵仔煎/雲林AI第一人', '一言不合就給一大串@@', 'text'],
      ['Taoist-Shih', '什麼都會是AI的事，我只要會叫AI做事', 'text'],
      ['⚡Michael（台灣智能）', '我下午全聯大採購了幾天的食物', 'text'],
      ['Andrew/台中教大/AI博士生', '這我以後上課用的', 'text']
    ];
    
    console.log('📝 添加測試發言記錄...');
    
    testMessages.forEach((msgData, index) => {
      const [displayName, messageContent, messageType] = msgData;
      const timestamp = new Date(Date.now() - (testMessages.length - index) * 120000); // 每條記錄間隔2分鐘
      const groupId = 'test-group-123';
      const userId = `test-user-${index + 1}`;
      const responseStatus = '已記錄';
      
      sheet.appendRow([
        timestamp,
        groupId,
        userId,
        displayName,
        messageContent,
        messageType,
        responseStatus
      ]);
    });
    
    // 重置緩存以包含新數據
    resetUserMappingCache();
    
    console.log(`✅ 添加了 ${testMessages.length} 條測試記錄`);
    return '✅ 測試數據添加成功';
    
  } catch (error) {
    console.error('❌ 添加測試數據失敗:', error);
    return `❌ 失敗: ${error.message}`;
  }
}
