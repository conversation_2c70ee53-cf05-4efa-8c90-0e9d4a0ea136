// == Cloudinary 上傳失敗診斷 ==
// 🚨 診斷 Cloudinary 上傳失敗和 LINE 音頻按鈕消失問題

/**
 * 🚨 緊急診斷 Cloudinary 上傳失敗
 * 找出為什麼顯示 example.com 而不是真實的 Cloudinary URL
 */
function diagnoseCloudinaryUploadFailure() {
  console.log('🚨 === 診斷 Cloudinary 上傳失敗 ===');
  console.log('🎯 問題：顯示 example.com，音頻按鈕消失');
  console.log('');

  try {
    // 1. 詳細檢查 Cloudinary 配置
    console.log('📋 1. 詳細檢查 Cloudinary 配置...');
    const config = getConfig();
    
    console.log('🔧 當前配置詳情：');
    console.log(`   Cloud Name: "${config.cloudinaryCloudName}" (長度: ${config.cloudinaryCloudName?.length || 0})`);
    console.log(`   API Key: "${config.cloudinaryApiKey}" (長度: ${config.cloudinaryApiKey?.length || 0})`);
    console.log(`   API Secret: ${config.cloudinaryApiSecret ? `***${config.cloudinaryApiSecret.length}字符***` : '未設定'}`);
    
    // 檢查配置值是否有空格或特殊字符
    if (config.cloudinaryCloudName) {
      const trimmed = config.cloudinaryCloudName.trim();
      if (trimmed !== config.cloudinaryCloudName) {
        console.log('⚠️ Cloud Name 包含前後空格');
      }
      if (!/^[a-zA-Z0-9_-]+$/.test(trimmed)) {
        console.log('⚠️ Cloud Name 包含無效字符');
      }
    }

    // 2. 測試實際的 Cloudinary 上傳
    console.log('\n🧪 2. 測試實際 Cloudinary 上傳...');
    testRealCloudinaryUpload();

    // 3. 檢查錯誤日誌中的 Cloudinary 相關錯誤
    console.log('\n📝 3. 檢查 Cloudinary 錯誤日誌...');
    checkCloudinaryErrorLogs();

    // 4. 診斷 LINE 音頻訊息問題
    console.log('\n📱 4. 診斷 LINE 音頻訊息問題...');
    diagnoseLINEAudioMessage();

    console.log('\n📊 診斷完成！');
    return '診斷完成';

  } catch (error) {
    console.error('❌ 診斷過程錯誤:', error);
    return `診斷失敗: ${error.message}`;
  }
}

/**
 * 🧪 測試實際的 Cloudinary 上傳
 */
function testRealCloudinaryUpload() {
  try {
    const config = getConfig();
    
    // 創建測試音頻檔案
    console.log('   📁 創建測試音頻檔案...');
    const testAudioData = new Int16Array(1000);
    for (let i = 0; i < 1000; i++) {
      testAudioData[i] = Math.sin(i * 0.1) * 16383;
    }
    
    const audioParams = {
      sampleRate: 24000,
      bitsPerSample: 16,
      channels: 1
    };
    
    const testBlob = createWAVFromPCM(new Uint8Array(testAudioData.buffer), audioParams);
    console.log(`   ✅ 測試檔案已創建: ${testBlob.getBytes().length} bytes`);

    // 嘗試上傳到 Cloudinary
    console.log('   🌩️ 嘗試上傳到 Cloudinary...');
    
    const timestamp = Math.floor(Date.now() / 1000);
    const publicId = `test/debug_audio_${timestamp}`;
    
    console.log(`   📋 上傳參數檢查:`);
    console.log(`      Public ID: ${publicId}`);
    console.log(`      Timestamp: ${timestamp}`);
    console.log(`      Cloud Name: ${config.cloudinaryCloudName}`);
    console.log(`      API Key: ${config.cloudinaryApiKey}`);
    
    // 生成簽名
    const uploadParams = {
      public_id: publicId,
      resource_type: 'video',
      format: 'm4a',
      timestamp: timestamp
    };
    
    const signature = generateCloudinarySignature(uploadParams, config.cloudinaryApiSecret);
    console.log(`      簽名: ${signature.substring(0, 10)}...`);
    
    // 構建上傳 URL
    const uploadUrl = `https://api.cloudinary.com/v1_1/${config.cloudinaryCloudName}/video/upload`;
    console.log(`      上傳 URL: ${uploadUrl}`);
    
    // 準備表單數據
    const formData = {
      file: testBlob,
      public_id: publicId,
      resource_type: 'video',
      format: 'm4a',
      api_key: config.cloudinaryApiKey,
      timestamp: timestamp,
      signature: signature
    };
    
    console.log('   📡 發送上傳請求...');
    
    const response = UrlFetchApp.fetch(uploadUrl, {
      method: 'POST',
      payload: formData,
      muteHttpExceptions: true
    });
    
    const responseCode = response.getResponseCode();
    const responseText = response.getContentText();
    
    console.log(`   📊 回應代碼: ${responseCode}`);
    
    if (responseCode === 200) {
      const result = JSON.parse(responseText);
      console.log('   ✅ Cloudinary 上傳成功！');
      console.log(`      URL: ${result.secure_url}`);
      console.log(`      Public ID: ${result.public_id}`);
      console.log(`      格式: ${result.format}`);
      return result.secure_url;
    } else {
      console.log('   ❌ Cloudinary 上傳失敗！');
      console.log(`      錯誤代碼: ${responseCode}`);
      console.log(`      錯誤詳情: ${responseText}`);
      
      // 解析錯誤訊息
      try {
        const errorData = JSON.parse(responseText);
        if (errorData.error && errorData.error.message) {
          console.log(`      具體錯誤: ${errorData.error.message}`);
        }
      } catch (parseError) {
        console.log('      無法解析錯誤訊息');
      }
    }

  } catch (error) {
    console.log(`   ❌ Cloudinary 上傳測試失敗: ${error.message}`);
  }
}

/**
 * 📝 檢查 Cloudinary 相關錯誤日誌
 */
function checkCloudinaryErrorLogs() {
  try {
    const sheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName('活動日誌');
    
    if (!sheet) {
      console.log('   ⚠️ 找不到活動日誌工作表');
      return;
    }
    
    const data = sheet.getDataRange().getValues();
    const recentLogs = data.slice(-20); // 最近 20 條記錄
    
    console.log('   📋 Cloudinary 相關日誌:');
    let foundCloudinaryLogs = false;
    
    recentLogs.forEach((row, index) => {
      if (row[0] && row[1] && row[2]) {
        const timestamp = new Date(row[0]).toLocaleString();
        const action = row[1];
        const details = row[2];
        
        if (action.includes('Cloudinary') || details.includes('Cloudinary') || details.includes('cloudinary')) {
          console.log(`      [${timestamp}] ${action}: ${details}`);
          foundCloudinaryLogs = true;
        }
      }
    });
    
    if (!foundCloudinaryLogs) {
      console.log('   ℹ️ 未找到 Cloudinary 相關日誌');
    }
    
  } catch (error) {
    console.log(`   ⚠️ 無法檢查錯誤日誌: ${error.message}`);
  }
}

/**
 * 📱 診斷 LINE 音頻訊息問題
 */
function diagnoseLINEAudioMessage() {
  try {
    console.log('   🔍 檢查 LINE 音頻訊息格式...');
    
    // 模擬音頻回覆測試
    const mockAudioResult = {
      success: true,
      audioUrl: 'https://example.com/test.m4a',
      cloudinaryUrl: null,  // 模擬 Cloudinary 失敗
      driveUrl: 'https://drive.google.com/file/d/test/view',
      fileId: 'test-file-id',
      fileName: 'test_audio.wav',
      mimeType: 'audio/wav',
      text: '測試文本',
      isPlayable: false,  // Cloudinary 失敗所以不可播放
      cloudinaryError: 'Cloudinary 上傳失敗'
    };
    
    console.log('   📝 模擬音頻回覆結果:');
    console.log(`      可播放: ${mockAudioResult.isPlayable}`);
    console.log(`      Cloudinary URL: ${mockAudioResult.cloudinaryUrl || '無'}`);
    console.log(`      Drive URL: ${mockAudioResult.driveUrl}`);
    
    // 檢查回覆文字生成
    const responseText = generateAudioResponseTextWithCloudinary('測試文本', mockAudioResult);
    console.log(`   📄 生成的回覆文字長度: ${responseText.length}`);
    console.log(`   📄 回覆文字預覽: ${responseText.substring(0, 100)}...`);
    
    // 檢查是否會嘗試發送音頻訊息
    if (mockAudioResult.isPlayable && mockAudioResult.cloudinaryUrl) {
      console.log('   ✅ 應該發送 LINE 音頻訊息');
    } else {
      console.log('   ⚠️ 只會發送文字訊息（因為 Cloudinary 失敗）');
    }
    
  } catch (error) {
    console.log(`   ❌ LINE 音頻訊息診斷失敗: ${error.message}`);
  }
}

/**
 * 🔧 修復 Cloudinary 上傳問題
 */
function fixCloudinaryUploadIssue() {
  console.log('🔧 === 修復 Cloudinary 上傳問題 ===');
  
  try {
    // 1. 清理配置值
    console.log('1️⃣ 清理 Cloudinary 配置值...');
    const config = getConfig();
    
    const cleanConfig = {
      cloudinaryCloudName: config.cloudinaryCloudName?.trim(),
      cloudinaryApiKey: config.cloudinaryApiKey?.trim(),
      cloudinaryApiSecret: config.cloudinaryApiSecret?.trim()
    };
    
    console.log('   清理後的配置:');
    console.log(`   Cloud Name: "${cleanConfig.cloudinaryCloudName}"`);
    console.log(`   API Key: "${cleanConfig.cloudinaryApiKey}"`);
    console.log(`   API Secret: ${cleanConfig.cloudinaryApiSecret ? '***已設定***' : '未設定'}`);
    
    // 2. 驗證配置格式
    console.log('\n2️⃣ 驗證配置格式...');
    
    const validations = {
      cloudName: /^[a-zA-Z0-9_-]+$/.test(cleanConfig.cloudinaryCloudName || ''),
      apiKey: /^\d+$/.test(cleanConfig.cloudinaryApiKey || ''),
      apiSecret: cleanConfig.cloudinaryApiSecret && cleanConfig.cloudinaryApiSecret.length > 0
    };
    
    console.log(`   Cloud Name 格式: ${validations.cloudName ? '✅ 有效' : '❌ 無效'}`);
    console.log(`   API Key 格式: ${validations.apiKey ? '✅ 有效' : '❌ 無效'}`);
    console.log(`   API Secret: ${validations.apiSecret ? '✅ 已設定' : '❌ 未設定'}`);
    
    // 3. 測試修復的上傳
    if (validations.cloudName && validations.apiKey && validations.apiSecret) {
      console.log('\n3️⃣ 測試修復的上傳...');
      testRealCloudinaryUpload();
    } else {
      console.log('\n❌ 配置格式錯誤，無法測試上傳');
      console.log('💡 請檢查 APIKEY 工作表中的 B8、B9、B10 設定');
    }
    
  } catch (error) {
    console.error('❌ 修復過程錯誤:', error);
  }
}

/**
 * 🚀 一鍵修復語音播放問題
 */
function quickFixVoicePlayback() {
  console.log('🚀 === 一鍵修復語音播放問題 ===');
  
  try {
    // 1. 診斷問題
    console.log('1️⃣ 診斷當前問題...');
    diagnoseCloudinaryUploadFailure();
    
    // 2. 測試修復
    console.log('\n2️⃣ 嘗試修復...');
    fixCloudinaryUploadIssue();
    
    // 3. 驗證修復結果
    console.log('\n3️⃣ 驗證修復結果...');
    const result = textToSpeechWithGemini('修復測試');
    
    if (result.success) {
      console.log('✅ TTS 生成成功');
      if (result.isPlayable) {
        console.log('🎉 完全修復！音頻可在 LINE 中播放');
        console.log(`   Cloudinary URL: ${result.cloudinaryUrl}`);
      } else {
        console.log('⚠️ 部分修復，WAV 檔案可下載但需要修復 Cloudinary');
        console.log(`   錯誤: ${result.cloudinaryError}`);
      }
    } else {
      console.log('❌ TTS 生成失敗');
      console.log(`   錯誤: ${result.error}`);
    }
    
    console.log('\n📊 修復狀態總結:');
    console.log(`   WAV 檔案: ✅ 已修復`);
    console.log(`   Cloudinary: ${result.isPlayable ? '✅ 正常' : '❌ 需要修復'}`);
    console.log(`   LINE 播放: ${result.isPlayable ? '✅ 可播放' : '⚠️ 需要下載'}`);
    
    return result;
    
  } catch (error) {
    console.error('❌ 一鍵修復失敗:', error);
    return { success: false, error: error.message };
  }
}

/**
 * 📋 顯示 Cloudinary 設定指南
 */
function showCloudinarySetupGuide() {
  console.log('📋 === Cloudinary 設定指南 ===');
  console.log('');
  
  const config = getConfig();
  
  console.log('🔧 當前設定檢查:');
  console.log(`   B8 (Cloud Name): "${config.cloudinaryCloudName}"`);
  console.log(`   B9 (API Key): "${config.cloudinaryApiKey}"`);
  console.log(`   B10 (API Secret): ${config.cloudinaryApiSecret ? '***已設定***' : '未設定'}`);
  console.log('');
  
  console.log('💡 常見問題和解決方案:');
  console.log('');
  console.log('1. ❌ Cloud Name 錯誤');
  console.log('   檢查: 是否包含空格或特殊字符');
  console.log('   正確格式: 只能包含字母、數字、連字號和底線');
  console.log('   範例: "my-cloud-name" 或 "mycloud123"');
  console.log('');
  
  console.log('2. ❌ API Key 錯誤');
  console.log('   檢查: 是否為純數字');
  console.log('   正確格式: 15位數字');
  console.log('   範例: "123456789012345"');
  console.log('');
  
  console.log('3. ❌ API Secret 錯誤');
  console.log('   檢查: 是否包含完整的密鑰');
  console.log('   正確格式: 字母數字組合');
  console.log('   範例: "abcd1234efgh5678ijkl9012"');
  console.log('');
  
  console.log('🔧 建議執行的修復步驟:');
  console.log('   1. quickFixVoicePlayback() - 一鍵修復');
  console.log('   2. testRealCloudinaryUpload() - 測試上傳');
  console.log('   3. fixCloudinaryUploadIssue() - 修復配置');
}
