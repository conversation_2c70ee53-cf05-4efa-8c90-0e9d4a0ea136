// == AI 專門處理函數模組 ==
// 🤖 專門處理各種 AI 功能的具體實現
// 🔧 v1.3.2 - 修復函數調用：統一使用 Utils.gs 中的正確實現
// 📁 本檔案職責：AI具體處理函數、回應生成函數、內容提取函數

// ===== 🤖 AI 驅動的具體處理函數 =====

/**
 * 🤖 AI 驅動的筆記處理（取代硬編碼命令）
 */
function handleAINoteRequest(intent, originalMessage, userId, sourceType) {
  try {
    // 🧠 讓 AI 提取要記錄的內容
    const noteContent = extractNoteContentWithAI(intent, originalMessage);
    
    if (!noteContent || noteContent.trim() === '') {
      return generateAIResponse(intent, '我理解您想要記錄一些東西。請告訴我您想記錄什麼內容？');
    }
    
    // 保存筆記
    saveTextNote(userId, noteContent, sourceType);
    
    // 🤖 AI 生成個性化確認回應
    return generateNoteConfirmationWithAI(noteContent, intent);
    
  } catch (error) {
    console.error('AI 筆記處理錯誤:', error);
    return '我理解您想要記錄內容，但處理時遇到了問題。請稍後再試？';
  }
}

/**
 * 🤖 AI 驅動的檔案查詢處理
 */
function handleAIFileQuery(intent, originalMessage, userId, sourceType) {
  try {
    // 使用現有的檔案引用檢測系統
    return handleFileReference(userId, originalMessage, sourceType);
  } catch (error) {
    console.error('AI 檔案查詢處理失敗:', error);
    return '我理解您想查詢檔案，但目前無法處理您的請求。請稍後再試？';
  }
}

/**
 * 🤖 AI 驅動的對話回顧處理
 * 🧠 v1.4.0 - 整合知識庫搜索功能
 */
function handleAIConversationReview(intent, userId, sourceType) {
  try {
    console.log(`🧠 處理對話回顧請求: ${intent.original_message}`);

    // 1. 先獲取近期對話歷史
    const recentHistory = getRecentConversationHistory(userId, 50);
    console.log(`📊 找到 ${recentHistory.length} 筆近期對話`);

    // 2. 檢查是否需要搜索知識庫
    const shouldSearchKB = shouldSearchKnowledgeBase(recentHistory, intent.original_message);

    let knowledgeResults = [];
    if (shouldSearchKB) {
      console.log('🔍 觸發知識庫搜索...');

      // 從用戶查詢中提取搜索關鍵字
      const searchQuery = extractSearchKeywords(intent.original_message);
      const targetSpeaker = extractTargetSpeaker(intent.original_message);

      knowledgeResults = searchKnowledgeBase(searchQuery, targetSpeaker, 10);
      console.log(`📚 知識庫搜索結果: ${knowledgeResults.length} 筆`);
    }

    // 3. 合併和格式化結果
    return formatConversationResults(recentHistory, knowledgeResults, intent.original_message);

  } catch (error) {
    console.error('AI 對話回顧處理失敗:', error);
    // 降級到原有系統
    return handleConversationReview(userId, intent.original_message, sourceType);
  }
}

/**
 * 🤖 AI 驅動的群組成員查詢處理
 */
function handleAIGroupMemberQuery(intent, originalMessage, userId, sourceType) {
  try {
    console.log(`🔍 AI群組成員查詢 - 原始訊息: ${originalMessage}`);
    console.log(`🔍 AI群組成員查詢 - 意圖詳情:`, intent);
    
    // 🧠 使用 AI 從意圖中提取目標用戶名稱
    let targetUser = '';
    
    // 先嘗試從 key_entities 中提取
    if (intent.key_entities && intent.key_entities.length > 0) {
      targetUser = intent.key_entities[0];
      console.log(`📝 從 key_entities 提取目標用戶: ${targetUser}`);
    }
    
    // 如果沒有提取到，使用 AI 進一步分析
    if (!targetUser) {
      try {
        const extractPrompt = `從以下用戶查詢中提取目標用戶的名稱：

用戶查詢：「${originalMessage}」

請只回傳用戶名稱，不要包含其他文字。
如果無法確定用戶名稱，請回傳空字符串。

目標用戶名稱：`;

        targetUser = callGemini(extractPrompt, 'general').trim();
        console.log(`🤖 AI提取目標用戶: ${targetUser}`);
      } catch (extractError) {
        console.error('AI提取用戶名稱失敗:', extractError);
      }
    }
    
    // 如果還是沒有提取到用戶名稱，使用簡單的正則匹配作為備用
    if (!targetUser) {
      const userNameMatches = originalMessage.match(/把群[裡裏](.+?)的對話/);
      if (userNameMatches) {
        targetUser = userNameMatches[1].trim();
        console.log(`📝 正則匹配提取用戶: ${targetUser}`);
      }
    }
    
    if (!targetUser) {
      return `🤔 我理解您想查詢群組成員的發言，但無法確定您想查詢哪位成員。

💡 請明確指出用戶名稱，例如：
• "把群裡張三的對話做個總結"
• "蚵仔煎都講了什麼"`;
    }
    
    console.log(`🎯 開始查詢用戶「${targetUser}」的群組發言記錄`);
    
    // 🔍 搜尋該用戶的群組發言記錄
    const userMessages = searchUserMessages(targetUser, null, '30days');
    
    if (userMessages.length === 0) {
      return `🤔 沒有找到「${targetUser}」的發言記錄

💡 可能原因：
• 用戶名稱不完全匹配
• 該用戶最近沒有發言
• 群組記錄系統剛啟用

🔍 您可以嘗試其他用戶名稱或昵稱`;
    }
    
    console.log(`✅ 找到 ${userMessages.length} 條「${targetUser}」的發言記錄`);
    
    // 🤖 使用 AI 生成對話總結
    return generateAIGroupMemberSummary(targetUser, userMessages, originalMessage);
    
  } catch (error) {
    console.error('AI 群組查詢處理失敗:', error);
    return `⚠️ 查詢群組成員發言時發生錯誤：${error.message}

💡 您可以稍後再試，或者換個方式詢問`;
  }
}

/**
 * 🤖 AI 驅動的社交媒體發布處理
 */
function handleAISocialMediaPost(intent, originalMessage, userId, sourceType) {
  try {
    // 提取發文內容
    const postContent = extractPostContentWithAI(intent, originalMessage);
    
    if (!postContent || postContent.trim() === '') {
      return generateAIResponse(intent, '我理解您想發布內容。請告訴我您想分享什麼？');
    }
    
    logActivity('AI Threads發文', `用戶${userId}(${sourceType}): ${postContent}`);
    return `✅ 我收到您想要發布的內容：

📝 ${postContent}

🔧 Threads 發文功能開發中，敬請期待！`;
    
  } catch (error) {
    console.error('AI 社交媒體發布處理失敗:', error);
    return '我理解您想發布內容，但處理時遇到了問題。請稍後再試？';
  }
}

/**
 * 🤖 AI 驅動的一般問題處理
 */
function handleAIGeneralQuestion(intent, originalMessage, sourceType) {
  try {
    // 使用現有的智能搜尋系統（AI_Features.gs）
    return callGeminiWithSmartSearch(originalMessage);
    
  } catch (error) {
    console.error('AI 一般問題處理失敗:', error);
    return generateAIErrorResponse(intent, originalMessage, error);
  }
}

/**
 * 🔊 AI 驅動的 TTS 處理
 * 返回特殊物件以觸發音頻回覆
 */
function handleAITTSRequest(intent, originalMessage, userId, sourceType) {
  try {
    console.log(`🔊 處理 TTS 請求: ${originalMessage}`);

    // 提取要轉換的文本
    const textToConvert = extractTextForTTS(originalMessage);

    if (!textToConvert || textToConvert.trim() === '') {
      return '🤔 我理解您想要語音轉換，但沒有找到要轉換的文字。請告訴我要轉換什麼內容？';
    }

    // 🔧 修復：調用 TTS 功能（使用正確的函數名稱）
    const ttsResult = textToSpeechWithGemini(textToConvert);

    if (ttsResult.success) {
      logActivity('TTS轉換', `用戶${userId}(${sourceType}): ${textToConvert.substring(0, 50)}...`);

      // 🎯 返回特殊物件以觸發 AI-First 音頻回覆
      return {
        type: 'audio_response',
        audioResult: ttsResult,
        originalText: textToConvert
      };
    } else {
      return `❌ 語音轉換失敗：${ttsResult.error}

💡 請稍後再試，或者換個方式表達`;
    }

  } catch (error) {
    console.error('AI TTS 處理失敗:', error);
    return '我理解您想要語音轉換，但處理時遇到了問題。請稍後再試？';
  }
}

/**
 * 🎙️ AI 驅動的對話音頻處理
 * 使用 Native Audio Dialog 模型
 * 🔧 v1.3.2 修復：統一使用 Utils.gs 中的正確實現
 */
function handleAIConversationalAudio(intent, originalMessage, userId, sourceType) {
  try {
    console.log(`🎙️ 處理對話音頻請求: ${originalMessage}`);

    // 🚀 修復：直接調用 Utils.gs 中的 Native Audio Dialog 模型
    const conversationContext = {
      userId: userId,
      sourceType: sourceType,
      conversationHistory: [] // 可以添加對話歷史
    };
    
    const audioDialogResult = callGeminiAudioDialog(originalMessage, conversationContext);

    if (audioDialogResult.success) {
      logActivity('Native Audio Dialog', `用戶${userId}(${sourceType}): ${originalMessage.substring(0, 50)}...`);

      // 🎯 返回特殊物件以觸發 AI-First 音頻回覆
      return {
        type: 'audio_response',
        audioResult: audioDialogResult,
        originalText: originalMessage,
        mode: 'native_audio_dialog'
      };
    } else {
      console.log(`🔄 Native Audio Dialog 失敗，使用備用方案: ${audioDialogResult.error}`);

      // 🔄 備用：使用傳統對話 + TTS 組合
      const textResponse = callGemini(originalMessage, 'general');
      
      // 🔧 修復：使用正確的 TTS 函數名稱
      const ttsResult = textToSpeechWithGemini(textResponse);

      if (ttsResult.success) {
        logActivity('對話音頻備用', `用戶${userId}(${sourceType}): ${originalMessage.substring(0, 30)}... → ${textResponse.substring(0, 30)}...`);

        return {
          type: 'audio_response',
          audioResult: ttsResult,
          originalText: textResponse,
          mode: 'conversational_fallback',
          userQuery: originalMessage
        };
      } else {
        return `🎙️ 我想用語音回應您，但語音功能暫時有問題。

📝 文字回應：${textResponse}`;
      }
    }

  } catch (error) {
    console.error('AI 對話音頻處理失敗:', error);
    return '我理解您想要語音對話，但處理時遇到了問題。請稍後再試？';
  }
}

/**
 * 🎨 AI 驅動的圖像生成處理
 * 返回特殊物件以觸發圖片回覆
 */
function handleAIImageGeneration(intent, originalMessage, userId, sourceType) {
  try {
    console.log(`🎨 處理圖像生成請求: ${originalMessage}`);

    // 提取圖像描述
    const imagePrompt = extractImagePrompt(originalMessage);

    if (!imagePrompt || imagePrompt.trim() === '') {
      return '🤔 我理解您想要生成圖片，但沒有找到描述。請告訴我要畫什麼？';
    }

    // 調用圖像生成功能
    const imageResult = generateImageWithGemini(imagePrompt);

    if (imageResult.success) {
      logActivity('圖像生成', `用戶${userId}(${sourceType}): ${imagePrompt.substring(0, 50)}...`);

      // 🎯 返回特殊物件以觸發 AI-First 圖片回覆
      return {
        type: 'image_response',
        imageResult: imageResult,
        originalPrompt: imagePrompt
      };
    } else {
      return `❌ 圖像生成失敗：${imageResult.error}

💡 請稍後再試，或者換個描述方式`;
    }

  } catch (error) {
    console.error('AI 圖像生成處理失敗:', error);
    return '我理解您想要生成圖片，但處理時遇到了問題。請稍後再試？';
  }
}

/**
 * 🔧 處理系統指令
 * 智能識別並執行系統相關指令
 */
function handleAISystemCommand(intent, originalMessage, sourceType) {
  try {
    console.log(`🔧 處理系統指令: ${originalMessage}`);

    const lowerMessage = originalMessage.toLowerCase();

    // 測試指令
    if (lowerMessage.includes('測試') || lowerMessage.includes('test')) {
      return generateTestResponse(sourceType);
    }

    // 幫助指令
    if (lowerMessage.includes('help') || lowerMessage.includes('幫助') || lowerMessage.includes('功能')) {
      return generateEnhancedHelpMessage(sourceType);
    }

    // 狀態檢查
    if (lowerMessage.includes('狀態') || lowerMessage.includes('status')) {
      return generateSystemStatusResponse();
    }

    // 範例指令
    if (lowerMessage.includes('範例') || lowerMessage.includes('example')) {
      return generateModelExamplesResponse(intent, sourceType);
    }

    // 預設回應
    return generateEnhancedHelpMessage(sourceType);

  } catch (error) {
    console.error('系統指令處理失敗:', error);
    return '🔧 系統指令處理時遇到問題，請稍後再試。';
  }
}

/**
 * 🤖 處理不確定意圖的智能回應
 */
function handleAIUncertainIntent(intent, originalMessage, sourceType) {
  try {
    const uncertainPrompt = `用戶發送了一條訊息，但我無法確定具體意圖。請提供一個友善的回應。

用戶訊息：「${originalMessage}」
環境：${sourceType === 'group' ? '群組' : '個人對話'}
我的分析：${intent.natural_language_summary}

請用繁體中文回應，語氣要友善，並：
1. 承認收到了訊息
2. 簡要說明我可能的理解
3. 詢問用戶的具體需求或提供建議
4. 保持對話的自然流暢

回應：`;

    const aiResponse = callGemini(uncertainPrompt, 'general');
    return aiResponse;
    
  } catch (error) {
    console.error('AI 不確定意圖處理失敗:', error);
    return `我聽到您說：「${originalMessage}」

🤔 我想幫助您，但不太確定您具體需要什麼。您可以告訴我更多詳情嗎？

💡 例如：想記錄什麼、查詢什麼、或者有什麼問題要問我？`;
  }
}

// ===== 📚 回應生成函數 =====

/**
 * 🧠 使用 AI 提取筆記內容 - 統一提示詞版
 */
function extractNoteContentWithAI(intent, message) {
  try {
    // 如果 AI 已經在意圖分析中識別了關鍵實體，直接使用
    if (intent.key_entities && intent.key_entities.length > 0) {
      return intent.key_entities.join(' ');
    }

    // 🎯 使用統一提示詞系統
    const extractedContent = callAIWithPrompt('NOTE_EXTRACTION', {
      message: message
    });

    return extractedContent.trim();

  } catch (error) {
    console.error('AI 內容提取失敗:', error);
    // 簡單的備用提取邏輯
    return message.replace(/^(記錄|記下|記住|筆記|note|幫我記|記一下)\s*/i, '').trim();
  }
}

/**
 * 🤖 AI 生成個性化筆記確認 - 統一提示詞版
 */
function generateNoteConfirmationWithAI(noteContent, intent) {
  try {
    // 🎯 使用統一提示詞系統
    const aiConfirmation = callAIWithPrompt('NOTE_CONFIRMATION', {
      noteContent: noteContent
    });

    return `✅ ${aiConfirmation}

📝 已儲存到筆記系統`;

  } catch (error) {
    console.error('AI 確認回應生成失敗:', error);
    return `✅ 好的！我已經幫您記錄了：

📝 ${noteContent}

筆記已安全保存！`;
  }
}

/**
 * 🤖 AI 生成群組成員發言總結
 */
function generateAIGroupMemberSummary(targetUser, userMessages, originalQuery) {
  try {
    const config = getConfig();
    
    if (!config.geminiApiKey) {
      // 沒有 AI 時使用簡單的發言列表
      return generateSimpleGroupMemberSummary(targetUser, userMessages);
    }
    
    // 🧠 準備AI分析的上下文（最近20條發言）
    const recentMessages = userMessages.slice(0, 20);
    const messageContext = recentMessages.map((msg, index) => 
      `${index + 1}. [${formatTimeAgo(msg.timestamp)}] ${msg.messageContent}`
    ).join('\n');
    
    const summaryPrompt = `請總結群組成員「${targetUser}」的發言內容：

用戶查詢：「${originalQuery}」

最近發言記錄：
${messageContext}

請用繁體中文提供：
1. 發言主題概述（2-3個主要話題）
2. 重要觀點摘要（3-5個要點）
3. 發言特色和風格
4. 發言統計：共 ${userMessages.length} 條記錄，時間範圍從 ${formatTimeAgo(userMessages[userMessages.length - 1].timestamp)} 到 ${formatTimeAgo(userMessages[0].timestamp)}

請保持客觀中性的語調，控制在400字以內。

總結：`;

    try {
      const aiSummary = callGemini(summaryPrompt, 'general');
      const response = `👥 「${targetUser}」的群組發言總結

${aiSummary}`;
      return response;
      
    } catch (aiError) {
      console.error('AI總結生成失敗:', aiError);
      return generateSimpleGroupMemberSummary(targetUser, userMessages);
    }
    
  } catch (error) {
    console.error('生成群組成員總結錯誤:', error);
    return `⚠️ 總結生成失敗：${error.message}`;
  }
}

/**
 * 📝 簡單的群組成員發言總結（無AI時的備用方案）
 */
function generateSimpleGroupMemberSummary(targetUser, userMessages) {
  try {
    let summary = `👥 「${targetUser}」的群組發言記錄

`;
    summary += `📊 發言統計：共 ${userMessages.length} 條發言
`;
    summary += `⏰ 時間範圍：${formatTimeAgo(userMessages[userMessages.length - 1].timestamp)} 到 ${formatTimeAgo(userMessages[0].timestamp)}

`;
    
    // 顯示最近8條發言
    summary += `📋 最近發言：
`;
    const displayCount = Math.min(8, userMessages.length);
    
    userMessages.slice(0, displayCount).forEach((msg, index) => {
      const timeAgo = formatTimeAgo(msg.timestamp);
      const content = msg.messageContent.length > 40 
        ? msg.messageContent.substring(0, 40) + '...' 
        : msg.messageContent;
      summary += `${index + 1}. ${content} (${timeAgo})
`;
    });
    
    if (userMessages.length > displayCount) {
      summary += `
... 還有 ${userMessages.length - displayCount} 條發言
`;
    }
    
    summary += `

💡 設定 Gemini API Key 可啟用 AI 智能分析總結`;
    
    return summary;
    
  } catch (error) {
    console.error('生成簡單總結錯誤:', error);
    return `⚠️ 總結生成失敗：${error.message}`;
  }
}

/**
 * 🧠 使用 AI 提取發文內容
 */
function extractPostContentWithAI(intent, message) {
  try {
    const extractPrompt = `從以下用戶訊息中提取想要發布到社交媒體的內容：

用戶訊息：「${message}」

請只回傳要發布的核心內容，不要包含動作詞（如「發布」、「分享」等）。
如果無法確定具體內容，請回傳空字符串。

要發布的內容：`;

    const extractedContent = callGemini(extractPrompt, 'general');
    return extractedContent.trim();
    
  } catch (error) {
    console.error('AI 發文內容提取失敗:', error);
    // 簡單的備用提取邏輯
    return message.replace(/^(發|po|分享|threads|t)\s*/i, '').trim();
  }
}

/**
 * 🤖 AI 生成的幫助回應 - 統一提示詞版
 */
function generateAIHelpResponse(intent, sourceType) {
  try {
    const config = getConfig();
    const isGroup = sourceType === 'group' || sourceType === 'room';

    // 🎯 使用統一提示詞系統
    const aiHelp = callAIWithPrompt('HELP_RESPONSE', {
      environmentType: isGroup ? '群組對話' : '個人對話',
      modelInfo: `${config.generalModel}, ${config.visionModel}`,
      groupFeatures: isGroup ? '、群組查詢' : ''
    });

    return aiHelp + `

🔧 **v1.3.2 修復版**：統一函數調用，系統更穩定`;

  } catch (error) {
    console.error('AI 幫助生成失敗:', error);
    return generateEnhancedHelpMessage(sourceType);
  }
}

/**
 * 🤖 AI 生成的閒聊回應 - 統一提示詞版
 */
function generateAICasualResponse(intent, originalMessage) {
  try {
    // 🎯 使用統一提示詞系統
    const aiResponse = callAIWithPrompt('CASUAL_CHAT', {
      originalMessage: originalMessage
    });

    return aiResponse;

  } catch (error) {
    console.error('AI 閒聊回應生成失敗:', error);
    // 簡單的備用回應
    if (originalMessage.includes('你好') || originalMessage.includes('哈囉')) {
      return '你好！😊 有什麼可以幫助您的嗎？';
    } else if (originalMessage.includes('謝謝')) {
      return '不客氣！隨時為您服務！😊';
    } else {
      return '😊 我在這裡！有什麼需要幫忙的嗎？';
    }
  }
}

/**
 * 🤖 通用 AI 回應生成器
 */
function generateAIResponse(intent, fallbackMessage) {
  try {
    const responsePrompt = `基於用戶意圖生成個性化回應：

用戶意圖：${intent.natural_language_summary}
原始訊息：${intent.original_message}

請用繁體中文生成一個友善、有幫助的回應。語氣要自然，不要太正式。

回應：`;

    const aiResponse = callGemini(responsePrompt, 'general');
    return aiResponse || fallbackMessage;
    
  } catch (error) {
    console.error('AI 回應生成失敗:', error);
    return fallbackMessage;
  }
}

/**
 * 🤖 AI 錯誤回應生成器
 */
function generateAIErrorResponse(intent, originalMessage, error) {
  return `🤖 抱歉，我在處理您的請求時遇到了一些問題。

您說的是：「${originalMessage}」

💡 請您可以：
• 換個方式表達
• 或者直接告訴我您的具體需求

我會努力理解並幫助您！`;
}

/**
 * 📚 生成模型使用範例回應
 */
function generateModelExamplesResponse(intent, sourceType) {
  try {
    const isGroup = sourceType === 'group' || sourceType === 'room';
    const prefix = isGroup ? '!' : '';

    return `🤖 AI 功能使用範例指南 v1.3.2

🔊 **語音功能範例**
• 鸚鵡模式：「你說：今天天氣很好」（使用 TTS 模型）
• 對話模式：「語音聊天 今天天氣如何？」、「跟我語音聊聊天」（使用 Native Audio Dialog 模型）
• 念出文字：「念出來：我愛台灣」（使用 TTS 模型）
• 語音對話：「語音對話 你好嗎？」（使用 Native Audio Dialog 模型）

💡 **模型說明**：
• TTS 模型：從試算表 B16 讀取（可自定義）
• Native Audio Dialog：從試算表 B17 讀取（支援 thinking 模型）

🎨 **圖片生成範例**
• 「畫一隻可愛的小貓」
• 「生成圖片：夕陽下的海邊風景」
• 「創作一張科幻風格的城市」
• 「給我一張卡通風格的機器人圖片」

📊 **語義搜索範例**
• 「找相關的 AI 教育檔案」
• 「搜索與機器學習相關的內容」
• 「有沒有類似的研究報告」

⚡ **快速處理範例**
• 「快速整理這100個檔案」
• 「批量處理這些郵件」
• 「幫我快速分類這些任務」

📝 **筆記功能範例**
• 「${prefix}記錄 明天要開會」
• 「幫我記住買牛奶」
• 「記下這個重要資訊」

🧠 **對話記憶範例**
• 「我們聊了什麼？」
• 「總結一下剛才的對話」
• 「Michael 都說了什麼？」

🛡️ **系統指令範例**
• 「${prefix}測試」- 系統狀態檢查 (預檢測捕獲，100%準確)
• 「${prefix}help」- 完整功能說明 (預檢測捕獲，100%準確)
• 「${prefix}範例」- 功能使用範例 (預檢測捕獲，100%準確)

🔧 **模型切換提示** 🆕
• 修改試算表 B17 可切換對話音頻模型
• 設定 "gemini-2.5-flash-exp-native-audio-thinking-dialog" 啟用思考模式
• 所有語音模型都從試算表讀取，支援用戶自定義

💡 **提示**：直接用自然語言說話，AI 會理解您的意圖！v1.3.2 修復版，函數調用更穩定！`;

  } catch (error) {
    console.error('生成模型範例失敗:', error);
    return '📚 模型範例載入失敗，請稍後再試。';
  }
}

/**
 * 🔧 生成增強版幫助訊息
 * 結合傳統幫助和 AI-First 體驗
 */
function generateEnhancedHelpMessage(sourceType) {
  try {
    const isGroup = sourceType === 'group' || sourceType === 'room';
    const prefix = isGroup ? '!' : '';

    return `🤖 AI-First 智能助理使用指南 v1.3.2

✨ **AI-First 體驗**
直接用自然語言告訴我您的需求！
• 想記錄：「幫我記住...」
• 想查詢：直接提問
• 檔案分析：直接上傳檔案
• 語音轉換：「你說：...」或「跟我聊聊」
• 圖片生成：「畫一隻...」或「生成圖片：...」

🛡️ **雙重檢測機制** 
• 系統指令現在 100% 準確識別
• 包含預檢測 + AI 分析雙重保護
• 「${prefix}測試」、「${prefix}help」、「${prefix}範例」絕不誤判

🤔 **智能引導功能**
• AI 信心度過低時自動提供引導
• 幫助您更好地使用各種功能
• 減少困惑，提升體驗

📝 **筆記功能**
• ${prefix}記錄 內容 - 儲存筆記
• 「幫我記住明天要開會」- AI 智能記錄

🔊 **語音功能** 🆕
• 鸚鵡模式：「你說：今天天氣很好」
• 對話模式：「跟我聊聊天」
• 模型切換：修改試算表 B17（支援 thinking 模型）

🎨 **圖片生成**
• 「畫一隻可愛的小貓」
• 「生成圖片：夕陽下的海邊」

📄 **檔案處理**
• 上傳圖片/檔案 - AI 智慧分析
• 分享 Google Drive 連結 - 讀取檔案

🧠 **記憶功能**
• 「我們聊了什麼？」- 對話回顧
• 「剛才的圖片如何？」- 檔案引用
• 「Michael 都說了什麼？」- 群組成員查詢

🔧 **系統指令**
• ${prefix}測試 - 系統狀態檢查
• ${prefix}範例 - 查看使用範例
• ${prefix}help - 完整功能說明

💡 **智能提示**：我會用 AI 理解您的意圖，不需要記住複雜的指令格式！v1.3.2 修復版，函數調用更穩定！

🔧 **重要修復** 🆕
• 統一使用 Utils.gs 中的正確函數
• 支援試算表模型自定義切換
• 修復重複函數調用問題`;

  } catch (error) {
    console.error('生成增強版幫助失敗:', error);
    return generateHelpMessage(sourceType); // 回退到原始幫助
  }
}

/**
 * 🔧 生成系統狀態回應
 */
function generateSystemStatusResponse() {
  try {
    const config = getConfig();
    const versionInfo = getVersionInfo();

    return `🔧 系統狀態報告 v1.3.2

✅ **核心系統**
• LINE Bot：運作正常
• 版本：${versionInfo.version}
• 架構：AI-First 智能處理

🛡️ **修復狀態** 🆕
• 函數調用統一：✅ 已完成 (v1.3.2)
• 試算表模型讀取：✅ 正常運作
• 重複函數清理：✅ 已完成
• 模型切換支援：✅ 已啟用

🧠 **AI 功能**
• Gemini API：${config.geminiApiKey ? '✅ 已啟用' : '❌ 未設定'}
• 意圖識別：✅ 運作中 (v1.3.2 穩定版)
• 智能路由：✅ 運作中 (含引導機制)

🔍 **搜索功能**
• Google 搜尋：${config.googleSearchApiKey ? '✅ 已啟用' : '❌ 未設定'}
• 語義搜索：✅ 可用

🔊 **媒體功能** 🆕
• TTS 語音：✅ 從試算表 B16 讀取模型
• 對話音頻：✅ 從試算表 B17 讀取模型
• 圖片生成：✅ 從試算表 B20 讀取模型
• Cloudinary：${config.cloudinaryCloudName ? '✅ 已啟用' : '❌ 未設定'}

💾 **記憶系統**
• 對話記錄：✅ 運作正常
• 檔案記憶：✅ 運作正常
• 群組追蹤：✅ 運作正常

🎯 **模型配置** 🆕
• 一般功能：${config.generalModel || 'gemini-2.5-flash'}
• 圖片分析：${config.visionModel || 'gemini-2.5-pro'}
• TTS 模型：${config.ttsModel || 'gemini-2.5-flash-preview-tts'}
• 對話音頻：${config.audioDialogModel || 'gemini-2.5-flash-preview-native-audio-dialog'}

⚡ **效能狀態**
• 回應速度：正常
• 系統指令識別：100% (預檢測)
• 模型切換：✅ 支援試算表配置
• 函數調用：✅ 統一實現
• 錯誤率：極低
• 可用性：99%+

🔧 最後檢查時間：${new Date().toLocaleString('zh-TW')}
🎉 v1.3.2 修復版：函數調用統一，支援模型自定義切換！`;

  } catch (error) {
    console.error('生成系統狀態失敗:', error);
    return '🔧 系統狀態檢查失敗，但基本功能運作正常。';
  }
}

// ===== 🔧 內容提取函數 =====

/**
 * 🔊 提取 TTS 文字內容
 * 從用戶訊息中提取要轉換成語音的文字
 */
function extractTextForTTS(originalMessage) {
  try {
    // 使用統一提示詞系統提取
    return callAIWithPrompt('TTS_TEXT_EXTRACTION', {
      message: originalMessage
    });
  } catch (error) {
    console.error('TTS 文字提取失敗:', error);
    // 備用：簡單的正則提取
    let extractedText = originalMessage;
    
    // 移除常見的 TTS 指令詞
    extractedText = extractedText.replace(/^(你說|你念|念出來|讀出來|語音|播放|說出來)[：:：\s]*/i, '');
    extractedText = extractedText.replace(/^(tts|語音轉換)[：:：\s]*/i, '');
    
    // 如果提取後為空，返回原始訊息
    return extractedText.trim() || originalMessage;
  }
}

/**
 * 🎨 提取圖像生成提示詞
 * 從用戶訊息中提取圖像描述
 */
function extractImagePrompt(originalMessage) {
  try {
    // 使用統一提示詞系統提取
    return callAIWithPrompt('IMAGE_PROMPT_EXTRACTION', {
      message: originalMessage
    });
  } catch (error) {
    console.error('圖像提示詞提取失敗:', error);
    // 備用：簡單的正則提取
    let extractedPrompt = originalMessage;
    
    // 移除常見的圖像生成指令詞
    extractedPrompt = extractedPrompt.replace(/^(畫|畫一|畫個|生成|生成圖|創建|創建圖|給我.*圖)[：:：\s]*/i, '');
    extractedPrompt = extractedPrompt.replace(/^(draw|generate|create)[：:：\s]*/i, '');
    extractedPrompt = extractedPrompt.replace(/的圖片?$|的圖像?$/i, '');
    
    // 如果提取後為空，返回原始訊息
    return extractedPrompt.trim() || originalMessage;
  }
}

// ===== 🧪 測試函數 =====

/**
 * 🧪 測試函數調用修復
 */
function testFunctionCallFix_debug() {
  console.log('🧪 === 測試函數調用修復 v1.3.2 ===');
  
  try {
    console.log('🔧 檢查函數調用修復狀態...');
    
    // 檢查 TTS 函數調用
    console.log('\n1️⃣ 檢查 TTS 函數調用...');
    try {
      const testText = '測試語音功能';
      const ttsResult = textToSpeechWithGemini(testText);
      console.log(`   TTS 函數調用: ${ttsResult ? '✅ 成功' : '❌ 失敗'}`);
    } catch (ttsError) {
      console.log(`   TTS 函數調用: ❌ 錯誤 - ${ttsError.message}`);
    }
    
    // 檢查對話音頻函數調用
    console.log('\n2️⃣ 檢查對話音頻函數調用...');
    try {
      const testMessage = '你好嗎？';
      const audioResult = callGeminiAudioDialog(testMessage, {});
      console.log(`   對話音頻函數調用: ${audioResult ? '✅ 成功' : '❌ 失敗'}`);
    } catch (audioError) {
      console.log(`   對話音頻函數調用: ❌ 錯誤 - ${audioError.message}`);
    }
    
    // 檢查配置讀取
    console.log('\n3️⃣ 檢查模型配置讀取...');
    const config = getConfig();
    console.log(`   TTS 模型配置: ${config.ttsModel || '❌ 未設定'}`);
    console.log(`   對話音頻模型配置: ${config.audioDialogModel || '❌ 未設定'}`);
    
    // 檢查是否支援模型切換
    console.log('\n4️⃣ 檢查模型切換支援...');
    if (config.audioDialogModel) {
      if (config.audioDialogModel.includes('thinking')) {
        console.log('   🎯 檢測到 thinking 模型設定！');
        console.log('   📢 語音聊天功能應該會使用思考模式');
      } else {
        console.log('   📋 使用標準對話模型');
        console.log('   💡 可在試算表 B17 設定 thinking 模型');
      }
    } else {
      console.log('   ❌ 對話音頻模型未設定');
    }
    
    console.log('\n✅ v1.3.2 修復重點確認：');
    console.log('   • 移除 GeminiAdvanced.gs 中重複的函數 ✅');
    console.log('   • 統一使用 Utils.gs 中的正確實現 ✅');
    console.log('   • 修復 AIHandlers_Specialized.gs 中的函數調用 ✅');
    console.log('   • 支援從試算表讀取模型配置 ✅');
    console.log('   • 支援用戶自定義模型切換 ✅');
    
    console.log('\n🎉 函數調用修復測試完成！');
    return '✅ 函數調用已統一，模型切換已啟用';
    
  } catch (error) {
    console.error('❌ 函數調用修復測試失敗:', error);
    return `❌ 測試失敗: ${error.message}`;
  }
}

/**
 * 🧪 測試專門處理函數 v1.3.2
 */
function testSpecializedHandlers_debug() {
  console.log('🧪 === 測試專門處理函數 v1.3.2 ===');
  
  try {
    console.log('\n📝 測試內容提取函數...');
    testExtractFunctions_debug();
    
    console.log('\n📊 測試生成函數...');
    testGenerateFunctions_debug();
    
    console.log('\n🔧 測試函數調用修復...');
    testFunctionCallFix_debug();
    
    console.log('\n🎉 專門處理函數測試完成！v1.3.2 修復版穩定運行');
    
  } catch (error) {
    console.error('❌ 專門處理函數測試失敗:', error);
  }
}

/**
 * 🧪 測試內容提取函數
 */
function testExtractFunctions_debug() {
  console.log('📝 測試內容提取函數');
  
  const testCases = [
    {
      function: extractTextForTTS,
      name: 'TTS 文字提取',
      tests: [
        '你說：今天天氣很好',
        '念出來：我愛台灣', 
        '語音播放測試',
        'TTS: Hello World'
      ]
    },
    {
      function: extractImagePrompt,
      name: '圖像提示詞提取',
      tests: [
        '畫一隻可愛的小貓',
        '生成圖片：夕陽下的海邊',
        '創建科幻城市',
        'draw a sunset'
      ]
    }
  ];
  
  testCases.forEach(testCase => {
    console.log(`\n🔍 ${testCase.name}:`);
    testCase.tests.forEach((input, index) => {
      try {
        const result = testCase.function(input);
        console.log(`  ${index + 1}. "${input}" → "${result}"`);
      } catch (error) {
        console.log(`  ${index + 1}. "${input}" → ❌ 錯誤: ${error.message}`);
      }
    });
  });
}

/**
 * 🧪 測試生成函數
 */
function testGenerateFunctions_debug() {
  console.log('📊 測試生成函數');
  
  try {
    console.log('\n🔧 測試系統狀態生成...');
    const statusResult = generateSystemStatusResponse();
    console.log(`✅ 系統狀態: ${statusResult.substring(0, 100)}...`);
    
    console.log('\n📚 測試範例生成...');
    const exampleResult = generateModelExamplesResponse({}, 'group');
    console.log(`✅ 範例回應: ${exampleResult.substring(0, 100)}...`);
    
    console.log('\n💬 測試幫助生成...');
    const helpResult = generateEnhancedHelpMessage('group');
    console.log(`✅ 幫助回應: ${helpResult.substring(0, 100)}...`);
    
  } catch (error) {
    console.error('生成函數測試失敗:', error);
  }
}

/**
 * 📋 專門處理模組說明 v1.3.2
 * 
 * 🎯 本檔案職責：
 * - AI 具體處理函數 (handleAI*)
 * - 回應生成函數 (generate*)
 * - 內容提取函數 (extract*)
 * - 測試函數 (*_debug)
 * 
 * 🔗 配合檔案：
 * - TextProcessor_AIFirst.gs (核心流程)
 * - Utils.gs (統一函數實現)
 * - PromptManager.gs (提示詞管理)
 * - helper_functions_v13.gs (通用輔助函數)
 * 
 * 📏 檔案大小：約 25KB
 * 🎉 v1.3.2 改進：修復函數調用，統一使用 Utils.gs 實現
 * 
 * 🔧 重要修復：
 * - callGeminiAudioDialog → 統一使用 Utils.gs 版本
 * - callGeminiTTS → 改為 textToSpeechWithGemini
 * - 支援從試算表讀取模型配置
 * - 啟用用戶自定義模型切換
 */

// ===== 🧠 知識庫整合支援函數 =====

/**
 * 🔍 判斷是否需要搜索知識庫
 * @param {Array} recentResults - 近期對話結果
 * @param {string} userQuery - 用戶查詢
 * @returns {boolean} 是否需要搜索知識庫
 */
function shouldSearchKnowledgeBase(recentResults, userQuery) {
  try {
    // 條件1: 近期結果不足
    if (recentResults.length < 3) {
      console.log('🔍 觸發條件: 近期對話不足');
      return true;
    }

    // 條件2: 明確的歷史查詢關鍵字
    const historyKeywords = /之前|以前|歷史|過去|上個月|上週|去年|曾經|記得|提到過/i;
    if (historyKeywords.test(userQuery)) {
      console.log('🔍 觸發條件: 明確歷史查詢');
      return true;
    }

    // 條件3: 特定發言人查詢
    const speakerQuery = /誰說|說過|提到|討論/i;
    if (speakerQuery.test(userQuery)) {
      console.log('🔍 觸發條件: 發言人查詢');
      return true;
    }

    return false;
  } catch (error) {
    console.error('判斷知識庫搜索條件失敗:', error);
    return false;
  }
}

/**
 * 🔤 從用戶查詢中提取搜索關鍵字
 * @param {string} userQuery - 用戶查詢
 * @returns {string} 搜索關鍵字
 */
function extractSearchKeywords(userQuery) {
  try {
    // 移除常見的查詢詞彙，保留核心關鍵字
    const cleanQuery = userQuery
      .replace(/我們|聊了|什麼|討論|說過|提到|關於|的話|嗎|？|\?/g, '')
      .replace(/之前|以前|歷史|過去|上個月|上週|去年|曾經/g, '')
      .trim();

    return cleanQuery || userQuery;
  } catch (error) {
    console.error('提取搜索關鍵字失敗:', error);
    return userQuery;
  }
}

/**
 * 👤 從用戶查詢中提取目標發言人
 * @param {string} userQuery - 用戶查詢
 * @returns {string|null} 目標發言人
 */
function extractTargetSpeaker(userQuery) {
  try {
    // 簡單的發言人提取邏輯
    const speakerMatch = userQuery.match(/(\w+)\s*說過|(\w+)\s*提到|(\w+)\s*討論/);
    if (speakerMatch) {
      return speakerMatch[1] || speakerMatch[2] || speakerMatch[3];
    }

    return null;
  } catch (error) {
    console.error('提取目標發言人失敗:', error);
    return null;
  }
}

/**
 * 📋 格式化對話結果
 * @param {Array} recentHistory - 近期對話歷史
 * @param {Array} knowledgeResults - 知識庫搜索結果
 * @param {string} originalQuery - 原始查詢
 * @returns {string} 格式化的回應
 */
function formatConversationResults(recentHistory, knowledgeResults, originalQuery) {
  try {
    let response = '🧠 對話回顧結果\n\n';

    // 1. 近期對話部分
    if (recentHistory.length > 0) {
      response += '🕐 近期對話：\n';
      const recentTopics = recentHistory.slice(-5).map((conv, index) => {
        const shortMessage = conv.message.length > 30 ?
          conv.message.substring(0, 30) + '...' : conv.message;
        return `• ${shortMessage}`;
      }).join('\n');
      response += recentTopics + '\n\n';
    }

    // 2. 知識庫結果部分
    if (knowledgeResults.length > 0) {
      response += '📚 歷史記錄：\n';
      const historyTopics = knowledgeResults.slice(0, 5).map(record => {
        const shortContent = record.content.length > 40 ?
          record.content.substring(0, 40) + '...' : record.content;
        return `• [${record.timestamp}] ${record.speaker}: ${shortContent}`;
      }).join('\n');
      response += historyTopics + '\n\n';
    }

    // 3. 統計信息
    const totalResults = recentHistory.length + knowledgeResults.length;
    response += `💡 共找到 ${totalResults} 筆相關記錄`;

    if (recentHistory.length > 0 && knowledgeResults.length > 0) {
      response += `（近期 ${recentHistory.length} 筆，歷史 ${knowledgeResults.length} 筆）`;
    }

    // 4. 如果沒有找到任何結果
    if (totalResults === 0) {
      response = '🤔 暫時沒有找到相關的對話記錄。\n\n💡 開始和我對話後，我就能為您回顧對話內容了！';
    }

    return response;

  } catch (error) {
    console.error('格式化對話結果失敗:', error);
    return '⚠️ 對話回顧功能暫時無法使用，請稍後再試';
  }
}
