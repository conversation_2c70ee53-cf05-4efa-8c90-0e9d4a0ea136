# 自動部署 LINE Bot 到 Google Apps Script - OAuth 認證版本 + 固定部署ID更新
name: Deploy LINE Bot to Google Apps Script

# 觸發條件
on:
  push:
    branches:
      - main
  workflow_dispatch:
    inputs:
      deploy_message:
        description: '部署說明'
        required: false
        default: 'Manual deployment'

# 工作內容
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      # 步驟 1: 取得程式碼
      - name: Checkout repository
        uses: actions/checkout@v4

      # 步驟 2: 設定 Node.js 環境
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      # 步驟 3: 安裝 clasp
      - name: Install clasp
        run: |
          npm install -g @google/clasp@latest
          echo "✅ clasp 安裝完成"

      # 步驟 4: 設置 OAuth 認證 + 檢查固定部署ID
      - name: Setup OAuth Authentication and Deployment ID
        run: |
          echo "=== 設置 OAuth 認證和部署ID ==="
          
          # 檢查必要的 secrets
          if [ -z "${{ secrets.CLASPRC_JSON }}" ]; then
            echo "❌ 缺少 CLASPRC_JSON secret"
            echo "請確保已按照設置指南添加此 secret"
            exit 1
          fi
          
          if [ -z "${{ secrets.GAS_DEPLOYMENT_ID }}" ]; then
            echo "❌ 缺少 GAS_DEPLOYMENT_ID secret"
            echo "請確保已設置固定的部署 ID"
            echo "LINE Bot 需要固定 URL 才能正常接收 Webhook"
            exit 1
          fi
          
          # 建立認證檔案
          echo '${{ secrets.CLASPRC_JSON }}' > ~/.clasprc.json
          
          # 驗證 JSON 格式
          if ! jq empty ~/.clasprc.json 2>/dev/null; then
            echo "❌ CLASPRC_JSON 格式錯誤"
            exit 1
          fi
          
          echo "✅ OAuth 認證設置完成"
          echo "✅ LINE Bot 部署 ID 已確認: ${{ secrets.GAS_DEPLOYMENT_ID }}"

      # 步驟 5: 檢查 LINE Bot 專案配置
      - name: Verify LINE Bot project configuration
        run: |
          echo "=== 檢查 LINE Bot 專案配置 ==="
          
          # 檢查 .clasp.json
          if [ ! -f ".clasp.json" ]; then
            echo "❌ 找不到 .clasp.json 檔案"
            exit 1
          fi
          
          SCRIPT_ID=$(jq -r '.scriptId' .clasp.json)
          echo "🎯 LINE Bot 專案 ID: $SCRIPT_ID"
          
          # 檢查專案ID格式
          if [ ${#SCRIPT_ID} -eq 57 ]; then
            echo "✅ 專案 ID 格式正確"
          else
            echo "⚠️ 專案 ID 格式可能異常，長度: ${#SCRIPT_ID}"
          fi
          
          # 檢查是否包含關鍵的 LINE Bot 檔案
          if [ -f "Webhook.gs" ]; then
            echo "✅ 找到 Webhook.gs - LINE Bot 核心模組"
          else
            echo "⚠️ 找不到 Webhook.gs - LINE Bot 可能無法正常運行"
          fi

      # 步驟 6: 準備 LINE Bot 檔案清單
      - name: Prepare LINE Bot deployment files
        run: |
          echo "=== 準備 LINE Bot 檔案清單 ==="
          
          # 列出所有要推送的檔案
          echo "📋 將推送的 LINE Bot 檔案:"
          find . -maxdepth 1 \( -name "*.gs" -o -name "*.html" -o -name "appsscript.json" \) | sort | while read file; do
            size=$(stat -c%s "$file" 2>/dev/null || echo "unknown")
            echo "   • $file ($size bytes)"
          done
          
          # 統計檔案數量
          GS_COUNT=$(find . -maxdepth 1 -name "*.gs" | wc -l)
          HTML_COUNT=$(find . -maxdepth 1 -name "*.html" | wc -l)
          JSON_COUNT=$(find . -maxdepth 1 -name "appsscript.json" | wc -l)
          TOTAL_COUNT=$((GS_COUNT + HTML_COUNT + JSON_COUNT))
          
          echo ""
          echo "📊 LINE Bot 檔案統計:"
          echo "   .gs 檔案: $GS_COUNT"
          echo "   .html 檔案: $HTML_COUNT"
          echo "   appsscript.json: $JSON_COUNT"
          echo "   總計: $TOTAL_COUNT"
          
          # 檢查是否有檔案要推送
          if [ $TOTAL_COUNT -eq 0 ]; then
            echo "❌ 沒有檔案要推送"
            exit 1
          fi
          
          # 檢查關鍵的 LINE Bot 模組
          echo ""
          echo "🔍 檢查關鍵 LINE Bot 模組:"
          for module in "Code.gs" "Webhook.gs" "TextProcessor.gs" "MediaProcessor.gs" "Utils.gs"; do
            if [ -f "$module" ]; then
              echo "   ✅ $module"
            else
              echo "   ❌ $module (缺失)"
            fi
          done

      # 步驟 7: 測試 clasp 連接
      - name: Test clasp connection
        run: |
          echo "=== 測試 clasp 連接 ==="
          
          # 測試認證狀態
          if clasp status; then
            echo "✅ OAuth 認證連接正常"
          else
            echo "❌ clasp 連接失敗"
            echo "可能原因: token 過期或權限問題"
            exit 1
          fi

      # 步驟 8: 推送 LINE Bot 檔案到 Google Apps Script
      - name: Push LINE Bot files to Google Apps Script
        run: |
          echo "=== 推送 LINE Bot 檔案到 Google Apps Script ==="
          echo ""
          
          # 執行推送
          echo "🚀 開始推送 LINE Bot 檔案..."
          START_TIME=$(date +%s)
          
          if clasp push --force; then
            END_TIME=$(date +%s)
            DURATION=$((END_TIME - START_TIME))
            
            echo ""
            echo "🎉 LINE Bot 檔案推送成功！"
            echo "⏱️ 推送耗時: ${DURATION} 秒"
            echo "✅ 所有 LINE Bot 模組已更新到 Google Apps Script"
          else
            echo ""
            echo "❌ LINE Bot 檔案推送失敗"
            echo "請檢查錯誤訊息進行診斷"
            exit 1
          fi

      # 🚀 步驟 9: 創建新版本並更新固定部署ID (LINE Bot 關鍵步驟)
      - name: Create version and update LINE Bot deployment
        id: create_version
        env:
          COMMIT_MSG: ${{ github.event.head_commit.message || 'TTS 和圖像生成功能更新' }}
          GITHUB_SHA: ${{ github.sha }}
          DEPLOY_MESSAGE: ${{ github.event.inputs.deploy_message }}
        run: |
          echo "=== 🚀 創建新版本並更新 LINE Bot 部署 ==="
          echo ""

          # 獲取部署描述 - 使用環境變數安全傳遞
          if [ -n "$DEPLOY_MESSAGE" ]; then
            DEPLOY_DESC="LINE Bot: $DEPLOY_MESSAGE"
          else
            COMMIT_SHA_SHORT=$(echo "$GITHUB_SHA" | cut -c1-7)
            DEPLOY_DESC="LINE Bot Auto deploy: $COMMIT_MSG (commit: $COMMIT_SHA_SHORT)"
          fi
          
          echo "📝 部署描述: $DEPLOY_DESC"
          echo "🎯 LINE Bot 部署 ID: ${{ secrets.GAS_DEPLOYMENT_ID }}"
          echo "🤖 這將更新 LINE Webhook URL 對應的服務"
          echo ""
          
          # 創建新版本
          echo "📦 創建新版本..."
          VERSION_START_TIME=$(date +%s)
          
          VERSION_OUT=$(clasp version "$DEPLOY_DESC")
          if [ $? -eq 0 ]; then
            VERSION_NUMBER=$(echo "$VERSION_OUT" | grep -o '[0-9]\+$')
            echo "version_number=$VERSION_NUMBER" >> "$GITHUB_OUTPUT"
            
            VERSION_END_TIME=$(date +%s)
            VERSION_DURATION=$((VERSION_END_TIME - VERSION_START_TIME))
            
            echo "✅ 新版本創建成功：版本 $VERSION_NUMBER"
            echo "⏱️ 版本創建耗時: ${VERSION_DURATION} 秒"
          else
            echo "❌ 創建版本失敗"
            exit 1
          fi
          
          echo ""
          
          # 更新固定部署ID到新版本 (LINE Bot 的 Webhook URL 保持不變)
          echo "🚀 更新 LINE Bot 部署到新版本..."
          DEPLOY_START_TIME=$(date +%s)
          
          if clasp deploy --deploymentId "${{ secrets.GAS_DEPLOYMENT_ID }}" --versionNumber "$VERSION_NUMBER" --description "$DEPLOY_DESC"; then
            DEPLOY_END_TIME=$(date +%s)
            DEPLOY_DURATION=$((DEPLOY_END_TIME - DEPLOY_START_TIME))
            
            echo ""
            echo "🎉 LINE Bot 部署更新成功！"
            echo "⏱️ 部署耗時: ${DEPLOY_DURATION} 秒"
            echo "✅ 部署 ID 保持不變，內容已更新到版本 $VERSION_NUMBER"
            echo "🌐 LINE Webhook URL 立即生效，無需更改 LINE Developers Console 設定"
          else
            echo ""
            echo "❌ LINE Bot 部署更新失敗"
            echo "⚠️ 版本已創建但部署失敗，LINE Bot 可能無法正常運行"
            echo "需要手動部署或檢查權限設定"
            exit 1
          fi

      # 步驟 10: 獲取 LINE Bot Web App URL 和部署信息
      - name: Get LINE Bot Web App URL and deployment info
        run: |
          echo "=== 🔗 LINE Bot 訪問信息 ==="
          
          SCRIPT_ID=$(jq -r '.scriptId' .clasp.json)
          
          echo "📋 LINE Bot 重要連結："
          echo "   🔧 GAS 編輯器: https://script.google.com/home/<USER>/$SCRIPT_ID/edit"
          echo "   📊 部署管理: https://script.google.com/home/<USER>/$SCRIPT_ID/deployments"
          echo "   🤖 LINE Webhook URL: https://script.google.com/macros/s/${{ secrets.GAS_DEPLOYMENT_ID }}/exec"
          echo "   🎮 測試頁面: https://script.google.com/macros/s/${{ secrets.GAS_DEPLOYMENT_ID }}/exec"
          echo ""
          echo "✅ 部署 ID 永久固定，LINE Webhook URL 永不改變"
          echo "📦 當前版本：${{ steps.create_version.outputs.version_number }}"
          echo "🔗 請確認此 URL 已設定在 LINE Developers Console 的 Webhook URL"
          
          echo ""
          echo "📋 當前部署狀態："
          clasp deployments

      # 步驟 11: 生成 LINE Bot 部署報告
      - name: Generate LINE Bot deployment report
        if: success()
        run: |
          echo "=== 📊 LINE Bot 完整部署報告 ==="
          
          SCRIPT_ID=$(jq -r '.scriptId' .clasp.json)
          DEPLOY_TIME=$(date '+%Y-%m-%d %H:%M:%S UTC')
          COMMIT_SHA=${GITHUB_SHA:0:7}
          COMMIT_MSG="${{ github.event.head_commit.message }}"
          
          echo "🎉 LINE Bot 部署完成時間: $DEPLOY_TIME"
          echo "📝 專案 ID: $SCRIPT_ID"
          echo "🎯 部署 ID: ${{ secrets.GAS_DEPLOYMENT_ID }}"
          echo "📦 版本號: ${{ steps.create_version.outputs.version_number }}"
          echo "📝 Commit: $COMMIT_SHA"
          echo "💬 Commit 訊息: $COMMIT_MSG"
          echo "📊 推送檔案數量: $(find . -maxdepth 1 \( -name "*.gs" -o -name "*.html" -o -name "appsscript.json" \) | wc -l)"
          echo "✅ 認證方式: OAuth"
          echo ""
          echo "📋 LINE Bot 自動化流程狀態:"
          echo "   ✅ 代碼推送: 成功"
          echo "   ✅ 版本創建: 成功"
          echo "   ✅ 部署更新: 成功"
          echo "   ✅ Webhook URL 固定: 是"
          echo "   ✅ 立即生效: 是"
          echo "   ✅ 需要手動操作: 否"
          echo ""
          echo "🤖 您的 LINE Bot 智能助理已自動更新並上線！"
          echo "📱 用戶可以立即使用新版本功能"

      # 步驟 12: 清理敏感檔案
      - name: Cleanup sensitive files
        if: always()
        run: |
          echo "=== 清理敏感檔案 ==="
          rm -f ~/.clasprc.json
          echo "✅ 敏感檔案清理完成"

      # 步驟 13: LINE Bot 部署失敗處理
      - name: Handle LINE Bot deployment failure
        if: failure()
        run: |
          echo "❌ LINE Bot 部署失敗診斷"
          echo ""
          echo "可能原因:"
          echo "1. CLASPRC_JSON secret 格式錯誤或過期"
          echo "2. GAS_DEPLOYMENT_ID secret 不正確或不存在"
          echo "3. Apps Script API 未在個人帳戶中啟用"
          echo "4. OAuth token 需要刷新"
          echo "5. 專案權限不足"
          echo "6. LINE Bot Web App權限設置問題"
          echo "7. LINE Bot 模組檔案缺失或損壞"
          echo ""
          echo "解決方案:"
          echo "1. 重新執行 clasp login 並更新 CLASPRC_JSON"
          echo "2. 確認 GAS_DEPLOYMENT_ID 是正確的部署ID"
          echo "3. 確認 https://script.google.com/home/<USER>"
          echo "4. 檢查專案共享權限"
          echo "5. 檢查 LINE Bot Web App 的執行和訪問權限設置"
          echo "6. 確認所有 LINE Bot 模組檔案完整"
          echo ""
          echo "🔗 快速修復："
          echo "   手動部署: https://script.google.com/home/<USER>/$(jq -r '.scriptId' .clasp.json)/deployments"
          echo "   LINE 設定: https://developers.line.biz/console/"
