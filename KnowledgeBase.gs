// == 知識庫管理模組 ==
// 🧠 v1.4.0 - 聊天歷史知識庫功能
// 處理聊天歷史的讀取、轉換和搜索

/**
 * 🔍 知識庫搜索主函數
 * @param {string} query - 搜索查詢
 * @param {string} speaker - 指定發言人（可選）
 * @param {number} maxResults - 最大結果數量
 * @returns {Array} 搜索結果
 */
function searchKnowledgeBase(query, speaker = null, maxResults = 10) {
  try {
    console.log(`🔍 知識庫搜索: "${query}", 發言人: ${speaker || '全部'}`);
    
    // 1. 載入知識庫資料
    const knowledgeData = loadKnowledgeBase();
    if (!knowledgeData || knowledgeData.length === 0) {
      console.log('📚 知識庫為空或載入失敗');
      return [];
    }
    
    // 2. 執行搜索
    const results = performSearch(knowledgeData, query, speaker, maxResults);
    
    console.log(`✅ 知識庫搜索完成，找到 ${results.length} 筆結果`);
    return results;
    
  } catch (error) {
    console.error('知識庫搜索失敗:', error);
    return [];
  }
}

/**
 * 📚 載入知識庫資料
 * 優先讀取 CSV，如果不存在則從 TXT 轉換
 * @returns {Array} 知識庫資料陣列
 */
function loadKnowledgeBase() {
  try {
    // 1. 優先嘗試讀取 CSV 檔案
    const csvData = loadCsvKnowledgeBase();
    if (csvData && csvData.length > 0) {
      console.log(`📊 成功載入 CSV 知識庫，共 ${csvData.length} 筆記錄`);
      return csvData;
    }
    
    // 2. 備用：從 TXT 轉換
    console.log('📝 CSV 不存在，嘗試從 TXT 轉換...');
    const txtData = loadAndConvertTxtKnowledgeBase();
    if (txtData && txtData.length > 0) {
      console.log(`🔄 成功從 TXT 轉換，共 ${txtData.length} 筆記錄`);
      return txtData;
    }
    
    console.log('❌ 無法載入知識庫資料');
    return [];
    
  } catch (error) {
    console.error('載入知識庫失敗:', error);
    return [];
  }
}

/**
 * 📊 讀取 CSV 知識庫檔案
 * @returns {Array} CSV 資料陣列
 */
function loadCsvKnowledgeBase() {
  try {
    // 嘗試從 Google Drive 讀取 CSV 檔案
    const files = DriveApp.getFilesByName('chatting_history.csv');
    if (!files.hasNext()) {
      console.log('📊 CSV 檔案不存在');
      return null;
    }
    
    const file = files.next();
    const csvContent = file.getBlob().getDataAsString('UTF-8');
    
    // 解析 CSV 內容
    return parseCsvContent(csvContent);
    
  } catch (error) {
    console.error('讀取 CSV 檔案失敗:', error);
    return null;
  }
}

/**
 * 📝 讀取並轉換 TXT 知識庫檔案
 * @returns {Array} 轉換後的資料陣列
 */
function loadAndConvertTxtKnowledgeBase() {
  try {
    // 嘗試從 Google Drive 讀取 TXT 檔案
    const files = DriveApp.getFilesByName('chatting_history.txt');
    if (!files.hasNext()) {
      console.log('📝 TXT 檔案也不存在');
      return null;
    }
    
    const file = files.next();
    const txtContent = file.getBlob().getDataAsString('UTF-8');
    
    // 解析並轉換 TXT 內容
    const convertedData = parseTxtContent(txtContent);
    
    // 保存為 CSV 檔案供下次使用
    if (convertedData && convertedData.length > 0) {
      saveCsvKnowledgeBase(convertedData);
    }
    
    return convertedData;
    
  } catch (error) {
    console.error('讀取/轉換 TXT 檔案失敗:', error);
    return null;
  }
}

/**
 * 🔧 解析 CSV 內容
 * @param {string} csvContent - CSV 檔案內容
 * @returns {Array} 解析後的資料陣列
 */
function parseCsvContent(csvContent) {
  try {
    const lines = csvContent.split('\n').filter(line => line.trim());
    const data = [];
    
    // 跳過標頭行（第一行）
    for (let i = 1; i < lines.length; i++) {
      const line = lines[i].trim();
      if (!line) continue;
      
      // 簡單 CSV 解析（假設沒有複雜的逗號轉義）
      const columns = line.split(',');
      if (columns.length >= 3) {
        data.push({
          timestamp: columns[0]?.trim() || '',
          speaker: columns[1]?.trim() || '',
          content: columns[2]?.trim() || '',
          tags: columns[3]?.trim() || ''
        });
      }
    }
    
    return data;
    
  } catch (error) {
    console.error('解析 CSV 內容失敗:', error);
    return [];
  }
}

/**
 * 🔄 解析 TXT 內容並轉換為結構化資料
 * @param {string} txtContent - TXT 檔案內容
 * @returns {Array} 轉換後的資料陣列
 */
function parseTxtContent(txtContent) {
  try {
    const lines = txtContent.split('\n').filter(line => line.trim());
    const data = [];
    
    lines.forEach(line => {
      // 解析格式：[時間] 發言人: 內容
      const match = line.match(/\[(.*?)\]\s*(.*?):\s*(.*)/);
      if (match) {
        const [, timestamp, speaker, content] = match;
        const tags = generateSimpleTopicTags(content);
        
        data.push({
          timestamp: timestamp.trim(),
          speaker: speaker.trim(),
          content: content.trim(),
          tags: tags
        });
      }
    });
    
    return data;
    
  } catch (error) {
    console.error('解析 TXT 內容失敗:', error);
    return [];
  }
}

/**
 * 🏷️ 生成簡單主題標籤
 * @param {string} content - 對話內容
 * @returns {string} 標籤字串（逗號分隔）
 */
function generateSimpleTopicTags(content) {
  try {
    // 簡單關鍵字匹配
    const keywords = [
      '部署', '語音', 'AI', '測試', '功能', '修復', '更新', '問題',
      '聊天', '對話', '圖片', '檔案', '記錄', '查詢', '幫助', '設定'
    ];
    
    const foundTags = keywords.filter(keyword => content.includes(keyword));
    
    // 如果找到標籤就返回，否則返回空字串
    return foundTags.length > 0 ? foundTags.slice(0, 3).join(',') : '';
    
  } catch (error) {
    console.error('生成標籤失敗:', error);
    return '';
  }
}

/**
 * 💾 保存 CSV 知識庫檔案
 * @param {Array} data - 要保存的資料
 */
function saveCsvKnowledgeBase(data) {
  try {
    // 建立 CSV 內容
    let csvContent = '時間,發言人,內容,主題標籤\n';
    
    data.forEach(record => {
      // 簡單的 CSV 格式化（不處理複雜的逗號轉義）
      csvContent += `${record.timestamp},${record.speaker},${record.content},${record.tags}\n`;
    });
    
    // 保存到 Google Drive
    const blob = Utilities.newBlob(csvContent, 'text/csv', 'chatting_history.csv');
    DriveApp.createFile(blob);
    
    console.log('💾 CSV 知識庫檔案已保存');
    
  } catch (error) {
    console.error('保存 CSV 檔案失敗:', error);
  }
}

/**
 * 🔍 執行實際搜索
 * @param {Array} data - 知識庫資料
 * @param {string} query - 搜索查詢
 * @param {string} speaker - 指定發言人
 * @param {number} maxResults - 最大結果數量
 * @returns {Array} 搜索結果
 */
function performSearch(data, query, speaker, maxResults) {
  try {
    let results = [];
    const queryLower = query.toLowerCase();
    
    // 搜索邏輯
    data.forEach(record => {
      let score = 0;
      
      // 1. 發言人匹配
      if (speaker && record.speaker.includes(speaker)) {
        score += 10;
      } else if (speaker) {
        return; // 如果指定了發言人但不匹配，跳過
      }
      
      // 2. 內容匹配
      if (record.content.toLowerCase().includes(queryLower)) {
        score += 5;
      }
      
      // 3. 標籤匹配
      if (record.tags && record.tags.toLowerCase().includes(queryLower)) {
        score += 3;
      }
      
      // 如果有匹配，加入結果
      if (score > 0 || (!speaker && record.content.toLowerCase().includes(queryLower))) {
        results.push({
          ...record,
          score: score,
          relevance: score > 0 ? 'high' : 'medium'
        });
      }
    });
    
    // 按分數排序並限制結果數量
    results.sort((a, b) => b.score - a.score);
    return results.slice(0, maxResults);
    
  } catch (error) {
    console.error('執行搜索失敗:', error);
    return [];
  }
}

/**
 * 🧪 測試知識庫功能
 */
function testKnowledgeBase_debug() {
  console.log('🧪 === 測試知識庫功能 ===');

  try {
    // 測試載入
    console.log('\n1️⃣ 測試載入知識庫...');
    const data = loadKnowledgeBase();
    console.log(`載入結果: ${data ? data.length : 0} 筆記錄`);

    if (data && data.length > 0) {
      // 顯示前幾筆資料樣本
      console.log('\n📊 資料樣本:');
      data.slice(0, 3).forEach((record, index) => {
        console.log(`   ${index + 1}. [${record.timestamp}] ${record.speaker}: ${record.content.substring(0, 50)}...`);
      });

      // 測試搜索
      console.log('\n2️⃣ 測試搜索功能...');
      const searchResults = searchKnowledgeBase('語音', null, 5);
      console.log(`搜索 "語音" 結果: ${searchResults.length} 筆`);

      // 測試發言人搜索
      console.log('\n3️⃣ 測試發言人搜索...');
      const speakerResults = searchKnowledgeBase('', 'Michael', 3);
      console.log(`搜索 "Michael" 發言: ${speakerResults.length} 筆`);

      // 測試整合功能
      console.log('\n4️⃣ 測試整合功能...');
      const mockIntent = {
        original_message: '我們之前討論過語音功能嗎？'
      };
      const integrationResult = handleAIConversationReview(mockIntent, 'test_user', 'user');
      console.log(`整合測試結果長度: ${integrationResult.length} 字符`);
    }

    console.log('\n✅ 知識庫功能測試完成');

  } catch (error) {
    console.error('❌ 知識庫測試失敗:', error);
  }
}

/**
 * 🔧 創建測試資料（用於開發測試）
 */
function createTestKnowledgeBase_debug() {
  console.log('🔧 === 創建測試知識庫資料 ===');

  try {
    const testData = [
      {
        timestamp: '2024-06-01 10:30',
        speaker: 'Michael',
        content: '今天要部署新版本的 LINE Bot',
        tags: '部署,LINE Bot'
      },
      {
        timestamp: '2024-06-01 10:31',
        speaker: 'Alice',
        content: '記得測試語音功能是否正常',
        tags: '測試,語音'
      },
      {
        timestamp: '2024-06-01 10:32',
        speaker: 'Bob',
        content: '語音聊天功能很棒，用戶反應很好',
        tags: '語音,功能'
      },
      {
        timestamp: '2024-06-01 11:00',
        speaker: 'Michael',
        content: '部署完成，所有功能都正常運作',
        tags: '部署,功能'
      },
      {
        timestamp: '2024-06-01 11:15',
        speaker: 'Alice',
        content: 'AI 回應的準確度有明顯提升',
        tags: 'AI,功能'
      }
    ];

    // 保存測試資料
    saveCsvKnowledgeBase(testData);
    console.log(`✅ 已創建 ${testData.length} 筆測試資料`);

    // 驗證保存結果
    const loadedData = loadKnowledgeBase();
    console.log(`🔍 驗證載入: ${loadedData ? loadedData.length : 0} 筆記錄`);

  } catch (error) {
    console.error('❌ 創建測試資料失敗:', error);
  }
}

/**
 * 🚀 完整系統測試
 */
function testCompleteKnowledgeBaseSystem_debug() {
  console.log('🚀 === 完整知識庫系統測試 ===');

  try {
    // 1. 創建測試資料
    console.log('\n1️⃣ 創建測試資料...');
    createTestKnowledgeBase_debug();

    // 2. 測試基礎功能
    console.log('\n2️⃣ 測試基礎功能...');
    testKnowledgeBase_debug();

    // 3. 測試各種查詢場景
    console.log('\n3️⃣ 測試查詢場景...');

    const testQueries = [
      '我們討論過語音功能嗎？',
      'Michael 說過什麼關於部署的？',
      '之前聊了什麼？',
      'Alice 提到過什麼？'
    ];

    testQueries.forEach((query, index) => {
      console.log(`\n   測試 ${index + 1}: "${query}"`);

      const mockIntent = { original_message: query };
      const result = handleAIConversationReview(mockIntent, 'test_user', 'user');

      console.log(`   結果長度: ${result.length} 字符`);
      console.log(`   包含歷史記錄: ${result.includes('📚 歷史記錄') ? '是' : '否'}`);
    });

    console.log('\n✅ 完整系統測試完成');
    console.log('\n🎯 知識庫功能已準備就緒！');
    console.log('📝 使用方式：');
    console.log('   • 將 chatting_history.csv 或 chatting_history.txt 放入 Google Drive');
    console.log('   • 用戶查詢對話歷史時會自動搜索知識庫');
    console.log('   • 支援發言人查詢、關鍵字搜索、時間範圍查詢');

  } catch (error) {
    console.error('❌ 完整系統測試失敗:', error);
  }
}
