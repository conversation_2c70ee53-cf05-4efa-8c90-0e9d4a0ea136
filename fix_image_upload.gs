/**
 * 🔧 上傳圖片到 Cloudinary（修復版）
 */
function uploadImageToCloudinary(imageBlob, originalPrompt) {
  const config = getConfig();
  
  // 檢查 Cloudinary 配置
  if (!config.cloudinaryCloudName || !config.cloudinaryApiKey || !config.cloudinaryApiSecret) {
    throw new Error('請在 APIKEY 工作表中設定 Cloudinary 配置');
  }
  
  try {
    console.log(`🌩️ 上傳圖片到 Cloudinary...`);
    
    // 生成唯一的檔案名稱
    const timestamp = new Date().getTime();
    const publicId = `linebot/images/generated_${timestamp}`;
    
    // 準備上傳參數（圖片上傳通常不需要 resource_type）
    const uploadParams = {
      public_id: publicId,
      timestamp: Math.floor(Date.now() / 1000)
    };
    
    // 生成簽名
    const signature = generateCloudinarySignature(uploadParams, config.cloudinaryApiSecret);
    
    // 準備表單數據
    const formData = {
      file: imageBlob,
      public_id: publicId,
      api_key: config.cloudinaryApiKey,
      timestamp: String(uploadParams.timestamp),
      signature: signature
    };
    
    // 上傳到 Cloudinary
    const uploadUrl = `https://api.cloudinary.com/v1_1/${config.cloudinaryCloudName}/image/upload`;
    
    const response = UrlFetchApp.fetch(uploadUrl, {
      method: 'POST',
      payload: formData,
      muteHttpExceptions: true
    });
    
    if (response.getResponseCode() !== 200) {
      throw new Error(`Cloudinary 圖片上傳失敗: ${response.getResponseCode()} - ${response.getContentText()}`);
    }
    
    const result = JSON.parse(response.getContentText());  // 修復：使用正確的方法獲取回應文本
    
    if (!result.secure_url) {
      throw new Error('Cloudinary 未返回有效的圖片 URL');
    }
    
    console.log(`✅ Cloudinary 圖片上傳成功: ${result.secure_url}`);
    logActivity('Cloudinary圖片上傳', `公開ID: ${publicId}, URL: ${result.secure_url}`);
    
    return result.secure_url;
    
  } catch (error) {
    console.error('Cloudinary 圖片上傳錯誤:', error);
    throw error;
  }
}
