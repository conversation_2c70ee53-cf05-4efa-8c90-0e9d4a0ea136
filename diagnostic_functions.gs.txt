// 🧪 測試函數：診斷 LINE BOT 連接問題
function diagnoseLINEBotConnection() {
  console.log('=== LINE BOT 連接診斷開始 ===');
  
  try {
    // 1. 檢查配置
    console.log('📋 1. 檢查配置...');
    const config = getConfig();
    
    console.log('✅ 配置檢查結果：');
    console.log(`• LINE Token: ${config.lineChannelAccessToken ? '✅ 已設定' : '❌ 未設定'}`);
    console.log(`• LINE Secret: ${config.lineChannelSecret ? '✅ 已設定' : '❌ 未設定'}`);
    console.log(`• Gemini API: ${config.geminiApiKey ? '✅ 已設定' : '❌ 未設定'}`);
    console.log(`• 一般模型: ${config.generalModel}`);
    console.log(`• 視覺模型: ${config.visionModel}`);
    
    // 2. 測試 Gemini API
    console.log('\n🤖 2. 測試 Gemini API...');
    if (config.geminiApiKey) {
      try {
        const testResponse = callGemini('請簡短回答：你好', 'general');
        console.log(`✅ Gemini API 測試成功: ${testResponse.substring(0, 50)}...`);
      } catch (geminiError) {
        console.log(`❌ Gemini API 測試失敗: ${geminiError.message}`);
      }
    } else {
      console.log('❌ Gemini API Key 未設定，跳過測試');
    }
    
    // 3. 檢查工作表
    console.log('\n📊 3. 檢查工作表...');
    const ss = SpreadsheetApp.getActiveSpreadsheet();
    const activitySheet = ss.getSheetByName('活動日誌');
    const apikeySheet = ss.getSheetByName('APIKEY');
    
    console.log(`• 活動日誌工作表: ${activitySheet ? '✅ 存在' : '❌ 不存在'}`);
    console.log(`• APIKEY 工作表: ${apikeySheet ? '✅ 存在' : '❌ 不存在'}`);
    
    if (activitySheet) {
      const lastRow = activitySheet.getLastRow();
      console.log(`• 活動日誌記錄數: ${Math.max(0, lastRow - 1)} 筆`);
      
      // 顯示最近的活動
      if (lastRow > 1) {
        console.log('\n📋 最近 5 筆活動記錄：');
        const recentData = activitySheet.getRange(Math.max(2, lastRow - 4), 1, Math.min(5, lastRow - 1), 3).getValues();
        recentData.forEach((row, index) => {
          console.log(`${index + 1}. ${row[0]} | ${row[1]} | ${row[2]}`);
        });
      }
    }
    
    // 4. 測試模型配置
    console.log('\n🔧 4. 測試模型配置...');
    try {
      const generalModelVersion = getModelApiVersion(config.generalModel);
      const visionModelVersion = getModelApiVersion(config.visionModel);
      console.log(`✅ 一般模型 ${config.generalModel} → API版本: ${generalModelVersion}`);
      console.log(`✅ 視覺模型 ${config.visionModel} → API版本: ${visionModelVersion}`);
    } catch (modelError) {
      console.log(`❌ 模型配置測試失敗: ${modelError.message}`);
    }
    
    // 5. 記錄診斷結果
    logActivity('系統診斷', '完整 LINE BOT 連接診斷已完成');
    
    console.log('\n🎯 診斷完成！請檢查上述結果，並查看活動日誌工作表');
    
    return '診斷完成 - 請查看執行日誌';
    
  } catch (error) {
    console.error('❌ 診斷過程錯誤:', error);
    logActivity('系統診斷錯誤', error.toString());
    return `診斷失敗: ${error.message}`;
  }
}

// 🧪 測試函數：模擬 LINE Webhook 請求
function testWebhookSimulation() {
  console.log('=== 模擬 LINE Webhook 測試 ===');
  
  // 模擬一個簡單的 LINE 文字訊息
  const mockEvent = {
    postData: {
      contents: JSON.stringify({
        events: [{
          type: 'message',
          message: {
            type: 'text',
            text: '測試訊息'
          },
          source: {
            type: 'user',
            userId: 'test-user-12345'
          },
          replyToken: 'test-reply-token-12345'
        }]
      })
    }
  };
  
  try {
    console.log('📤 模擬發送 Webhook 請求...');
    
    // 直接調用 doPost 函數（在 Webhook.gs 中）
    const result = doPost(mockEvent);
    
    console.log('✅ Webhook 處理完成');
    console.log('📋 處理結果:', result);
    
    // 檢查活動日誌是否有新記錄
    const ss = SpreadsheetApp.getActiveSpreadsheet();
    const activitySheet = ss.getSheetByName('活動日誌');
    
    if (activitySheet && activitySheet.getLastRow() > 1) {
      const lastActivity = activitySheet.getRange(activitySheet.getLastRow(), 1, 1, 3).getValues()[0];
      console.log('📊 最新活動記錄:', lastActivity);
    }
    
    return '模擬測試完成';
    
  } catch (error) {
    console.error('❌ Webhook 模擬測試失敗:', error);
    logActivity('Webhook測試錯誤', error.toString());
    return `模擬測試失敗: ${error.message}`;
  }
}

// 🧪 測試函數：檢查部署狀態
function checkDeploymentStatus() {
  console.log('=== 檢查部署狀態 ===');
  
  try {
    // 獲取當前部署的 Web App URL
    const webAppUrl = ScriptApp.getService().getUrl();
    console.log('🌐 Web App URL:', webAppUrl);
    
    // 檢查 doPost 函數是否存在
    if (typeof doPost === 'function') {
      console.log('✅ doPost 函數存在');
    } else {
      console.log('❌ doPost 函數不存在');
    }
    
    // 檢查主要模組是否載入
    const modules = [
      'getConfig',
      'callGemini', 
      'replyMessage',
      'logActivity',
      'getModelApiVersion'
    ];
    
    console.log('🔍 檢查核心函數：');
    modules.forEach(funcName => {
      if (typeof this[funcName] === 'function') {
        console.log(`✅ ${funcName}`);
      } else {
        console.log(`❌ ${funcName} - 函數不存在`);
      }
    });
    
    return 'deployment check completed';
    
  } catch (error) {
    console.error('部署狀態檢查錯誤:', error);
    return `檢查失敗: ${error.message}`;
  }
}
