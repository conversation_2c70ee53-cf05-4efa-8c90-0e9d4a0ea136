// == LINE 語音按鈕測試指南 ==
// 🎵 測試修復後的語音播放按鈕功能

/**
 * 🎯 完整測試 LINE 語音按鈕修復
 * 這個函數會測試從 TTS 生成到 LINE 播放的完整流程
 */
function testLINEVoiceButtonFix_debug() {
  console.log('🎯 === 完整測試 LINE 語音按鈕修復 ===');
  console.log('🎵 目標：確保語音訊息在 LINE 中顯示播放按鈕而非下載連結');
  console.log('');
  
  try {
    // 1. 測試 Cloudinary 基礎功能
    console.log('1️⃣ 測試 Cloudinary 基礎功能...');
    const cloudinaryTest = quickTestNewCloudinaryFunctions_debug();
    
    if (!cloudinaryTest.success) {
      console.log('❌ Cloudinary 基礎功能測試失敗');
      console.log('💡 請先修復 Cloudinary 配置');
      return {
        success: false,
        step: 'cloudinary_basic',
        error: 'Cloudinary 配置問題'
      };
    }
    console.log('✅ Cloudinary 基礎功能正常');
    
    // 2. 測試 TTS 生成和上傳
    console.log('\n2️⃣ 測試 TTS 生成和 Cloudinary 上傳...');
    const ttsResult = textToSpeechWithGemini('這是測試語音按鈕的訊息');
    
    console.log('📊 TTS 測試結果:');
    console.log(`   TTS 生成: ${ttsResult.success ? '✅ 成功' : '❌ 失敗'}`);
    console.log(`   Cloudinary 上傳: ${ttsResult.isPlayable ? '✅ 成功' : '❌ 失敗'}`);
    console.log(`   檔案格式: ${ttsResult.mimeType || '未知'}`);
    
    if (!ttsResult.success) {
      console.log('❌ TTS 生成失敗');
      return {
        success: false,
        step: 'tts_generation',
        error: ttsResult.error
      };
    }
    
    if (!ttsResult.isPlayable) {
      console.log('⚠️ TTS 生成成功但 Cloudinary 上傳失敗');
      console.log(`   錯誤詳情: ${ttsResult.cloudinaryError}`);
      return {
        success: false,
        step: 'cloudinary_upload',
        error: ttsResult.cloudinaryError,
        fallbackUrl: ttsResult.driveUrl
      };
    }
    
    // 3. 驗證音頻 URL 格式
    console.log('\n3️⃣ 驗證音頻 URL 格式...');
    const audioUrl = ttsResult.cloudinaryUrl;
    console.log(`   音頻 URL: ${audioUrl}`);
    
    // 檢查 URL 是否符合 LINE 要求
    const isValidFormat = audioUrl.includes('.m4a') || audioUrl.includes('f_m4a');
    const isCloudinary = audioUrl.includes('cloudinary.com');
    const isSecure = audioUrl.startsWith('https://');
    
    console.log(`   M4A 格式: ${isValidFormat ? '✅' : '❌'}`);
    console.log(`   Cloudinary 來源: ${isCloudinary ? '✅' : '❌'}`);
    console.log(`   HTTPS 安全連結: ${isSecure ? '✅' : '❌'}`);
    
    const urlValid = isValidFormat && isCloudinary && isSecure;
    
    if (!urlValid) {
      console.log('⚠️ 音頻 URL 格式可能不符合 LINE 要求');
    }
    
    // 4. 生成 LINE 回覆訊息
    console.log('\n4️⃣ 生成 LINE 回覆訊息...');
    const lineResponse = generateAudioResponseForLINE(ttsResult);
    console.log('📱 LINE 回覆訊息預覽:');
    console.log(lineResponse.substring(0, 200) + '...');
    
    // 5. 總結測試結果
    console.log('\n📊 測試結果總結:');
    console.log(`   Cloudinary: ${cloudinaryTest.success ? '✅' : '❌'}`);
    console.log(`   TTS 生成: ${ttsResult.success ? '✅' : '❌'}`);
    console.log(`   音頻上傳: ${ttsResult.isPlayable ? '✅' : '❌'}`);
    console.log(`   URL 格式: ${urlValid ? '✅' : '⚠️'}`);
    
    const overallSuccess = cloudinaryTest.success && ttsResult.success && ttsResult.isPlayable && urlValid;
    
    if (overallSuccess) {
      console.log('\n🎉 所有測試通過！');
      console.log('📱 LINE 語音按鈕應該可以正常顯示');
      console.log('');
      console.log('🧪 接下來請在 LINE 中測試：');
      console.log('   1. 發送訊息：「你說 測試語音按鈕」');
      console.log('   2. 檢查回覆中是否有 🎵 播放按鈕');
      console.log('   3. 點擊按鈕確認能否播放');
      console.log('');
    } else {
      console.log('\n❌ 測試未完全通過');
      console.log('🔧 建議檢查上述失敗項目');
    }
    
    return {
      success: overallSuccess,
      results: {
        cloudinary: cloudinaryTest.success,
        tts: ttsResult.success,
        upload: ttsResult.isPlayable,
        urlFormat: urlValid
      },
      audioUrl: audioUrl,
      lineResponse: lineResponse,
      nextSteps: overallSuccess ? 
        ['在 LINE 中測試「你說 測試語音按鈕」', '確認播放按鈕顯示', '測試播放功能'] :
        ['修復上述失敗項目', '重新執行測試']
    };
    
  } catch (error) {
    console.error('❌ 測試過程發生錯誤:', error);
    return {
      success: false,
      step: 'test_process',
      error: error.message
    };
  }
}

/**
 * 📱 生成適用於 LINE 的音頻回覆訊息
 */
function generateAudioResponseForLINE(ttsResult) {
  if (!ttsResult.success) {
    return `❌ 語音生成失敗：${ttsResult.error}`;
  }
  
  let response = `🔊 語音轉換完成！\n\n📄 轉換文本：${ttsResult.text}\n🎵 音頻檔案：${ttsResult.fileName}`;
  
  if (ttsResult.isPlayable && ttsResult.cloudinaryUrl) {
    response += `\n✅ 可在 LINE 中播放（M4A 格式）`;
    response += `\n🎵 點擊下方按鈕播放`;
    response += `\n🌩️ 高品質音頻：${ttsResult.cloudinaryUrl}`;
    response += `\n💾 備用下載：${ttsResult.driveUrl}`;
  } else {
    response += `\n⚠️ Cloudinary 錯誤：${ttsResult.cloudinaryError || '未知錯誤'}`;
    response += `\n💾 可下載的 WAV 檔案：${ttsResult.driveUrl}`;
    response += `\n💡 音頻需要下載後播放`;
  }
  
  return response;
}

/**
 * 🎵 模擬 LINE 語音回覆（用於測試）
 * 這個函數模擬實際的 LINE 回覆流程
 */
function simulateLINEVoiceReply_debug(testText = '這是語音按鈕測試') {
  console.log('🎵 === 模擬 LINE 語音回覆 ===');
  console.log(`📝 測試文本: "${testText}"`);
  console.log('');
  
  try {
    // 1. 生成 TTS
    console.log('1️⃣ 生成 TTS...');
    const ttsResult = textToSpeechWithGemini(testText);
    
    // 2. 生成回覆訊息
    console.log('2️⃣ 生成回覆訊息...');
    const responseText = generateAudioResponseForLINE(ttsResult);
    
    // 3. 模擬 LINE 回覆
    console.log('3️⃣ 模擬 LINE 回覆...');
    console.log('📱 回覆訊息內容:');
    console.log('─'.repeat(50));
    console.log(responseText);
    console.log('─'.repeat(50));
    
    // 4. 判斷按鈕顯示情況
    console.log('\n4️⃣ 判斷按鈕顯示情況...');
    
    if (ttsResult.isPlayable && ttsResult.cloudinaryUrl) {
      console.log('✅ 預期結果：顯示 🎵 播放按鈕');
      console.log('🎯 用戶可以直接在 LINE 中播放音頻');
      console.log(`🔗 播放 URL: ${ttsResult.cloudinaryUrl}`);
    } else {
      console.log('❌ 預期結果：只顯示下載連結');
      console.log('📁 用戶需要下載後播放');
      console.log(`🔗 下載 URL: ${ttsResult.driveUrl}`);
      console.log(`⚠️ 問題原因: ${ttsResult.cloudinaryError || 'Cloudinary 上傳失敗'}`);
    }
    
    console.log('\n📊 模擬結果:');
    console.log(`   TTS 成功: ${ttsResult.success ? '✅' : '❌'}`);
    console.log(`   顯示播放按鈕: ${ttsResult.isPlayable ? '✅' : '❌'}`);
    console.log(`   音頻格式: ${ttsResult.mimeType || '未知'}`);
    
    return {
      success: ttsResult.success,
      hasPlayButton: ttsResult.isPlayable,
      responseText: responseText,
      audioUrl: ttsResult.cloudinaryUrl || ttsResult.driveUrl,
      ttsResult: ttsResult
    };
    
  } catch (error) {
    console.error('❌ 模擬失敗:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 🔧 語音按鈕問題診斷
 */
function diagnoseLINEVoiceButtonIssue_debug() {
  console.log('🔧 === 語音按鈕問題診斷 ===');
  console.log('');
  
  const issues = [];
  const solutions = [];
  
  // 1. 檢查 Cloudinary 配置
  console.log('1️⃣ 檢查 Cloudinary 配置...');
  const config = getConfig();
  
  if (!config.cloudinaryCloudName || !config.cloudinaryApiKey || !config.cloudinaryApiSecret) {
    issues.push('Cloudinary 配置不完整');
    solutions.push('在 APIKEY 工作表中設定完整的 Cloudinary 配置');
    console.log('❌ Cloudinary 配置不完整');
  } else {
    console.log('✅ Cloudinary 配置完整');
  }
  
  // 2. 測試 TTS 功能
  console.log('\n2️⃣ 測試 TTS 基礎功能...');
  try {
    const ttsTest = textToSpeechWithGemini('測試');
    if (!ttsTest.success) {
      issues.push('TTS 生成失敗');
      solutions.push('檢查 Gemini API 配置和網路連接');
      console.log(`❌ TTS 生成失敗: ${ttsTest.error}`);
    } else {
      console.log('✅ TTS 生成正常');
      
      if (!ttsTest.isPlayable) {
        issues.push('Cloudinary 上傳失敗');
        solutions.push('檢查 Cloudinary API 金鑰和網路連接');
        console.log(`❌ Cloudinary 上傳失敗: ${ttsTest.cloudinaryError}`);
      } else {
        console.log('✅ Cloudinary 上傳正常');
      }
    }
  } catch (error) {
    issues.push('TTS 測試過程出錯');
    solutions.push('檢查代碼完整性和依賴函數');
    console.log(`❌ TTS 測試出錯: ${error.message}`);
  }
  
  // 3. 總結診斷結果
  console.log('\n📊 診斷結果:');
  
  if (issues.length === 0) {
    console.log('🎉 未發現問題！語音按鈕應該正常顯示');
    console.log('💡 如果 LINE 中仍有問題，請檢查：');
    console.log('   • LINE Bot 權限設定');
    console.log('   • Webhook 是否正常運作');
    console.log('   • 網路連接狀況');
  } else {
    console.log(`❌ 發現 ${issues.length} 個問題:`);
    issues.forEach((issue, index) => {
      console.log(`   ${index + 1}. ${issue}`);
    });
    
    console.log('\n🔧 建議解決方案:');
    solutions.forEach((solution, index) => {
      console.log(`   ${index + 1}. ${solution}`);
    });
  }
  
  return {
    hasIssues: issues.length > 0,
    issues: issues,
    solutions: solutions
  };
}

/**
 * 📋 LINE 語音按鈕測試指南
 */
function showLINEVoiceButtonTestGuide_debug() {
  console.log('📋 === LINE 語音按鈕測試指南 ===');
  console.log('');
  console.log('🎯 目標：確保語音訊息在 LINE 中顯示播放按鈕');
  console.log('');
  console.log('📝 測試步驟：');
  console.log('');
  console.log('1️⃣ 執行技術測試：');
  console.log('   • 執行 testLINEVoiceButtonFix_debug()');
  console.log('   • 確認所有項目都是 ✅');
  console.log('');
  console.log('2️⃣ LINE 實際測試：');
  console.log('   • 在 LINE 中發送：「你說 測試語音按鈕」');
  console.log('   • 觀察回覆訊息');
  console.log('');
  console.log('3️⃣ 判斷結果：');
  console.log('');
  console.log('   ✅ 成功（顯示播放按鈕）：');
  console.log('      • 回覆中有 🎵 播放按鈕');
  console.log('      • 可以直接點擊播放');
  console.log('      • 音質清晰');
  console.log('');
  console.log('   ❌ 失敗（只有下載連結）：');
  console.log('      • 只有文字連結');
  console.log('      • 需要下載才能播放');
  console.log('      • 執行 diagnoseLINEVoiceButtonIssue_debug() 診斷');
  console.log('');
  console.log('4️⃣ 故障排除：');
  console.log('   • 檢查 Cloudinary 配置');
  console.log('   • 重新執行測試函數');
  console.log('   • 查看執行日誌');
  console.log('');
  console.log('💡 提示：如果技術測試都通過但 LINE 中仍有問題，');
  console.log('    可能是 LINE Bot 設定或網路問題。');
  console.log('');
}
