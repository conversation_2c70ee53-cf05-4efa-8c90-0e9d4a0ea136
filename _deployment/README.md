# 🔐 部署配置資料夾說明

## 📋 用途
此資料夾包含所有敏感的部署配置資訊，包括 API Keys、部署 ID 等。

## 🔒 安全性
- 此資料夾已被 `.gitignore` 排除，不會上傳到 GitHub
- 請勿將此資料夾內的檔案分享或公開

## 📁 檔案說明

### `deployment.json` (實際配置)
- 包含所有真實的 API Keys 和配置
- 用於系統自動恢復配置
- **絕對不要上傳到版本控制**

### `deployment.template.json` (範本)
- 提供配置結構參考
- 包含佔位符而非真實值
- 可以安全地分享給其他開發者

## 🔧 使用說明

### 初次設定
1. 複製 `deployment.template.json` 為 `deployment.json`
2. 填入真實的 API Keys 和配置值
3. 執行 `loadDeploymentConfig()` 函數載入配置

### 緊急恢復
當 Google Sheets 的 APIKEY 工作表數據丟失時：
1. 確認 `deployment.json` 中的配置正確
2. 執行 `emergencyRestoreFromDeploymentFile()` 函數

### 備份更新
定期執行 `updateDeploymentBackup()` 來同步最新配置到此檔案。

## ⚠️ 重要提醒
- 定期備份此資料夾到安全位置
- 不要在公開場所分享檔案內容
- 定期更換 API Keys 以確保安全性
