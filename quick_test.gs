// 🧪 快速測試修復後的意圖檢測

function quickTestIntentFix() {
  console.log('🧪 === 快速測試意圖檢測修復 ===');
  
  const testCases = [
    '你說 你好嗎',
    '能給我炒高麗菜的圖片嗎'
  ];
  
  testCases.forEach((testInput, index) => {
    console.log(`\n測試 ${index + 1}: "${testInput}"`);
    
    try {
      // 測試 AI 意圖分析
      const intent = analyzeUserIntentWithAI(testInput, 'group', 'test-user');
      console.log(`檢測到的意圖: ${intent.primary_intent}`);
      console.log(`信心度: ${intent.confidence}%`);
      console.log(`摘要: ${intent.natural_language_summary}`);
      
      // 檢查是否正確
      if (testInput.includes('你說') && intent.primary_intent === 'text_to_speech') {
        console.log('✅ 語音意圖檢測正確！');
      } else if (testInput.includes('圖片') && intent.primary_intent === 'image_generation') {
        console.log('✅ 圖片意圖檢測正確！');
      } else {
        console.log(`❌ 意圖檢測可能有問題，期望的意圖沒有被識別`);
      }
      
    } catch (error) {
      console.error(`測試 ${index + 1} 失敗:`, error);
    }
  });
  
  console.log('\n🎉 快速測試完成！');
}

function testFullFlow() {
  console.log('🧪 === 測試完整流程 ===');
  
  try {
    // 測試語音功能
    console.log('\n🔊 測試語音功能...');
    const ttsResponse = handleTextMessageAIFirst('你說 你好嗎', null, 'test-user', 'group');
    console.log('語音回應類型:', typeof ttsResponse);
    console.log('語音回應內容:', ttsResponse);
    
    // 測試圖片功能
    console.log('\n🎨 測試圖片功能...');
    const imageResponse = handleTextMessageAIFirst('能給我炒高麗菜的圖片嗎', null, 'test-user', 'group');
    console.log('圖片回應類型:', typeof imageResponse);
    console.log('圖片回應內容:', imageResponse);
    
  } catch (error) {
    console.error('完整流程測試失敗:', error);
  }
}
