// == Cloudinary 通用操作模組（修復版）==
// 🌩️ 基於官方範例改寫的 Google Apps Script 版本
// 🔧 修復簽名計算問題：嚴格按照官方文檔排除不必要參數

/**
 * 🌩️ Cloudinary 通用上傳函數（修復版）
 * 修復簽名計算，只包含必要參數
 */
function cloudinaryUpload(fileBlob, options = {}) {
  try {
    const config = getConfig();
    
    // 檢查配置
    if (!config.cloudinaryCloudName || !config.cloudinaryApiKey || !config.cloudinaryApiSecret) {
      throw new Error('請在 APIKEY 工作表中設定完整的 Cloudinary 配置');
    }
    
    console.log(`🌩️ Cloudinary 上傳開始...`);
    
    // 預設選項
    const defaultOptions = {
      resource_type: 'auto',  // auto, image, video, raw
      public_id: `linebot/${new Date().getTime()}`
    };
    
    // 合併選項
    const uploadOptions = { ...defaultOptions, ...options };
    
    // 準備核心上傳參數（只有這些參與簽名計算）
    const coreParams = {
      public_id: uploadOptions.public_id,
      timestamp: Math.floor(Date.now() / 1000)
    };
    
    // 只有當明確指定時才添加到簽名參數中
    if (uploadOptions.format) {
      coreParams.format = uploadOptions.format;
    }
    
    console.log(`📋 核心簽名參數:`, coreParams);
    
    // 🔧 生成簽名（只使用核心參數）
    const signature = generateCloudinarySignatureFixed(coreParams, config.cloudinaryApiSecret);
    
    // 準備表單數據（包含更多參數，但簽名只基於核心參數）
    const formData = {
      file: fileBlob,
      public_id: uploadOptions.public_id,
      api_key: config.cloudinaryApiKey,
      timestamp: String(coreParams.timestamp),
      signature: signature
    };
    
    // 添加其他表單參數（這些不參與簽名計算）
    if (uploadOptions.format) formData.format = uploadOptions.format;
    if (uploadOptions.folder) formData.folder = uploadOptions.folder;
    if (uploadOptions.overwrite) formData.overwrite = uploadOptions.overwrite;
    if (uploadOptions.quality) formData.quality = uploadOptions.quality;
    
    // 確定上傳 URL 和資源類型
    let resourceType = uploadOptions.resource_type;
    if (resourceType === 'auto') {
      // 根據 MIME 類型自動判斷
      const mimeType = fileBlob.getContentType();
      if (mimeType.startsWith('image/')) resourceType = 'image';
      else if (mimeType.startsWith('video/') || mimeType.startsWith('audio/')) resourceType = 'video';
      else resourceType = 'raw';
    }
    
    const uploadUrl = `https://api.cloudinary.com/v1_1/${config.cloudinaryCloudName}/${resourceType}/upload`;
    
    console.log(`📤 上傳到: ${uploadUrl}`);
    console.log(`🎯 資源類型: ${resourceType}`);
    
    // 執行上傳
    const response = UrlFetchApp.fetch(uploadUrl, {
      method: 'POST',
      payload: formData,
      muteHttpExceptions: true
    });
    
    const responseCode = response.getResponseCode();
    const responseText = response.getContentText();
    
    console.log(`📊 回應代碼: ${responseCode}`);
    
    if (responseCode !== 200) {
      console.log(`❌ 上傳失敗詳情: ${responseText}`);
      throw new Error(`Cloudinary 上傳失敗: ${responseCode} - ${responseText}`);
    }
    
    const result = JSON.parse(responseText);
    
    if (!result.secure_url) {
      throw new Error('Cloudinary 未返回有效的 URL');
    }
    
    console.log(`✅ 上傳成功: ${result.secure_url}`);
    
    // 記錄活動
    logActivity('Cloudinary上傳', `公開ID: ${result.public_id}, URL: ${result.secure_url}, 格式: ${result.format}`);
    
    return {
      success: true,
      public_id: result.public_id,
      version: result.version,
      signature: result.signature,
      width: result.width,
      height: result.height,
      format: result.format,
      resource_type: result.resource_type,
      created_at: result.created_at,
      tags: result.tags || [],
      bytes: result.bytes,
      type: result.type,
      etag: result.etag,
      placeholder: result.placeholder,
      url: result.url,
      secure_url: result.secure_url,
      original_filename: result.original_filename
    };
    
  } catch (error) {
    console.error('❌ Cloudinary 上傳錯誤:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 🔐 修復的 Cloudinary 簽名生成函數
 * 嚴格按照官方文檔，只包含必要參數
 */
function generateCloudinarySignatureFixed(params, apiSecret) {
  try {
    // 排除不應該參與簽名的參數
    const excludeParams = ['file', 'cloud_name', 'resource_type', 'api_key', 'folder', 'overwrite', 'quality', 'fetch_format'];
    
    // 只包含應該參與簽名的參數
    const sortedParams = Object.keys(params)
      .filter(key => !excludeParams.includes(key))
      .sort()
      .map(key => `${key}=${String(params[key])}`)
      .join('&');
    
    const stringToSign = sortedParams + apiSecret;
    
    console.log(`🔐 修復的簽名字符串: ${stringToSign}`);
    
    // 使用 SHA-1 生成簽名
    return Utilities.computeDigest(Utilities.DigestAlgorithm.SHA_1, stringToSign)
      .map(byte => ('0' + (byte & 0xFF).toString(16)).slice(-2))
      .join('');
      
  } catch (error) {
    console.error('❌ 簽名生成錯誤:', error);
    throw error;
  }
}

/**
 * 🎨 生成優化的 Cloudinary URL
 * 等同於原範例的 cloudinary.url() 函數
 */
function cloudinaryOptimizeUrl(publicId, options = {}) {
  try {
    const config = getConfig();
    
    if (!config.cloudinaryCloudName) {
      throw new Error('請設定 Cloudinary Cloud Name');
    }
    
    // 預設優化選項
    const defaultOptions = {
      fetch_format: 'auto',
      quality: 'auto',
      secure: true
    };
    
    const urlOptions = { ...defaultOptions, ...options };
    
    // 構建轉換參數
    const transformations = [];
    
    // 品質和格式優化
    if (urlOptions.quality) transformations.push(`q_${urlOptions.quality}`);
    if (urlOptions.fetch_format) transformations.push(`f_${urlOptions.fetch_format}`);
    
    // 尺寸調整
    if (urlOptions.width) transformations.push(`w_${urlOptions.width}`);
    if (urlOptions.height) transformations.push(`h_${urlOptions.height}`);
    
    // 裁切選項
    if (urlOptions.crop) transformations.push(`c_${urlOptions.crop}`);
    if (urlOptions.gravity) transformations.push(`g_${urlOptions.gravity}`);
    
    // 效果
    if (urlOptions.effect) transformations.push(`e_${urlOptions.effect}`);
    if (urlOptions.radius) transformations.push(`r_${urlOptions.radius}`);
    
    // 角度旋轉
    if (urlOptions.angle) transformations.push(`a_${urlOptions.angle}`);
    
    // 邊框
    if (urlOptions.border) transformations.push(`bo_${urlOptions.border}`);
    
    // 構建完整 URL
    const protocol = urlOptions.secure ? 'https' : 'http';
    const baseUrl = `${protocol}://res.cloudinary.com/${config.cloudinaryCloudName}`;
    
    let url = `${baseUrl}/image/upload`;
    
    if (transformations.length > 0) {
      url += `/${transformations.join(',')}`;
    }
    
    url += `/${publicId}`;
    
    console.log(`🎨 生成優化 URL: ${url}`);
    
    return url;
    
  } catch (error) {
    console.error('❌ URL 生成錯誤:', error);
    return null;
  }
}

/**
 * 🔄 生成自動裁切的方形圖片 URL
 * 等同於原範例的自動裁切功能
 */
function cloudinaryAutoCropUrl(publicId, size = 500) {
  return cloudinaryOptimizeUrl(publicId, {
    crop: 'auto',
    gravity: 'auto',
    width: size,
    height: size,
    fetch_format: 'auto',
    quality: 'auto'
  });
}

/**
 * 🎵 上傳音頻到 Cloudinary（使用修復的通用函數）
 */
function uploadAudioToCloudinaryV2(audioBlob, options = {}) {
  const defaultOptions = {
    format: 'm4a',
    public_id: `linebot/tts/audio_${new Date().getTime()}`
  };
  
  return cloudinaryUpload(audioBlob, { ...defaultOptions, ...options });
}

/**
 * 🖼️ 上傳圖片到 Cloudinary（使用修復的通用函數）
 */
function uploadImageToCloudinaryV2(imageBlob, options = {}) {
  const defaultOptions = {
    public_id: `linebot/images/img_${new Date().getTime()}`
  };
  
  return cloudinaryUpload(imageBlob, { ...defaultOptions, ...options });
}

/**
 * 🧪 測試修復的 Cloudinary 簽名
 */
function testFixedCloudinarySignature_debug() {
  console.log('🧪 === 測試修復的 Cloudinary 簽名 ===');
  
  try {
    const config = getConfig();
    
    console.log('1️⃣ 測試簽名生成...');
    
    // 測試參數（只包含核心參數）
    const testParams = {
      public_id: 'linebot/test',
      timestamp: 1315060510,
      format: 'm4a'
    };
    
    console.log('   測試參數:', testParams);
    
    const signature = generateCloudinarySignatureFixed(testParams, config.cloudinaryApiSecret);
    console.log(`   生成簽名: ${signature}`);
    
    // 手動驗證簽名字符串
    const expectedString = `format=m4a&public_id=linebot/test&timestamp=1315060510${config.cloudinaryApiSecret}`;
    console.log(`   預期字符串: ${expectedString}`);
    
    console.log('✅ 簽名生成測試完成');
    
    // 測試實際上傳
    console.log('\n2️⃣ 測試實際音頻上傳...');
    const testAudioData = new ArrayBuffer(1000);
    const testAudioBlob = Utilities.newBlob(new Uint8Array(testAudioData), 'audio/wav', 'test_audio.wav');
    
    const audioResult = uploadAudioToCloudinaryV2(testAudioBlob);
    console.log(`   音頻上傳: ${audioResult.success ? '✅ 成功' : '❌ 失敗'}`);
    
    if (audioResult.success) {
      console.log(`   音頻 URL: ${audioResult.secure_url}`);
      console.log(`   格式: ${audioResult.format}`);
    } else {
      console.log(`   錯誤: ${audioResult.error}`);
    }
    
    // 測試圖片上傳
    console.log('\n3️⃣ 測試實際圖片上傳...');
    const testImageData = new ArrayBuffer(1000);
    const testImageBlob = Utilities.newBlob(new Uint8Array(testImageData), 'image/png', 'test_image.png');
    
    const imageResult = uploadImageToCloudinaryV2(testImageBlob);
    console.log(`   圖片上傳: ${imageResult.success ? '✅ 成功' : '❌ 失敗'}`);
    
    if (imageResult.success) {
      console.log(`   圖片 URL: ${imageResult.secure_url}`);
      console.log(`   格式: ${imageResult.format}`);
    } else {
      console.log(`   錯誤: ${imageResult.error}`);
    }
    
    // 總結
    console.log('\n📊 修復測試總結:');
    console.log(`   簽名生成: ✅ 修復`);
    console.log(`   音頻上傳: ${audioResult.success ? '✅' : '❌'}`);
    console.log(`   圖片上傳: ${imageResult.success ? '✅' : '❌'}`);
    
    const overallSuccess = audioResult.success && imageResult.success;
    
    if (overallSuccess) {
      console.log('\n🎉 所有修復測試通過！');
      console.log('💡 現在可以正常使用 Cloudinary 功能');
    } else {
      console.log('\n⚠️ 仍有問題需要解決');
    }
    
    return {
      success: overallSuccess,
      audioResult: audioResult,
      imageResult: imageResult
    };
    
  } catch (error) {
    console.error('❌ 修復測試失敗:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 🔧 回滾到原始的簡單上傳方式
 * 如果通用函數仍有問題，使用這個最簡單的版本
 */
function simpleCloudinaryUpload_debug(fileBlob, publicId, resourceType = 'auto', format = null) {
  console.log('🔧 === 使用最簡單的 Cloudinary 上傳 ===');
  
  try {
    const config = getConfig();
    
    // 最基本的參數
    const uploadParams = {
      public_id: publicId,
      timestamp: Math.floor(Date.now() / 1000)
    };
    
    // 只在指定時添加格式
    if (format) {
      uploadParams.format = format;
    }
    
    console.log('📋 最簡參數:', uploadParams);
    
    // 使用修復的簽名函數
    const signature = generateCloudinarySignatureFixed(uploadParams, config.cloudinaryApiSecret);
    
    // 最簡表單數據
    const formData = {
      file: fileBlob,
      public_id: publicId,
      api_key: config.cloudinaryApiKey,
      timestamp: String(uploadParams.timestamp),
      signature: signature
    };
    
    if (format) formData.format = format;
    
    // 確定資源類型
    let actualResourceType = resourceType;
    if (resourceType === 'auto') {
      const mimeType = fileBlob.getContentType();
      if (mimeType.startsWith('image/')) actualResourceType = 'image';
      else if (mimeType.startsWith('video/') || mimeType.startsWith('audio/')) actualResourceType = 'video';
      else actualResourceType = 'raw';
    }
    
    const uploadUrl = `https://api.cloudinary.com/v1_1/${config.cloudinaryCloudName}/${actualResourceType}/upload`;
    
    console.log(`📤 上傳到: ${uploadUrl}`);
    
    const response = UrlFetchApp.fetch(uploadUrl, {
      method: 'POST',
      payload: formData,
      muteHttpExceptions: true
    });
    
    const responseCode = response.getResponseCode();
    const responseText = response.getContentText();
    
    console.log(`📊 回應代碼: ${responseCode}`);
    
    if (responseCode === 200) {
      const result = JSON.parse(responseText);
      console.log(`✅ 簡單上傳成功: ${result.secure_url}`);
      return result;
    } else {
      console.log(`❌ 簡單上傳失敗: ${responseText}`);
      throw new Error(`上傳失敗: ${responseCode} - ${responseText}`);
    }
    
  } catch (error) {
    console.error('❌ 簡單上傳錯誤:', error);
    throw error;
  }
}
