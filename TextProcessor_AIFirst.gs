// == AI-First 文字處理模組 - 核心流程 ==
// 🤖 完全基於 Gemini AI 的意圖理解和智能路由系統
// 🔧 v1.3.1 - 重構優化版：移除重複代碼，專注核心流程
// 📁 本檔案職責：主處理流程、雙重檢測機制、智能路由

// 🛡️ 明確系統命令清單（預檢測用）
const EXPLICIT_SYSTEM_COMMANDS = {
  // 測試相關
  'test': 'system_test',
  'testing': 'system_test', 
  '測試': 'system_test',
  
  // 幫助相關
  'help': 'system_help',
  '幫助': 'system_help',
  '功能': 'system_help',
  
  // 狀態相關
  'status': 'system_status',
  '狀態': 'system_status',
  
  // 範例相關
  'examples': 'model_examples',
  'example': 'model_examples',
  '範例': 'model_examples',
  '示範': 'model_examples'
};

/**
 * 🛡️ 預檢測系統命令函數
 * 在 AI 分析前先檢查明確的系統命令，確保 100% 正確執行
 */
function preDetectSystemCommand(userInput) {
  const cleanInput = userInput.replace(/^[!！]/, '').trim().toLowerCase();
  
  // 完全匹配檢測
  if (EXPLICIT_SYSTEM_COMMANDS[cleanInput]) {
    return {
      isSystemCommand: true,
      commandType: EXPLICIT_SYSTEM_COMMANDS[cleanInput],
      confidence: 100
    };
  }
  
  return {
    isSystemCommand: false,
    confidence: 0
  };
}

/**
 * 🔧 直接系統命令處理函數
 * 繞過 AI 分析，直接執行系統命令
 */
function handleSystemCommandDirect(commandType, sourceType, replyToken) {
  let response;
  
  switch (commandType) {
    case 'system_test':
      response = generateTestResponse(sourceType);
      break;
    case 'system_help':
      response = generateEnhancedHelpMessage(sourceType);
      break;
    case 'system_status':
      response = generateSystemStatusResponse();
      break;
    case 'model_examples':
      response = generateModelExamplesResponse({}, sourceType);
      break;
    default:
      response = generateEnhancedHelpMessage(sourceType);
  }
  
  if (replyToken) {
    replyMessage(replyToken, response);
    logActivity('系統命令直接處理', `命令: ${commandType}`);
  }
  
  return response;
}

/**
 * 🤔 智能引導觸發檢查
 * 當 AI 信心度過低時觸發統一引導
 */
function shouldTriggerGuidance(aiIntent) {
  return aiIntent.confidence < 70;
}

/**
 * 🧠 AI-First 文字訊息處理主函數
 * 完全依賴 Gemini AI 理解用戶意圖，不使用命令匹配
 * 🔧 v1.3.1 - 重構優化版：核心流程，通用函數統一管理
 */
function handleTextMessageAIFirst(text, replyToken, userId, sourceType) {
  try {
    console.log(`📝 收到文字訊息: ${text} (用戶: ${userId}, 來源: ${sourceType})`);
    
    // 1. 移除群組前綴但保留原始意圖
    const cleanText = text.startsWith('!') || text.startsWith('！') ? text.slice(1).trim() : text.trim();
    
    // 🛡️ 預檢測層 - 明確系統命令直接處理
    const preCheck = preDetectSystemCommand(cleanText);
    if (preCheck.isSystemCommand) {
      console.log(`🔧 預檢測命中系統命令: ${preCheck.commandType} (信心度: ${preCheck.confidence}%)`);
      return handleSystemCommandDirect(preCheck.commandType, sourceType, replyToken);
    }
    
    // 2. 🤖 核心：使用 AI 理解用戶意圖
    const userIntent = analyzeUserIntentWithAI(cleanText, sourceType, userId);
    
    // 3. 基於 AI 理解的意圖進行智能路由
    const response = routeByAIIntent(userIntent, cleanText, userId, sourceType);
    
    // 4. 🎯 AI-First 智能回覆處理
    if (typeof response === 'object' && response.type) {
      // 處理特殊回覆類型（音頻、圖片等） - 調用通用函數
      handleSpecialResponse(response, replyToken, text, userId, sourceType, userIntent);
      return response;
    } else {
      // 處理一般文字回覆
      const responseText = response;

      // 記錄對話到記憶系統 - 調用通用函數
      recordConversationSafely(userId, cleanText, responseText, sourceType, userIntent);

      // 回覆用戶
      if (replyToken) {
        replyMessage(replyToken, responseText);
        logActivity('AI智能回覆', `意圖: ${userIntent.primary_intent} (信心度: ${userIntent.confidence}%)`);
      }

      return responseText;
    }
    
  } catch (error) {
    console.error('AI-First 文字處理錯誤:', error);
    // 調用通用錯誤處理函數
    return handleAIProcessingError(text, replyToken, userId, sourceType, error);
  }
}

/**
 * 🧠 核心功能：使用 Gemini AI 分析用戶意圖
 * 🔧 統一提示詞版，強化群組成員查詢識別
 */
function analyzeUserIntentWithAI(userMessage, sourceType, userId) {
  try {
    const config = getConfig();

    if (!config.geminiApiKey) {
      throw new Error('需要 Gemini API Key 才能使用 AI-First 模式');
    }

    // 🎯 使用統一提示詞系統進行意圖分析
    const aiResponse = callAIWithPrompt('INTENT_ANALYSIS', {
      userMessage: userMessage,
      sourceType: sourceType === 'group' ? '群組聊天' : sourceType === 'room' ? '聊天室' : '個人對話'
    });
    
    // 解析 AI 回應
    try {
      const jsonMatch = aiResponse.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const intentData = JSON.parse(jsonMatch[0]);
        
        // 驗證和補充數據
        intentData.original_message = userMessage;
        intentData.source_type = sourceType;
        intentData.analysis_timestamp = new Date().toISOString();
        
        console.log('🧠 AI 意圖分析結果:', intentData);
        return intentData;
      }
    } catch (parseError) {
      console.error('解析 AI 意圖回應失敗:', parseError);
    }
    
    // AI 回應解析失敗時的備用分析
    return generateFallbackIntent(userMessage, sourceType);
    
  } catch (error) {
    console.error('AI 意圖分析失敗:', error);
    return generateFallbackIntent(userMessage, sourceType);
  }
}

/**
 * 🎯 基於 AI 意圖的智能路由系統
 * 完全取代硬編碼的 if-else 命令匹配
 * 🔧 v1.3.1 - 重構優化版：委託具體處理給專門函數
 */
function routeByAIIntent(intent, originalMessage, userId, sourceType) {
  try {
    console.log(`🎯 路由到意圖: ${intent.primary_intent} (信心度: ${intent.confidence}%)`);
    
    // 🚀 智能引導檢查 - 當 AI 信心度過低時提供統一引導
    if (shouldTriggerGuidance(intent)) {
      console.log(`🤔 觸發智能引導 (信心度: ${intent.confidence}%)`);
      return callAIWithPrompt('UNIFIED_GUIDANCE', {});
    }
    
    // 🔗 委託給專門處理函數 (在 AIHandlers_Specialized.gs 中)
    switch (intent.primary_intent) {
      case 'drive_link_sharing':
        return handleGoogleDriveLink(originalMessage, userId, sourceType);
        
      case 'note_taking':
        return handleAINoteRequest(intent, originalMessage, userId, sourceType);
        
      case 'file_query':
        return handleAIFileQuery(intent, originalMessage, userId, sourceType);
        
      case 'conversation_review':
        return handleAIConversationReview(intent, userId, sourceType);
        
      case 'group_member_query':
        if (sourceType === 'group' || sourceType === 'room') {
          return handleAIGroupMemberQuery(intent, originalMessage, userId, sourceType);
        } else {
          return generateAIResponse(intent, '這個功能只能在群組中使用。您想要查看我們的對話記錄嗎？');
        }
        
      case 'social_media_post':
        return handleAISocialMediaPost(intent, originalMessage, userId, sourceType);

      case 'text_to_speech':
        return handleAITTSRequest(intent, originalMessage, userId, sourceType);

      case 'conversational_audio':
      case 'voice_chat':
        return handleAIConversationalAudio(intent, originalMessage, userId, sourceType);

      case 'image_generation':
        return handleAIImageGeneration(intent, originalMessage, userId, sourceType);

      case 'system_help':
        return generateAIHelpResponse(intent, sourceType);

      case 'system_command':
        return handleAISystemCommand(intent, originalMessage, sourceType);

      case 'model_examples':
        return generateModelExamplesResponse(intent, sourceType);

      case 'general_question':
        // 🔧 修復：檢查是否為被錯誤分類的語音聊天請求
        if (/跟我聊|聊聊|聊天|對話|語音聊天|語音對話|用語音|語音回應/i.test(originalMessage)) {
          console.log(`🔧 修復：將 general_question 重新路由到語音對話`);
          return handleAIConversationalAudio(intent, originalMessage, userId, sourceType);
        }
        return handleAIGeneralQuestion(intent, originalMessage, sourceType);

      case 'casual_chat':
        // 🔧 修復：檢查是否為被錯誤分類的語音聊天請求
        if (/跟我聊|聊聊|聊天|對話|語音聊天|語音對話|用語音|語音回應/i.test(originalMessage)) {
          console.log(`🔧 修復：將 casual_chat 重新路由到語音對話`);
          return handleAIConversationalAudio(intent, originalMessage, userId, sourceType);
        }
        return generateAICasualResponse(intent, originalMessage);

      default:
        // 🤖 如果不確定意圖，讓 AI 自主決定如何回應
        return handleAIUncertainIntent(intent, originalMessage, sourceType);
    }
    
  } catch (error) {
    console.error('AI 路由錯誤:', error);
    return generateAIErrorResponse(intent, originalMessage, error);
  }
}

/**
 * 🛡️ 備用意圖生成（當 AI 分析失敗時）
 * 🔧 強化群組查詢檢測
 */
function generateFallbackIntent(message, sourceType) {
  // 簡單的關鍵字備用檢測
  const intent = {
    primary_intent: 'general_question',
    confidence: 30,
    key_entities: [],
    natural_language_summary: '用戶發送了訊息，需要進一步理解意圖',
    suggested_action: '提供通用回應並詢問具體需求',
    original_message: message,
    source_type: sourceType,
    fallback_mode: true
  };
  
  // 🔧 強化的意圖推測，特別是群組成員查詢
  if (message.includes('drive.google.com') || message.includes('docs.google.com')) {
    intent.primary_intent = 'drive_link_sharing';
    intent.confidence = 90;
  } else if (/記錄|記下|記住|筆記|note/i.test(message)) {
    intent.primary_intent = 'note_taking';
    intent.confidence = 70;
  } else if ((sourceType === 'group' || sourceType === 'room') && 
             (/把群[裡裏](.+?)的對話|(.+?)都講了|(.+?)說了什麼|總結(.+?)的|分析(.+?)的發言/i.test(message))) {
    // 🎯 強化群組成員查詢檢測
    intent.primary_intent = 'group_member_query';
    intent.confidence = 85;
    
    // 嘗試提取用戶名稱
    const matches = message.match(/把群[裡裏](.+?)的對話|(.+?)都講了|(.+?)說了什麼|總結(.+?)的|分析(.+?)的發言/i);
    if (matches) {
      const extractedName = matches.find((match, index) => index > 0 && match);
      if (extractedName) {
        intent.key_entities = [extractedName.trim()];
        intent.natural_language_summary = `用戶想要查詢群組成員「${extractedName.trim()}」的發言記錄`;
      }
    }
  } else if (/聊了什麼|對話|總結|回顧/i.test(message) && !(sourceType === 'group' && /(.+?)的/.test(message))) {
    intent.primary_intent = 'conversation_review';
    intent.confidence = 70;
  } else if (/幫助|help|怎麼用|功能/i.test(message)) {
    intent.primary_intent = 'system_help';
    intent.confidence = 80;
  } else if (/^!?測試$|^!?test$|^!?狀態$|^!?status$/i.test(message.trim())) {
    // 🔧 系統指令檢測
    intent.primary_intent = 'system_command';
    intent.confidence = 95;
    intent.natural_language_summary = '用戶想要執行系統指令';
  } else if (/範例|example|使用方法|怎麼用.*功能/i.test(message)) {
    // 📚 模型範例請求檢測
    intent.primary_intent = 'model_examples';
    intent.confidence = 85;
    intent.natural_language_summary = '用戶想要了解功能使用範例';
  } else if (/你說|你念|念出來|讀出來|語音|播放|tts|說出來|跟我聊|聊聊|聊天|對話|語音聊天|語音對話/i.test(message)) {
    // 🔊 語音 TTS 意圖檢測（包含對話模式）
    intent.primary_intent = 'text_to_speech';
    intent.confidence = 85;
    intent.natural_language_summary = '用戶想要語音功能（可能是複述或對話）';
  } else if (/畫|生成圖|創建圖|圖像|畫圖|圖片|generate image|draw|create image|給我.*圖/i.test(message)) {
    // 🎨 圖像生成意圖檢測
    intent.primary_intent = 'image_generation';
    intent.confidence = 85;
    intent.natural_language_summary = '用戶想要生成圖像';
  }
  
  console.log('🛡️ 備用意圖檢測結果:', intent);
  return intent;
}

// 🧪 測試核心流程 - 精簡版
function testAIFirstCore_debug() {
  console.log('=== 測試 AI-First 核心流程 v1.3.1 ===');
  
  const testMessages = [
    '測試',                      // 應該被預檢測捕獲
    'help',                     // 應該被預檢測捕獲  
    '範例',                     // 應該被預檢測捕獲
    '幫我記住今天要買牛奶',         // AI 分析
    '嗯'                        // 應該觸發智能引導
  ];
  
  testMessages.forEach((message, index) => {
    try {
      console.log(`\n測試 ${index + 1}: "${message}"`);
      
      // 測試預檢測
      const preCheck = preDetectSystemCommand(message);
      if (preCheck.isSystemCommand) {
        console.log(`🛡️ 預檢測捕獲: ${preCheck.commandType} (100%)`);
      } else {
        const intent = analyzeUserIntentWithAI(message, 'group', 'test-user');
        console.log(`🧠 AI分析: ${intent.primary_intent} (${intent.confidence}%)`);
        
        if (shouldTriggerGuidance(intent)) {
          console.log(`🤔 會觸發智能引導 (信心度過低)`);
        }
      }
      
    } catch (error) {
      console.log(`測試 ${index + 1} 失敗: ${error.message}`);
    }
  });
  
  console.log('\n✅ AI-First 核心流程 v1.3.1 測試完成');
}

/**
 * 📋 核心流程檔案說明 v1.3.1
 * 
 * 🎯 本檔案職責：
 * - 雙重檢測機制 (預檢測 + AI分析)
 * - 主處理流程 (handleTextMessageAIFirst)
 * - 智能路由系統 (routeByAIIntent)
 * - 意圖分析和備用檢測
 * 
 * 🔗 配合檔案：
 * - AIHandlers_Specialized.gs (具體處理函數)
 * - helper_functions_v13.gs (通用輔助函數)
 * - PromptManager.gs (提示詞管理)
 * 
 * 📏 檔案大小：約 12KB (重構後減少 40%)
 * 🎉 v1.3.1 改進：移除重複代碼，專注核心流程
 */
