// == Cloudinary 簽名修復測試 ==
// 🔧 測試修復後的 Cloudinary 簽名功能

/**
 * 🧪 測試修復後的 Cloudinary 簽名生成
 */
function testCloudinarySignatureFix_debug() {
  console.log('🧪 === 測試 Cloudinary 簽名修復 ===');
  
  try {
    const config = getConfig();
    
    // 1. 檢查配置
    console.log('1️⃣ 檢查 Cloudinary 配置...');
    console.log(`   Cloud Name: "${config.cloudinaryCloudName}"`);
    console.log(`   API Key: "${config.cloudinaryApiKey}"`);
    console.log(`   API Secret: ${config.cloudinaryApiSecret ? '***已設定***' : '未設定'}`);
    
    if (!config.cloudinaryCloudName || !config.cloudinaryApiKey || !config.cloudinaryApiSecret) {
      console.log('❌ Cloudinary 配置不完整');
      return { success: false, error: 'Cloudinary 配置不完整' };
    }
    
    // 2. 測試簽名生成（模擬大 timestamp）
    console.log('\n2️⃣ 測試簽名生成...');
    
    const testTimestamp = Math.floor(Date.now() / 1000); // 當前時間戳
    const largeTimestamp = 1751067835; // 可能導致科學記數法的大數字
    
    console.log(`   當前 timestamp: ${testTimestamp}`);
    console.log(`   測試 timestamp: ${largeTimestamp}`);
    
    // 測試參數
    const testParams = {
      public_id: 'test/signature_fix',
      resource_type: 'video',
      format: 'm4a',
      timestamp: largeTimestamp
    };
    
    console.log('   測試參數:', testParams);
    
    // 生成簽名
    const signature = generateCloudinarySignature(testParams, config.cloudinaryApiSecret);
    console.log(`   生成的簽名: ${signature.substring(0, 20)}...`);
    
    // 3. 測試實際上傳（小檔案）
    console.log('\n3️⃣ 測試實際上傳...');
    
    // 創建測試音頻檔案
    const testAudioData = new ArrayBuffer(1000);
    const testBlob = Utilities.newBlob(new Uint8Array(testAudioData), 'audio/wav', 'test_audio.wav');
    
    console.log(`   測試檔案大小: ${testBlob.getBytes().length} bytes`);
    
    try {
      const uploadResult = uploadAudioToCloudinary(testBlob, '測試修復');
      console.log('✅ Cloudinary 上傳成功！');
      console.log(`   上傳 URL: ${uploadResult}`);
      
      return {
        success: true,
        signature: signature,
        uploadUrl: uploadResult,
        message: 'Cloudinary 簽名修復成功'
      };
      
    } catch (uploadError) {
      console.log('❌ Cloudinary 上傳失敗');
      console.log(`   錯誤: ${uploadError.message}`);
      
      // 檢查是否還是簽名問題
      if (uploadError.message.includes('Invalid Signature')) {
        console.log('⚠️ 仍然是簽名問題，需要進一步調查');
        return {
          success: false,
          error: '簽名問題未完全解決',
          details: uploadError.message
        };
      } else {
        console.log('💡 可能是其他問題（如配置錯誤、網路問題等）');
        return {
          success: false,
          error: '非簽名問題',
          details: uploadError.message
        };
      }
    }
    
  } catch (error) {
    console.error('❌ 測試失敗:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 🔧 測試簽名字符串生成細節
 */
function debugSignatureGeneration_debug() {
  console.log('🔧 === 調試簽名生成細節 ===');
  
  try {
    const config = getConfig();
    
    // 模擬大 timestamp
    const problematicTimestamp = 1751067835015; // 這種大數字可能被轉換為科學記數法
    const normalTimestamp = Math.floor(Date.now() / 1000);
    
    console.log('📋 測試不同 timestamp 格式:');
    console.log(`   問題 timestamp: ${problematicTimestamp}`);
    console.log(`   正常 timestamp: ${normalTimestamp}`);
    console.log(`   String(問題): ${String(problematicTimestamp)}`);
    console.log(`   String(正常): ${String(normalTimestamp)}`);
    
    // 測試參數
    const params1 = {
      public_id: 'test/debug',
      resource_type: 'video',
      format: 'm4a',
      timestamp: problematicTimestamp
    };
    
    const params2 = {
      public_id: 'test/debug',
      resource_type: 'video',
      format: 'm4a',
      timestamp: normalTimestamp
    };
    
    console.log('\n🔐 生成簽名比較:');
    
    // 生成簽名1（大數字）
    const signature1 = generateCloudinarySignature(params1, config.cloudinaryApiSecret);
    console.log(`   大數字簽名: ${signature1.substring(0, 20)}...`);
    
    // 生成簽名2（正常數字）
    const signature2 = generateCloudinarySignature(params2, config.cloudinaryApiSecret);
    console.log(`   正常簽名: ${signature2.substring(0, 20)}...`);
    
    // 手動構建簽名字符串來驗證
    console.log('\n🔍 手動驗證簽名字符串:');
    
    const manualParams1 = Object.keys(params1)
      .filter(key => key !== 'file' && key !== 'api_key')
      .sort()
      .map(key => `${key}=${String(params1[key])}`)
      .join('&');
    
    console.log(`   手動構建1: ${manualParams1}`);
    
    const manualParams2 = Object.keys(params2)
      .filter(key => key !== 'file' && key !== 'api_key')
      .sort()
      .map(key => `${key}=${String(params2[key])}`)
      .join('&');
    
    console.log(`   手動構建2: ${manualParams2}`);
    
    return {
      success: true,
      signatures: {
        large: signature1,
        normal: signature2
      },
      stringToSign: {
        large: manualParams1,
        normal: manualParams2
      }
    };
    
  } catch (error) {
    console.error('❌ 調試失敗:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 🎯 完整的 Cloudinary 修復驗證
 */
function completeCloudinaryFixVerification_debug() {
  console.log('🎯 === 完整的 Cloudinary 修復驗證 ===');
  
  try {
    // 1. 檢查配置
    console.log('1️⃣ 檢查配置...');
    const config = getConfig();
    const hasValidConfig = config.cloudinaryCloudName && config.cloudinaryApiKey && config.cloudinaryApiSecret;
    console.log(`   配置狀態: ${hasValidConfig ? '✅ 完整' : '❌ 不完整'}`);
    
    if (!hasValidConfig) {
      return {
        success: false,
        error: 'Cloudinary 配置不完整',
        recommendations: [
          '請在 APIKEY 工作表中設定：',
          'B8: Cloudinary Cloud Name',
          'B9: Cloudinary API Key', 
          'B10: Cloudinary API Secret'
        ]
      };
    }
    
    // 2. 測試簽名生成
    console.log('\n2️⃣ 測試簽名生成...');
    const signatureTest = debugSignatureGeneration_debug();
    console.log(`   簽名測試: ${signatureTest.success ? '✅ 成功' : '❌ 失敗'}`);
    
    // 3. 測試實際上傳
    console.log('\n3️⃣ 測試實際上傳...');
    const uploadTest = testCloudinarySignatureFix_debug();
    console.log(`   上傳測試: ${uploadTest.success ? '✅ 成功' : '❌ 失敗'}`);
    
    // 4. 測試 TTS 功能
    console.log('\n4️⃣ 測試 TTS 整合...');
    const ttsTest = verifyTTSFix_debug();
    console.log(`   TTS 測試: ${ttsTest.success ? '✅ 成功' : '❌ 失敗'}`);
    
    // 5. 總結
    console.log('\n📊 修復驗證總結:');
    console.log(`   配置: ${hasValidConfig ? '✅' : '❌'}`);
    console.log(`   簽名: ${signatureTest.success ? '✅' : '❌'}`);
    console.log(`   上傳: ${uploadTest.success ? '✅' : '❌'}`);
    console.log(`   TTS: ${ttsTest.success ? '✅' : '❌'}`);
    console.log(`   播放: ${ttsTest.isPlayable ? '✅' : '⚠️'}`);
    
    const allPassed = hasValidConfig && signatureTest.success && uploadTest.success && ttsTest.success;
    
    if (allPassed && ttsTest.isPlayable) {
      console.log('\n🎉 完全修復成功！所有功能正常');
      console.log('💡 可以在 LINE 中測試語音功能：「你說 你好嗎」');
    } else if (allPassed) {
      console.log('\n⚠️ 部分修復成功，檔案可下載但 Cloudinary 待調整');
    } else {
      console.log('\n❌ 修復未完成，請檢查上述失敗項目');
    }
    
    return {
      success: allPassed,
      results: {
        config: hasValidConfig,
        signature: signatureTest.success,
        upload: uploadTest.success,
        tts: ttsTest.success,
        playable: ttsTest.isPlayable
      },
      details: {
        signatureTest: signatureTest,
        uploadTest: uploadTest,
        ttsTest: ttsTest
      }
    };
    
  } catch (error) {
    console.error('❌ 完整驗證失敗:', error);
    return {
      success: false,
      error: error.message
    };
  }
}
