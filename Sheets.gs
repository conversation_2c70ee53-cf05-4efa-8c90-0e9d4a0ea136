// == 工作表管理模組 v3.0 ==
// 🚀 支援功能分類的智能模型管理系統
// 專門處理所有 Google Sheets 工作表的初始化和管理

// 1. 自動建立所有必要工作表
function initializeSheets() {
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  const sheetNames = [
    '活動日誌', '摘要日誌', '筆記暫存', '檔案日誌', '地點日誌',
    'APIKEY', 'Threads發文紀錄', 'Sheet1', 'TempSearch',
    // 記憶系統工作表
    '用戶對話歷史', '檔案記憶庫', '分析任務佇列',
    // 群組功能工作表
    '群組發言記錄'
  ];
  
  sheetNames.forEach(name => {
    if (!ss.getSheetByName(name)) {
      const newSheet = ss.insertSheet(name);
      console.log(`已建立工作表: ${name}`);
      
      // 設定新工作表的標題行
      setupSheetHeaders(newSheet, name);
    }
  });
  
  // 設定 APIKEY 工作表結構（安全版本 - 不覆蓋現有數據）
  const apikeySheet = ss.getSheetByName('APIKEY');
  if (apikeySheet) {
    setupAPIKEYSheetSafe(apikeySheet);
  }
}

// 2. 設定工作表標題行
function setupSheetHeaders(sheet, sheetName) {
  // 先檢查是否是記憶系統工作表
  if (['用戶對話歷史', '檔案記憶庫', '分析任務佇列'].includes(sheetName)) {
    setupMemorySheetHeaders(sheet, sheetName);
    return;
  }
  
  // 原有的其他工作表設定
  let headers = [];
  switch(sheetName) {
    case '活動日誌':
      headers = ['時間戳', '動作', '詳細資訊'];
      break;
    case '檔案日誌':
      headers = ['時間戳', '用戶ID', '檔案名稱', '檔案連結', '檔案大小', '媒體類型', '來源類型'];
      break;
    case '筆記暫存':
      headers = ['時間戳', '用戶ID', '來源類型', '筆記內容'];
      break;
    case 'Threads發文紀錄':
      headers = ['時間戳', '用戶ID', '發文內容', '發文狀態', '回應訊息'];
      break;
    case '摘要日誌':
      headers = ['時間戳', '事件類型', '摘要內容', '相關用戶'];
      break;
    case '地點日誌':
      headers = ['時間戳', '用戶ID', '位置名稱', '經度', '緯度', '地址'];
      break;
    case 'TempSearch':
      headers = ['時間戳', '搜尋關鍵字', '搜尋結果', '用戶ID'];
      break;
    case '群組發言記錄':
      headers = ['時間戳', '群組ID', '用戶ID', '用戶顯示名稱', '訊息內容', '訊息類型', '回應狀態'];
      break;
    default:
      return; // 其他工作表不設定標題
  }
  
  if (headers.length > 0) {
    sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
    sheet.getRange(1, 1, 1, headers.length).setFontWeight('bold');
    sheet.getRange(1, 1, 1, headers.length).setBackground('#e1f5fe');
    console.log(`已設定 ${sheetName} 工作表標題行`);
  }
}

// 3. 🔒 安全版本：設定 APIKEY 工作表結構（不覆蓋現有數據）
function setupAPIKEYSheetSafe(apikeySheet) {
  try {
    console.log('🔒 開始安全設定 APIKEY 工作表 v3.0...');
    
    // 檢查是否已有數據
    const lastRow = apikeySheet.getLastRow();
    const hasData = lastRow > 1;
    
    if (hasData) {
      console.log('⚠️ 檢測到現有數據，執行保護性更新...');
      
      // 🎯 關鍵修復：安全地增加缺失的配置行
      addMissingConfigRowsSafely(apikeySheet);
      
      // 🚀 更新模型選擇下拉選單（新版功能分類系統）
      updateFunctionalModelDropdowns(apikeySheet);
      
      // 🎯 設定最佳欄寬
      setupOptimalColumnWidths(apikeySheet);
      
      // 確保標題行格式正確
      ensureAPIKEYHeaders(apikeySheet);
      
      console.log('✅ 安全更新完成 - 現有數據已保護，功能分類系統已啟用');
      
    } else {
      console.log('📋 工作表為空，執行完整初始化...');
      
      // 工作表為空時才進行完整初始化
      setupAPIKEYSheetComplete(apikeySheet);
      
      console.log('✅ 完整初始化完成');
    }
    
    logActivity('APIKEY工作表更新', '功能分類模型系統已啟用，獨立驗證規則已設定');
    
  } catch (error) {
    console.error('❌ APIKEY 工作表設定失敗:', error);
    logActivity('APIKEY工作表錯誤', error.toString());
  }
}

// 4. 🎯 安全地增加缺失的配置行（使用新的功能分類）
function addMissingConfigRowsSafely(sheet) {
  try {
    console.log('🔧 安全檢查並補充缺失的配置行（功能分類系統）...');
    
    // 🎯 使用新的功能分類系統定義配置
    const baseConfigs = [
      { row: 1, aCol: 'API 項目說明', bCol: '', cCol: '備註' },
      { row: 2, aCol: 'LINE Channel Access Token', bCol: '', cCol: '' },
      { row: 3, aCol: 'LINE Channel Secret', bCol: '', cCol: '' },
      { row: 4, aCol: 'Gemini API Key', bCol: '', cCol: '' },
      { row: 5, aCol: 'YouTube Data API Key', bCol: '', cCol: '' },
      { row: 6, aCol: 'Threads User ID', bCol: '', cCol: '' },
      { row: 7, aCol: 'Threads Access Token', bCol: '', cCol: '' },
      { row: 8, aCol: 'Cloudinary Cloud Name', bCol: '', cCol: '' },
      { row: 9, aCol: 'Cloudinary API Key', bCol: '', cCol: '' },
      { row: 10, aCol: 'Cloudinary API Secret', bCol: '', cCol: '' },
      { row: 11, aCol: 'Google Drive Folder ID', bCol: '', cCol: '🔧 請填入您的 Google Drive 資料夾 ID' },
      { row: 12, aCol: 'Google Search API Key', bCol: '', cCol: '用於AI智能搜尋' },
      { row: 13, aCol: 'Google Search Engine ID', bCol: '', cCol: '用於AI智能搜尋' }
    ];
    
    // 🚀 從功能分類系統自動生成模型配置
    const functionalCategories = getAllFunctionalCategories();
    const functionalConfigs = [];
    
    functionalCategories.forEach(category => {
      const config = FUNCTIONAL_MODEL_CATEGORIES[category];
      const defaultModel = getDefaultModelForCategory(category);
      
      functionalConfigs.push({
        row: config.sheetRow,
        aCol: config.categoryName,
        bCol: defaultModel,
        cCol: config.description
      });
    });
    
    // 合併所有配置
    const allConfigs = [...baseConfigs, ...functionalConfigs];
    
    // 🔍 檢查每一行並安全地補充缺失的配置
    allConfigs.forEach(config => {
      try {
        // 檢查 A 欄是否有正確的標題
        const currentAValue = sheet.getRange(`A${config.row}`).getValue();
        
        if (!currentAValue || currentAValue !== config.aCol) {
          // A 欄缺失或錯誤，需要補充
          console.log(`🔧 補充 A${config.row}: ${config.aCol}`);
          sheet.getRange(`A${config.row}`).setValue(config.aCol);
        }
        
        // 檢查 B 欄是否有值（只在沒有值時設定預設值）
        const currentBValue = sheet.getRange(`B${config.row}`).getValue();
        
        if (!currentBValue && config.bCol) {
          // B 欄沒有值且有預設值，設定預設值
          console.log(`🔧 設定 B${config.row} 預設值: ${config.bCol}`);
          sheet.getRange(`B${config.row}`).setValue(config.bCol);
        }
        
        // 檢查 C 欄備註（只在沒有值時設定）
        const currentCValue = sheet.getRange(`C${config.row}`).getValue();
        
        if (!currentCValue && config.cCol) {
          // C 欄沒有值且有備註，設定備註
          console.log(`🔧 設定 C${config.row} 備註: ${config.cCol}`);
          sheet.getRange(`C${config.row}`).setValue(config.cCol);
        }
        
      } catch (rowError) {
        console.error(`❌ 處理第 ${config.row} 行時出錯:`, rowError);
      }
    });
    
    console.log('✅ 功能分類配置補充完成');
    
  } catch (error) {
    console.error('❌ 安全增加配置行失敗:', error);
    throw error;
  }
}

// 5. 🚀 更新功能分類模型下拉選單（每個功能獨立驗證規則）
function updateFunctionalModelDropdowns(sheet) {
  try {
    console.log('🔄 更新功能分類模型選擇下拉選單...');
    
    // 🎯 為每個功能類別設定獨立的驗證規則
    const functionalCategories = getAllFunctionalCategories();
    
    functionalCategories.forEach(category => {
      try {
        const config = FUNCTIONAL_MODEL_CATEGORIES[category];
        const models = getModelsByCategory(category);
        const helpText = getHelpTextForCategory(category);
        const sheetRow = getSheetRowForCategory(category);
        
        console.log(`🔧 設定 ${config.categoryName} (B${sheetRow}) 的下拉選單...`);
        
        // 建立該功能專用的驗證規則
        const categoryRule = SpreadsheetApp.newDataValidation()
          .requireValueInList(models, true)
          .setAllowInvalid(true)
          .setHelpText(helpText)
          .build();
        
        // 只對該功能的特定儲存格設定驗證規則
        sheet.getRange(`B${sheetRow}`).setDataValidation(categoryRule);
        
        console.log(`✅ ${config.categoryName} 下拉選單設定完成，共 ${models.length} 個模型選項`);
        
      } catch (categoryError) {
        console.error(`❌ 設定 ${category} 驗證規則失敗:`, categoryError);
      }
    });
    
    console.log('✅ 所有功能分類模型下拉選單更新完成');
    
  } catch (error) {
    console.error('❌ 更新功能分類下拉選單失敗:', error);
    throw error;
  }
}

// 6. 🎯 設定最佳欄寬（解決模型名稱顯示問題）
function setupOptimalColumnWidths(sheet) {
  try {
    console.log('🔧 設定最佳欄寬配置...');
    
    // 設定各欄寬度
    sheet.setColumnWidth(1, 250);  // A 欄：API 項目說明
    sheet.setColumnWidth(2, 350);  // B 欄：模型選擇（增加寬度以顯示完整模型名稱）
    sheet.setColumnWidth(3, 300);  // C 欄：備註說明
    
    console.log('✅ 欄寬設定完成：A欄 250px, B欄 350px, C欄 300px');
    
  } catch (error) {
    console.error('❌ 設定欄寬失敗:', error);
  }
}

// 7. 確保 APIKEY 標題行格式正確
function ensureAPIKEYHeaders(sheet) {
  try {
    // 確保標題行存在且格式正確
    const headerRange = sheet.getRange('A1:C1');
    const currentHeaders = headerRange.getValues()[0];
    
    // 檢查是否需要設定標題
    if (!currentHeaders[0] || currentHeaders[0] !== 'API 項目說明') {
      const headers = [['API 項目說明', '請在此填入實際值', '備註']];
      headerRange.setValues(headers);
    }
    
    // 設定標題格式
    headerRange.setFontWeight('bold');
    headerRange.setBackground('#e1f5fe');
    
    // 設定 A 欄標題格式（擴展到 A20）
    sheet.getRange('A1:A20').setFontWeight('bold');
    
    // 設定 B 欄背景色（輸入區域，擴展到 B20）
    sheet.getRange('B2:B20').setBackground('#f0f8ff');
    
  } catch (error) {
    console.error('❌ 設定標題格式失敗:', error);
  }
}

// 8. 🎯 完整初始化 APIKEY 工作表（使用功能分類系統）
function setupAPIKEYSheetComplete(apikeySheet) {
  console.log('📋 執行 APIKEY 工作表完整初始化（功能分類系統）...');
  
  // 基礎配置
  const baseHeaders = [
    ['API 項目說明', '請在此填入實際值', '備註'],
    ['LINE Channel Access Token', '', ''],
    ['LINE Channel Secret', '', ''],
    ['Gemini API Key', '', ''],
    ['YouTube Data API Key', '', ''],
    ['Threads User ID', '', ''],
    ['Threads Access Token', '', ''],
    ['Cloudinary Cloud Name', '', ''],
    ['Cloudinary API Key', '', ''],
    ['Cloudinary API Secret', '', ''],
    ['Google Drive Folder ID', '', '🔧 請填入您的 Google Drive 資料夾 ID'],
    ['Google Search API Key', '', '用於AI智能搜尋'],
    ['Google Search Engine ID', '', '用於AI智能搜尋']
  ];
  
  // 🚀 從功能分類系統自動生成模型配置
  const functionalCategories = getAllFunctionalCategories();
  const functionalHeaders = [];
  
  functionalCategories.forEach(category => {
    const config = FUNCTIONAL_MODEL_CATEGORIES[category];
    const defaultModel = getDefaultModelForCategory(category);
    
    functionalHeaders.push([
      config.categoryName,
      defaultModel,
      config.description
    ]);
  });
  
  // 合併所有標題行
  const allHeaders = [...baseHeaders, ...functionalHeaders];
  
  // 設定到工作表
  apikeySheet.getRange(1, 1, allHeaders.length, 3).setValues(allHeaders);
  
  // 設定格式
  apikeySheet.getRange('A1:A20').setFontWeight('bold');
  apikeySheet.getRange('B2:B20').setBackground('#f0f8ff');
  
  // 🚀 設定功能分類模型下拉選單
  updateFunctionalModelDropdowns(apikeySheet);
  
  // 🎯 設定最佳欄寬
  setupOptimalColumnWidths(apikeySheet);
  
  console.log('✅ APIKEY 工作表完整初始化完成，功能分類系統已啟用');
}

// 9. 清理工作表數據（維護功能）
function cleanupWorksheets() {
  try {
    const ss = SpreadsheetApp.getActiveSpreadsheet();
    const cleanupConfig = {
      '活動日誌': { maxRows: 1000, keepRows: 500 },
      '檔案日誌': { maxRows: 2000, keepRows: 1000 },
      '用戶對話歷史': { maxRows: 5000, keepRows: 2500 },
      '筆記暫存': { maxRows: 1000, keepRows: 500 },
      '群組發言記錄': { maxRows: 5000, keepRows: 2500 }
    };
    
    Object.entries(cleanupConfig).forEach(([sheetName, config]) => {
      const sheet = ss.getSheetByName(sheetName);
      if (sheet && sheet.getLastRow() > config.maxRows) {
        const rowsToDelete = sheet.getLastRow() - config.keepRows;
        sheet.deleteRows(2, rowsToDelete); // 從第2行開始刪除（保留標題行）
        console.log(`已清理 ${sheetName}：刪除 ${rowsToDelete} 行舊資料`);
      }
    });
    
    return `✅ 工作表清理完成`;
  } catch (error) {
    console.error('清理工作表錯誤:', error);
    return `❌ 工作表清理失敗：${error.message}`;
  }
}

// 10. 檢查工作表健康狀態
function checkSheetsHealth() {
  try {
    const ss = SpreadsheetApp.getActiveSpreadsheet();
    const requiredSheets = [
      '活動日誌', '摘要日誌', '筆記暫存', '檔案日誌', '地點日誌',
      'APIKEY', 'Threads發文紀錄', '用戶對話歷史', '檔案記憶庫', '分析任務佇列',
      '群組發言記錄'
    ];
    
    const healthReport = {
      totalSheets: requiredSheets.length,
      existingSheets: 0,
      missingSheets: [],
      sheetDetails: {}
    };
    
    requiredSheets.forEach(sheetName => {
      const sheet = ss.getSheetByName(sheetName);
      if (sheet) {
        healthReport.existingSheets++;
        healthReport.sheetDetails[sheetName] = {
          exists: true,
          rows: sheet.getLastRow(),
          columns: sheet.getLastColumn()
        };
      } else {
        healthReport.missingSheets.push(sheetName);
        healthReport.sheetDetails[sheetName] = {
          exists: false,
          rows: 0,
          columns: 0
        };
      }
    });
    
    console.log('工作表健康檢查完成:', healthReport);
    return healthReport;
  } catch (error) {
    console.error('工作表健康檢查錯誤:', error);
    return { error: error.toString() };
  }
}

// 11. 備份重要工作表數據
function backupImportantSheets() {
  try {
    const ss = SpreadsheetApp.getActiveSpreadsheet();
    const timestamp = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
    const backupSheets = ['APIKEY', '檔案記憶庫', '用戶對話歷史', '群組發言記錄'];
    
    backupSheets.forEach(sheetName => {
      const originalSheet = ss.getSheetByName(sheetName);
      if (originalSheet) {
        const backupName = `${sheetName}_備份_${timestamp}`;
        
        // 檢查是否已存在備份
        if (!ss.getSheetByName(backupName)) {
          const backupSheet = originalSheet.copyTo(ss);
          backupSheet.setName(backupName);
          console.log(`已建立備份: ${backupName}`);
        }
      }
    });
    
    return `✅ 重要工作表備份完成 (${timestamp})`;
  } catch (error) {
    console.error('備份工作表錯誤:', error);
    return `❌ 備份失敗：${error.message}`;
  }
}

// ===== 🧪 測試函數 =====

// 12. 🧪 測試函數：測試功能分類模型系統
function testFunctionalModelSystem_debug() {
  console.log('=== 測試功能分類模型系統 v3.0 ===');
  
  try {
    const ss = SpreadsheetApp.getActiveSpreadsheet();
    const apikeySheet = ss.getSheetByName('APIKEY');
    
    if (!apikeySheet) {
      console.log('❌ APIKEY 工作表不存在');
      return '❌ APIKEY 工作表不存在';
    }
    
    console.log('🚀 測試功能分類系統...');
    
    // 測試功能分類配置
    const categories = getAllFunctionalCategories();
    console.log(`📊 功能分類數量: ${categories.length}`);
    
    categories.forEach(category => {
      const config = FUNCTIONAL_MODEL_CATEGORIES[category];
      const models = getModelsByCategory(category);
      const defaultModel = getDefaultModelForCategory(category);
      
      console.log(`📋 ${config.categoryName}:`);
      console.log(`   行號: B${config.sheetRow}`);
      console.log(`   模型數量: ${models.length}`);
      console.log(`   預設模型: ${defaultModel}`);
      
      // 檢查工作表中的實際值
      const currentValue = apikeySheet.getRange(`B${config.sheetRow}`).getValue();
      console.log(`   當前設定: ${currentValue || '(空值)'}`);
    });
    
    // 執行系統更新
    console.log('\n🔧 執行系統更新...');
    setupAPIKEYSheetSafe(apikeySheet);
    
    // 驗證更新結果
    console.log('\n📊 驗證更新結果:');
    categories.forEach(category => {
      const config = FUNCTIONAL_MODEL_CATEGORIES[category];
      const updatedValue = apikeySheet.getRange(`B${config.sheetRow}`).getValue();
      const isValid = updatedValue && getModelsByCategory(category).includes(updatedValue);
      
      console.log(`   ${config.categoryName}: ${isValid ? '✅' : '❌'} ${updatedValue}`);
    });
    
    console.log('\n✅ 功能分類模型系統測試完成');
    return `✅ 功能分類系統測試完成，共 ${categories.length} 個功能類別`;
    
  } catch (error) {
    console.error('❌ 功能分類系統測試失敗:', error);
    return `❌ 測試失敗：${error.message}`;
  }
}

// 13. 🧪 測試函數：驗證每個功能的下拉選單
function testIndividualDropdownValidation_debug() {
  console.log('=== 測試獨立下拉選單驗證 ===');
  
  try {
    const ss = SpreadsheetApp.getActiveSpreadsheet();
    const apikeySheet = ss.getSheetByName('APIKEY');
    
    if (!apikeySheet) {
      console.log('❌ APIKEY 工作表不存在');
      return '❌ APIKEY 工作表不存在';
    }
    
    // 更新下拉選單
    updateFunctionalModelDropdowns(apikeySheet);
    
    // 驗證每個功能的下拉選單
    const categories = getAllFunctionalCategories();
    const validationResults = [];
    
    categories.forEach(category => {
      const config = FUNCTIONAL_MODEL_CATEGORIES[category];
      const sheetRow = getSheetRowForCategory(category);
      const models = getModelsByCategory(category);
      
      try {
        // 獲取該儲存格的驗證規則
        const cell = apikeySheet.getRange(`B${sheetRow}`);
        const validation = cell.getDataValidation();
        
        const result = {
          category: category,
          categoryName: config.categoryName,
          row: sheetRow,
          expectedModels: models.length,
          hasValidation: !!validation,
          validationText: validation ? validation.getHelpText() : null
        };
        
        validationResults.push(result);
        
        console.log(`📋 ${config.categoryName} (B${sheetRow}):`);
        console.log(`   預期模型數: ${models.length}`);
        console.log(`   有驗證規則: ${result.hasValidation ? '✅' : '❌'}`);
        if (result.validationText) {
          const helpLines = result.validationText.split('\n');
          console.log(`   說明文字: ${helpLines[0]}...`);
        }
        console.log('');
        
      } catch (cellError) {
        console.error(`❌ 檢查 ${category} 驗證規則失敗:`, cellError);
      }
    });
    
    const successCount = validationResults.filter(r => r.hasValidation).length;
    console.log(`📊 驗證結果: ${successCount}/${validationResults.length} 個功能有獨立驗證規則`);
    
    return `✅ 獨立下拉選單驗證完成，${successCount}/${validationResults.length} 個功能已設定`;
    
  } catch (error) {
    console.error('❌ 獨立下拉選單驗證失敗:', error);
    return `❌ 驗證失敗：${error.message}`;
  }
}

// 14. 🧪 測試函數：強制更新功能分類系統
function forceUpdateFunctionalSystem_debug() {
  console.log('🔧 === 強制更新功能分類系統 ===');
  
  try {
    const ss = SpreadsheetApp.getActiveSpreadsheet();
    const apikeySheet = ss.getSheetByName('APIKEY');
    
    if (!apikeySheet) {
      console.log('❌ APIKEY 工作表不存在，需要先建立');
      return '❌ APIKEY 工作表不存在';
    }
    
    console.log('🔧 執行強制更新...');
    
    // 執行完整的安全更新
    setupAPIKEYSheetSafe(apikeySheet);
    
    // 驗證更新結果
    console.log('📊 驗證功能分類系統...');
    const categories = getAllFunctionalCategories();
    let successCount = 0;
    
    categories.forEach(category => {
      const config = FUNCTIONAL_MODEL_CATEGORIES[category];
      const aValue = apikeySheet.getRange(`A${config.sheetRow}`).getValue();
      const bValue = apikeySheet.getRange(`B${config.sheetRow}`).getValue();
      
      const aCorrect = aValue === config.categoryName;
      const bValid = bValue && getModelsByCategory(category).includes(bValue);
      
      if (aCorrect && bValid) {
        successCount++;
        console.log(`✅ ${config.categoryName}: ${bValue}`);
      } else {
        console.log(`❌ ${config.categoryName}: A欄${aCorrect?'✅':'❌'} B欄${bValid?'✅':'❌'}`);
      }
    });
    
    console.log(`📊 更新結果: ${successCount}/${categories.length} 個功能配置正確`);
    
    return `✅ 強制更新完成，${successCount}/${categories.length} 個功能配置正確`;
    
  } catch (error) {
    console.error('❌ 強制更新失敗:', error);
    return `❌ 更新失敗: ${error.message}`;
  }
}

// 15. 🧪 測試函數：初始化所有工作表
function testInitializeAllSheets_debug() {
  console.log('=== 測試工作表初始化 ===');
  
  try {
    initializeSheets();
    const healthReport = checkSheetsHealth();
    
    console.log(`✅ 工作表初始化測試完成`);
    console.log(`📊 存在的工作表: ${healthReport.existingSheets}/${healthReport.totalSheets}`);
    
    if (healthReport.missingSheets.length > 0) {
      console.log(`❌ 缺少的工作表: ${healthReport.missingSheets.join(', ')}`);
    }
    
    return healthReport;
  } catch (error) {
    console.error('❌ 工作表初始化測試失敗:', error);
    return { error: error.toString() };
  }
}

// 16. 🧪 測試函數：清理所有工作表
function testCleanupAllSheets_debug() {
  console.log('=== 測試工作表清理 ===');
  
  try {
    const result = cleanupWorksheets();
    console.log(result);
    return result;
  } catch (error) {
    console.error('❌ 工作表清理測試失敗:', error);
    return `❌ 清理測試失敗：${error.message}`;
  }
}
