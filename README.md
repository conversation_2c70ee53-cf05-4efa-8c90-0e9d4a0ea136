# LINE Bot 智能助理 🤖

一個基於 Google Apps Script 和 Gemini AI 的智能 LINE Bot，具備檔案處理、對話記憶、群組聊天記錄等功能。

## ✨ 主要功能

- 🤖 **AI 智能對話**：基於 Gemini AI 的自然語言理解
- 🔊 **文字轉語音 (TTS)**：使用 Gemini TTS 模型，支援「念出來」指令 🆕
- 🎨 **AI 圖像生成**：使用 Gemini 圖像生成模型，支援「畫一張」指令 🆕
- 📁 **檔案智能處理**：自動分析上傳的圖片、文件等
- 🧠 **記憶系統**：記住用戶對話和檔案內容
- 👥 **群組功能**：記錄群組發言，支援成員查詢
- 🔍 **智能搜尋**：自動判斷是否需要網路搜尋
- 📱 **多平台整合**：支援 Threads 發文等功能

## 🔧 配置系統說明

### 🎯 敏感資訊保護策略

本專案使用 **Git skip-worktree** 技術來保護敏感資訊：

- **`.clasp.json`** 包含真實的 Google Apps Script 專案 ID
- **所有 API Keys** 存放在 Google Sheets 的 APIKEY 工作表中
- **Git 會忽略 `.clasp.json` 的變更**，確保敏感資訊不會被意外提交

### 📊 配置項目對照表

| 配置項目 | 存放位置 | 說明 |
|---------|---------|------|
| Google Apps Script ID | `.clasp.json` (skip-worktree) | clasp 推送代碼用，Git 忽略變更 |
| LINE Channel Access Token | Google Sheets B2 | 程式從 getConfig() 讀取 |
| LINE Channel Secret | Google Sheets B3 | 程式從 getConfig() 讀取 |
| Gemini API Key | Google Sheets B4 | 程式從 getConfig() 讀取 |
| Google Drive Folder ID | Google Sheets B11 | 程式從 getConfig() 讀取 |
| 其他 API Keys | Google Sheets B5-B13 | 程式從 getConfig() 讀取 |

## 🚀 快速開始

### 👨‍💻 給原作者（繼續開發）

如果您是專案原作者，想要繼續開發：

#### 設定 skip-worktree（一次性操作）

**使用命令列：**
```bash
git update-index --skip-worktree .clasp.json
```

**使用 SourceTree：**
1. 右鍵點擊 `.clasp.json` 文件
2. 選擇 `Skip Worktree`
3. 或在終端機標籤中執行：`git update-index --skip-worktree .clasp.json`

#### 日常開發流程

設定完成後，您的日常工作流程：

```bash
# 正常推送代碼到 Google Apps Script
clasp push

# 正常 Git 操作，完全不用管配置文件
git add .
git commit -m "您的更新"
git push origin main
```

✅ **Git 永遠不會看到 `.clasp.json` 的變更**  
✅ **您可以正常使用 clasp push**  
✅ **其他人下載代碼時看到的是模板版本**  

#### 如果需要更新開源模板

```bash
# 暫時恢復 Git 追蹤
git update-index --no-skip-worktree .clasp.json

# 更新為模板版本
cp .clasp.json.template .clasp.json

# 提交模板更新
git add .clasp.json
git commit -m "更新 .clasp.json 模板"

# 重新設定 skip-worktree 並恢復真實配置
git update-index --skip-worktree .clasp.json
# 手動編輯 .clasp.json，改回您的真實 Script ID
```

### 📖 給新用戶（完整設定）

如果您是新用戶，想要使用這個 LINE Bot：

#### 步驟 1：建立 Google Apps Script 專案

1. 前往 [Google Apps Script](https://script.google.com/)
2. 點擊「新增專案」
3. 複製專案的 **Script ID**（在網址列中可以找到）

#### 步驟 2：設定本地配置

1. **複製並編輯配置檔案**：
   ```bash
   cp .clasp.json.template .clasp.json
   ```

2. **編輯 `.clasp.json`**，填入您的真實 Script ID：
   ```json
   {
     "scriptId": "您的真實Google_Apps_Script專案ID",
     "rootDir": "."
   }
   ```

3. **設定 skip-worktree（重要）**：
   ```bash
   git update-index --skip-worktree .clasp.json
   ```

#### 步驟 3：部署代碼

1. **安裝 clasp**：
   ```bash
   npm install -g @google/clasp
   ```

2. **登入並推送**：
   ```bash
   clasp login
   clasp push
   ```

#### 步驟 4：建立 Google Sheets 資料庫

1. 建立一個新的 Google 試算表
2. 在 Google Apps Script 中執行 `initializeSheets()` 函數

#### 步驟 5：設定所有 API Keys

在 Google Sheets 的 `APIKEY` 工作表中填入：

| 儲存格 | API 項目 | 必需性 | 取得方式 |
|-------|---------|--------|----------|
| B2 | LINE Channel Access Token | ✅ 必需 | [LINE Developers](https://developers.line.biz/) |
| B3 | LINE Channel Secret | ✅ 必需 | [LINE Developers](https://developers.line.biz/) |
| B4 | Gemini API Key | ✅ 必需 | [Google AI Studio](https://aistudio.google.com/) |
| B11 | Google Drive Folder ID | ✅ 必需 | 建立資料夾並複製 ID |
| B5 | YouTube Data API Key | ⚪ 可選 | [Google Cloud Console](https://console.cloud.google.com/) |
| B6-B13 | 其他 API Keys | ⚪ 可選 | 詳見 `config.template.js` |

#### 步驟 6：部署為 Web App

1. 在 Apps Script 中點擊「部署」→「新增部署作業」
2. 選擇「網頁應用程式」，執行身分「我」，存取權限「任何人」
3. 複製 **Web App URL**

#### 步驟 7：設定 LINE Bot

1. 前往 [LINE Developers Console](https://developers.line.biz/)
2. 在 `Messaging API` 設定中填入 Webhook URL
3. 啟用 **Use webhook**，停用 **Auto-reply messages**

## 🎮 功能使用

### 個人對話
- 直接傳送訊息進行 AI 對話
- 上傳檔案進行智能分析
- 使用 `記錄 內容` 儲存筆記

### 群組使用
- 所有指令需加 `!` 前綴
- `!help` - 查看功能說明
- `!張三都講了些什麼？` - 查詢成員發言
- `!你覺得李四說的有道理嗎？` - AI 分析發言

## 🔧 開發者資訊

### 專案結構

```
├── .clasp.json.template     # Apps Script 配置模板（新用戶參考）
├── .clasp.json              # 真實配置（skip-worktree 保護）
├── config.template.js       # API Keys 參考說明
├── *.gs                    # Google Apps Script 主要代碼
├── LICENSE                 # MIT 許可證
├── README.md              # 專案說明
└── .gitignore             # Git 忽略文件
```

### 主要模組

- `Code.gs` - 主要入口和版本管理
- `Webhook.gs` - LINE Webhook 處理
- `TextProcessor.gs` - 文字訊息處理和路由
- `AI_Features.gs` - AI 功能整合
- `MediaProcessor.gs` - 媒體檔案處理
- `Memory_System.gs` - 記憶系統
- `GroupChatTracker.gs` - 群組聊天記錄
- `Utils.gs` - 工具函數
- `Sheets.gs` - 工作表管理

### 🧪 測試功能

執行系統健康檢查：
```javascript
// 在 Google Apps Script 中執行
systemHealthCheck()
```

測試新功能（TTS 和圖像生成）：
```javascript
// 測試所有新功能
testAllNewFeatures()

// 快速功能檢查
quickFeatureCheck()

// 測試 TTS 功能
testTTS()

// 測試圖像生成功能
testImageGeneration()
```

### 🎯 新功能使用範例

**文字轉語音：**
```
用戶：念出來：今天天氣很好
回應：🔊 語音轉換完成！
     📄 轉換文本：今天天氣很好
     🎵 音頻檔案：Gemini_TTS_xxx.mp3
     🔗 下載連結：[Drive連結]
```

**圖像生成：**
```
用戶：畫一張可愛的小貓
回應：🎨 圖像生成完成！
     📝 描述：可愛的小貓
     🖼️ 圖片檔案：Gemini_Generated_xxx.png
     🔗 查看連結：[Drive連結]
```

## ❓ 常見問題

### Q: 為什麼要使用 skip-worktree？
A: 讓開發者可以在本地使用真實配置，同時保證開源版本不包含敏感資訊。

### Q: 如何檢查 skip-worktree 狀態？
A: 執行 `git ls-files -v | grep "^S"` 可以看到被 skip-worktree 的文件。

### Q: 在 SourceTree 中如何設定 skip-worktree？
A: 右鍵點擊文件 → 選擇 `Skip Worktree`，或使用終端機執行命令。

### Q: API Keys 要填在哪裡？
A: 所有 API Keys 都填在 Google Sheets 的 `APIKEY` 工作表中。

### Q: 如何取得各種 API Keys？
A: 請參考 `config.template.js` 中的詳細說明。

## 📄 許可證

本專案採用 [MIT 許可證](LICENSE)。

## 🤝 貢獻

歡迎提交 Issue 和 Pull Request！

---

**🔒 安全保證**：使用 Git skip-worktree 技術保護敏感資訊，開源安全無虞。
