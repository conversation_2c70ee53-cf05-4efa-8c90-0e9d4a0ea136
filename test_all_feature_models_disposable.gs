// ===== 🧪 一次性測試和修復腳本 =====
// 用於測試新增的功能模型和智能處理中樞
// 📁 位置：S:\OneDrive\Dokumente\Projects\tribe\tribe-line-bot\_ignore\_debug_scripts

/**
 * 🧪 完整功能測試：所有新增功能模型
 * 這是一次性測試腳本，用於驗證所有功能模型的整合
 */
function testAllNewFeatureModels() {
  console.log('🧪 ===== 完整功能測試：所有新增功能模型 =====');
  
  try {
    // 1. 測試配置讀取
    console.log('\n1. 📋 測試配置讀取...');
    const config = getConfig();
    console.log(`✅ 一般功能模型: ${config.generalModel}`);
    console.log(`✅ 圖片分析模型: ${config.visionModel}`);
    console.log(`✅ 語音合成模型: ${config.ttsModel}`);
    console.log(`✅ 對話音頻模型: ${config.audioDialogModel}`);
    console.log(`✅ 嵌入向量模型: ${config.embeddingModel}`);
    console.log(`✅ 高效率模型: ${config.fastModel}`);
    console.log(`✅ 圖片生成模型: ${config.imageGenModel}`);
    
    // 2. 測試模型驗證
    console.log('\n2. 🔍 測試模型功能驗證...');
    testModelValidationForAllFunctions();
    
    // 3. 測試智能模型選擇
    console.log('\n3. 🧠 測試智能模型選擇...');
    testSmartModelSelectionForAllTasks();
    
    // 4. 測試 A18 嵌入向量模型使用場景
    console.log('\n4. 📊 測試 A18 嵌入向量模型使用場景...');
    testA18EmbeddingModelScenarios();
    
    // 5. 測試 A19 高效率模型使用場景
    console.log('\n5. ⚡ 測試 A19 高效率模型使用場景...');
    testA19FastModelScenarios();
    
    // 6. 測試增強版智能處理中樞
    console.log('\n6. 🚀 測試增強版智能處理中樞...');
    testEnhancedProcessorIntegration();
    
    console.log('\n🎉 ===== 完整功能測試完成 =====');
    return '✅ 所有新增功能模型測試通過';
    
  } catch (error) {
    console.error('❌ 完整功能測試失敗:', error);
    return `❌ 測試失敗: ${error.message}`;
  }
}

/**
 * 📊 A18 嵌入向量模型使用場景測試
 * 說明如何使用嵌入向量模型進行語義搜索和文檔匹配
 */
function testA18EmbeddingModelScenarios() {
  console.log('📊 === A18 嵌入向量模型使用場景測試 ===');
  
  const embeddingScenarios = [
    {
      userInput: '找出與我上次聊到的 AI 教育相關的檔案',
      explanation: '🔍 語義搜索：AI 會理解「AI 教育」的語義，找出所有相關檔案',
      aiProcess: '用戶意圖 → 嵌入向量模型 → 計算語義相似度 → 找到相關檔案',
      promptExample: 'SEMANTIC_SEARCH: 查詢="AI 教育", 返回相關檔案'
    },
    {
      userInput: '這份報告跟之前的研究有什麼關聯？',
      explanation: '📄 文檔關聯分析：比較當前文檔與歷史文檔的語義相似度',
      aiProcess: '文檔內容 → 嵌入向量模型 → 與歷史文檔比較 → 找出關聯性',
      promptExample: 'DOCUMENT_CORRELATION: 比較當前文檔與歷史文檔的相似度'
    },
    {
      userInput: '把相似主題的筆記整理在一起',
      explanation: '🗂️ 內容自動分類：根據語義相似度自動整理相同主題的內容',
      aiProcess: '所有筆記 → 嵌入向量模型 → 計算相似度矩陣 → 自動分類',
      promptExample: 'CONTENT_CLUSTERING: 將相似內容自動分組'
    },
    {
      userInput: '推薦我可能感興趣的文章',
      explanation: '🎯 個性化推薦：根據用戶歷史興趣和行為模式推薦內容',
      aiProcess: '用戶興趣特徵 → 嵌入向量模型 → 內容匹配 → 個性化推薦',
      promptExample: 'PERSONALIZED_RECOMMENDATION: 基於用戶興趣推薦內容'
    }
  ];
  
  embeddingScenarios.forEach((scenario, index) => {
    console.log(`\n📊 場景 ${index + 1}: ${scenario.userInput}`);
    console.log(`   💡 說明: ${scenario.explanation}`);
    console.log(`   🔄 AI 處理流程: ${scenario.aiProcess}`);
    console.log(`   📝 提示詞範例: ${scenario.promptExample}`);
    
    // 模擬使用嵌入模型
    const config = getConfig();
    console.log(`   🤖 使用模型: ${config.embeddingModel}`);
    console.log(`   📈 API 版本: ${getModelApiVersion(config.embeddingModel)}`);
  });
  
  console.log('\n✅ A18 嵌入向量模型使用場景測試完成');
  console.log('💡 關鍵優勢：理解語義、不僅僅是關鍵字匹配、支援模糊查詢');
}

/**
 * ⚡ A19 高效率模型使用場景測試
 * 說明如何使用高效率模型進行快速處理和批量任務
 */
function testA19FastModelScenarios() {
  console.log('⚡ === A19 高效率模型使用場景測試 ===');
  
  const fastProcessingScenarios = [
    {
      userInput: '幫我快速整理這100個檔案的標題',
      explanation: '📚 批量文件處理：快速處理大量檔案，提取標題和摘要',
      aiProcess: '100個檔案 → 高效率模型並行處理 → 快速提取標題 → 批量回覆',
      promptExample: 'BATCH_TITLE_EXTRACTION: 快速提取所有檔案標題',
      advantages: '速度快、成本低、適合大量重複性任務'
    },
    {
      userInput: '快速分類這些郵件',
      explanation: '🗂️ 快速分類任務：根據內容快速將郵件分類到不同類別',
      aiProcess: '郵件列表 → 高效率模型快速分析 → 自動分類 → 回覆分類結果',
      promptExample: 'QUICK_EMAIL_CLASSIFICATION: 快速分類郵件',
      advantages: '處理速度快、適合重複性分類工作'
    },
    {
      userInput: '給我一個簡單的狀態確認',
      explanation: '✅ 狀態確認任務：快速檢查和確認系統或項目狀態',
      aiProcess: '狀態查詢 → 高效率模型快速分析 → 簡潔回覆',
      promptExample: 'STATUS_CHECK: 快速確認當前狀態',
      advantages: '回應迅速、資源消耗少'
    },
    {
      userInput: '批量標記這些任務的優先級',
      explanation: '🏷️ 批量標記任務：根據內容和條件快速為任務分配優先級',
      aiProcess: '任務列表 → 高效率模型批量分析 → 優先級分配 → 批量更新',
      promptExample: 'BATCH_PRIORITY_TAGGING: 批量分配任務優先級',
      advantages: '批量處理能力強、一致性好'
    }
  ];
  
  fastProcessingScenarios.forEach((scenario, index) => {
    console.log(`\n⚡ 場景 ${index + 1}: ${scenario.userInput}`);
    console.log(`   💡 說明: ${scenario.explanation}`);
    console.log(`   🔄 AI 處理流程: ${scenario.aiProcess}`);
    console.log(`   📝 提示詞範例: ${scenario.promptExample}`);
    console.log(`   🎯 優勢: ${scenario.advantages}`);
    
    // 模擬使用高效率模型
    const config = getConfig();
    console.log(`   🤖 使用模型: ${config.fastModel}`);
    console.log(`   📈 API 版本: ${getModelApiVersion(config.fastModel)}`);
  });
  
  console.log('\n✅ A19 高效率模型使用場景測試完成');
  console.log('💡 關鍵優勢：成本效益、高吞吐量、適合批量處理、快速回應');
}

/**
 * 🔍 模型功能驗證測試
 */
function testModelValidationForAllFunctions() {
  const testCases = [
    { model: 'gemini-2.5-flash-preview-tts', function: 'text_to_speech' },
    { model: 'gemini-2.5-flash-preview-native-audio-dialog', function: 'conversational_audio' },
    { model: 'gemini-2.0-flash-preview-image-generation', function: 'image_generation' },
    { model: 'gemini-embedding-exp', function: 'semantic_search' },
    { model: 'gemini-2.5-flash-lite-preview-06-17', function: 'fast_processing' },
    // 錯誤匹配測試
    { model: 'gemini-2.5-flash', function: 'text_to_speech' }, // 應該失敗
    { model: 'gemini-2.5-pro', function: 'image_generation' }  // 應該失敗
  ];
  
  testCases.forEach(testCase => {
    const validation = validateModelForFunction(testCase.model, testCase.function);
    console.log(`🔍 ${testCase.model} 用於 ${testCase.function}: ${validation.isValid ? '✅' : '❌'}`);
    
    if (!validation.isValid && validation.recommendations.length > 0) {
      console.log(`   💡 建議: ${validation.recommendations[0]}`);
    }
  });
}

/**
 * 🧠 智能模型選擇測試
 */
function testSmartModelSelectionForAllTasks() {
  const tasks = [
    'text_to_speech',
    'conversational_audio',
    'image_generation',
    'semantic_search',
    'vision_analysis',
    'fast_processing',
    'general'
  ];
  
  tasks.forEach(task => {
    const selectedModel = getModelForTask(task);
    const config = getConfig();
    
    let expectedModel = '';
    switch(task) {
      case 'text_to_speech': expectedModel = config.ttsModel; break;
      case 'conversational_audio': expectedModel = config.audioDialogModel; break;
      case 'image_generation': expectedModel = config.imageGenModel; break;
      case 'semantic_search': expectedModel = config.embeddingModel; break;
      case 'vision_analysis': expectedModel = config.visionModel; break;
      case 'fast_processing': expectedModel = config.fastModel; break;
      case 'general': expectedModel = config.generalModel; break;
    }
    
    const isCorrect = selectedModel === expectedModel;
    console.log(`🧠 任務 ${task}: ${isCorrect ? '✅' : '❌'} ${selectedModel}`);
  });
}

/**
 * 🚀 增強版處理器整合測試
 */
function testEnhancedProcessorIntegration() {
  const testMessages = [
    { message: '你說今天天氣很好', expectedIntent: 'text_to_speech', expectedModel: 'ttsModel' },
    { message: '跟我聊聊天', expectedIntent: 'conversational_audio', expectedModel: 'audioDialogModel' },
    { message: '畫一隻貓', expectedIntent: 'image_generation', expectedModel: 'imageGenModel' },
    { message: '找相關檔案', expectedIntent: 'semantic_search', expectedModel: 'embeddingModel' },
    { message: '快速處理', expectedIntent: 'batch_processing', expectedModel: 'fastModel' }
  ];
  
  testMessages.forEach(test => {
    console.log(`🚀 "${test.message}" → ${test.expectedIntent} → ${test.expectedModel}`);
  });
}

/**
 * 🎙️ Native Audio Dialog 模型配置測試
 * 測試從鸚鵡模式升級到對話模式的配置
 */
function testNativeAudioDialogConfiguration() {
  console.log('🎙️ === Native Audio Dialog 模型配置測試 ===');
  
  console.log('\n📋 模型配置對比:');
  
  const config = getConfig();
  console.log(`🦜 A16 語音合成模型 (鸚鵡模式): ${config.ttsModel}`);
  console.log(`   • 功能: 文字轉語音，複述用戶指定內容`);
  console.log(`   • 使用場景: "你說：今天天氣很好" → 複述`);
  console.log(`   • API版本: ${getModelApiVersion(config.ttsModel)}`);
  
  console.log(`\n🎙️ A17 對話音頻模型 (大腦模式): ${config.audioDialogModel}`);
  console.log(`   • 功能: 智能對話，理解並回應用戶`);
  console.log(`   • 使用場景: "今天天氣如何？" → 智能回答`);
  console.log(`   • API版本: ${getModelApiVersion(config.audioDialogModel)}`);
  
  console.log('\n🔄 智能模式切換邏輯:');
  const testCases = [
    { input: '你說：今天天氣很好', expectedMode: 'parrot', expectedModel: config.ttsModel },
    { input: '今天天氣如何？', expectedMode: 'conversational', expectedModel: config.audioDialogModel },
    { input: '重複：我愛你', expectedMode: 'parrot', expectedModel: config.ttsModel },
    { input: '跟我聊聊天', expectedMode: 'conversational', expectedModel: config.audioDialogModel }
  ];
  
  testCases.forEach(testCase => {
    console.log(`\n🧪 輸入: "${testCase.input}"`);
    console.log(`   期望模式: ${testCase.expectedMode}`);
    console.log(`   期望模型: ${testCase.expectedModel}`);
    console.log(`   智能路由: ${testCase.expectedMode === 'parrot' ? '🦜 鸚鵡模式' : '🎙️ 對話模式'}`);
  });
  
  console.log('\n✅ Native Audio Dialog 模型配置測試完成');
  console.log('💡 系統會自動判斷用戶意圖，選擇合適的音頻處理模式');
}

/**
 * 🧪 試算表配置完整性檢查
 */
function checkSpreadsheetConfigIntegrity() {
  console.log('🧪 === 試算表配置完整性檢查 ===');
  
  try {
    const ss = SpreadsheetApp.getActiveSpreadsheet();
    const apikeySheet = ss.getSheetByName('APIKEY');
    
    if (!apikeySheet) {
      console.log('❌ APIKEY 工作表不存在');
      return '❌ 配置檢查失敗';
    }
    
    const expectedConfigs = [
      { row: 14, name: '一般功能模型', configKey: 'generalModel' },
      { row: 15, name: '圖片分析模型', configKey: 'visionModel' },
      { row: 16, name: '語音合成模型', configKey: 'ttsModel' },
      { row: 17, name: '對話音頻模型', configKey: 'audioDialogModel' },
      { row: 18, name: '嵌入向量模型', configKey: 'embeddingModel' },
      { row: 19, name: '高效率模型', configKey: 'fastModel' },
      { row: 20, name: '圖片生成模型', configKey: 'imageGenModel' }
    ];
    
    console.log('📋 檢查模型配置行:');
    expectedConfigs.forEach(config => {
      const labelValue = apikeySheet.getRange(`A${config.row}`).getValue();
      const modelValue = apikeySheet.getRange(`B${config.row}`).getValue();
      
      const labelCorrect = labelValue === config.name;
      const hasValue = modelValue && modelValue.length > 0;
      
      console.log(`   A${config.row}: ${labelCorrect ? '✅' : '❌'} ${labelValue}`);
      console.log(`   B${config.row}: ${hasValue ? '✅' : '⚠️'} ${modelValue || '(空值)'}`);
      console.log('');
    });
    
    // 檢查下拉選單
    console.log('📝 檢查下拉選單驗證規則:');
    const range = apikeySheet.getRange('B14:B20');
    const validation = range.getDataValidation();
    
    if (validation) {
      console.log('✅ 下拉選單驗證規則已設定');
      console.log(`   幫助文字: ${validation.getHelpText().substring(0, 50)}...`);
    } else {
      console.log('⚠️ 下拉選單驗證規則未設定');
    }
    
    console.log('\n✅ 試算表配置完整性檢查完成');
    return '✅ 配置檢查通過';
    
  } catch (error) {
    console.error('❌ 配置檢查失敗:', error);
    return `❌ 檢查失敗: ${error.message}`;
  }
}

/**
 * 🚀 執行所有測試的主函數
 */
function runAllFeatureModelTests() {
  console.log('🚀 ===== 執行所有新功能模型測試 =====');
  
  const tests = [
    { name: '配置完整性檢查', func: checkSpreadsheetConfigIntegrity },
    { name: 'Native Audio Dialog 配置', func: testNativeAudioDialogConfiguration },
    { name: '所有功能模型', func: testAllNewFeatureModels }
  ];
  
  const results = [];
  
  tests.forEach(test => {
    try {
      console.log(`\n🧪 開始測試: ${test.name}`);
      const result = test.func();
      results.push({ name: test.name, result: result, status: 'success' });
      console.log(`✅ ${test.name} 完成`);
    } catch (error) {
      console.error(`❌ ${test.name} 失敗:`, error);
      results.push({ name: test.name, result: error.message, status: 'failed' });
    }
  });
  
  console.log('\n📊 ===== 測試結果總結 =====');
  results.forEach(result => {
    const icon = result.status === 'success' ? '✅' : '❌';
    console.log(`${icon} ${result.name}: ${result.result}`);
  });
  
  const successCount = results.filter(r => r.status === 'success').length;
  console.log(`\n🎯 測試完成: ${successCount}/${results.length} 通過`);
  
  return results;
}
