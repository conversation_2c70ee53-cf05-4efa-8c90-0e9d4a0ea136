// == 新功能測試腳本 ==
// 🧪 測試 TTS 和圖像生成功能是否正常工作

/**
 * 🧪 完整的新功能測試套件
 */
function runNewFeatureTests() {
  console.log('🧪 === 開始測試新功能 ===');
  
  const results = {
    promptManager: false,
    ttsFunction: false,
    imageGenFunction: false,
    smartProcessor: false,
    integration: false
  };
  
  try {
    // 測試 1: 提示詞管理系統
    console.log('\n🎯 測試 1: 提示詞管理系統');
    if (typeof callAIWithPrompt === 'function') {
      console.log('✅ callAIWithPrompt 函數存在');
      results.promptManager = true;
    } else {
      console.log('❌ callAIWithPrompt 函數不存在');
    }
    
    // 測試 2: TTS 功能
    console.log('\n🔊 測試 2: TTS 功能');
    if (typeof textToSpeechWithGemini === 'function') {
      console.log('✅ textToSpeechWithGemini 函數存在');
      results.ttsFunction = true;
    } else {
      console.log('❌ textToSpeechWithGemini 函數不存在');
    }
    
    // 測試 3: 圖像生成功能
    console.log('\n🎨 測試 3: 圖像生成功能');
    if (typeof generateImageWithGemini === 'function') {
      console.log('✅ generateImageWithGemini 函數存在');
      results.imageGenFunction = true;
    } else {
      console.log('❌ generateImageWithGemini 函數不存在');
    }
    
    // 測試 4: 智能處理中樞
    console.log('\n🧠 測試 4: 智能處理中樞');
    if (typeof smartProcessorHub === 'function') {
      console.log('✅ smartProcessorHub 函數存在');
      results.smartProcessor = true;
    } else {
      console.log('❌ smartProcessorHub 函數不存在');
    }
    
    // 測試 5: 整合測試
    console.log('\n🔗 測試 5: 整合測試');
    if (typeof handleAITTSRequest === 'function' && typeof handleAIImageGeneration === 'function') {
      console.log('✅ AI 處理函數整合完成');
      results.integration = true;
    } else {
      console.log('❌ AI 處理函數整合不完整');
    }
    
    // 測試結果總結
    console.log('\n📊 === 測試結果總結 ===');
    const passedTests = Object.values(results).filter(result => result).length;
    const totalTests = Object.keys(results).length;
    
    console.log(`通過測試: ${passedTests}/${totalTests}`);
    
    Object.entries(results).forEach(([testName, passed]) => {
      const status = passed ? '✅' : '❌';
      console.log(`${status} ${testName}`);
    });
    
    if (passedTests === totalTests) {
      console.log('\n🎉 所有測試通過！新功能已準備就緒');
      return '所有測試通過';
    } else {
      console.log('\n⚠️ 部分測試失敗，請檢查相關模組');
      return `${passedTests}/${totalTests} 測試通過`;
    }
    
  } catch (error) {
    console.error('❌ 測試過程中發生錯誤:', error);
    return `測試失敗: ${error.message}`;
  }
}

/**
 * 🔧 檢查配置是否完整
 */
function checkNewFeatureConfig() {
  console.log('🔧 === 檢查新功能配置 ===');
  
  try {
    const config = getConfig();
    
    // 檢查必要的配置
    const requiredConfigs = [
      { key: 'geminiApiKey', name: 'Gemini API Key' },
      { key: 'folderId', name: 'Drive 資料夾 ID' }
    ];
    
    let allConfigsPresent = true;
    
    requiredConfigs.forEach(({ key, name }) => {
      if (config[key] && config[key] !== '') {
        console.log(`✅ ${name}: 已配置`);
      } else {
        console.log(`❌ ${name}: 未配置或為空`);
        allConfigsPresent = false;
      }
    });
    
    // 檢查模型配置
    console.log('\n🤖 檢查模型配置:');
    const models = [
      'gemini-2.5-flash-preview-tts',
      'gemini-2.5-pro-preview-tts',
      'gemini-2.0-flash-preview-image-generation'
    ];
    
    models.forEach(model => {
      try {
        const apiVersion = getModelApiVersion(model);
        console.log(`✅ ${model}: API 版本 ${apiVersion}`);
      } catch (error) {
        console.log(`❌ ${model}: 配置錯誤 - ${error.message}`);
        allConfigsPresent = false;
      }
    });
    
    if (allConfigsPresent) {
      console.log('\n🎉 所有配置檢查通過！');
      return '配置完整';
    } else {
      console.log('\n⚠️ 部分配置缺失，請檢查設定');
      return '配置不完整';
    }
    
  } catch (error) {
    console.error('❌ 配置檢查失敗:', error);
    return `配置檢查失敗: ${error.message}`;
  }
}

/**
 * 🎯 模擬用戶輸入測試
 */
function testUserInputSimulation() {
  console.log('🎯 === 模擬用戶輸入測試 ===');
  
  const testInputs = [
    {
      input: '念出來：這是測試訊息',
      expectedIntent: 'text_to_speech',
      description: 'TTS 指令測試'
    },
    {
      input: '畫一張可愛的小貓',
      expectedIntent: 'image_generation',
      description: '圖像生成指令測試'
    },
    {
      input: '語音播放：歡迎使用新功能',
      expectedIntent: 'text_to_speech',
      description: 'TTS 替代指令測試'
    },
    {
      input: '生成圖片：美麗的夕陽',
      expectedIntent: 'image_generation',
      description: '圖像生成替代指令測試'
    }
  ];
  
  testInputs.forEach((test, index) => {
    try {
      console.log(`\n${index + 1}. ${test.description}`);
      console.log(`輸入: "${test.input}"`);
      
      // 測試意圖識別
      if (typeof callAIWithPrompt === 'function') {
        console.log('🧠 意圖識別: 可用');
      } else {
        console.log('❌ 意圖識別: 不可用');
      }
      
      // 測試文本提取
      if (test.expectedIntent === 'text_to_speech' && typeof extractTextForTTS === 'function') {
        console.log('🔍 TTS 文本提取: 可用');
      } else if (test.expectedIntent === 'image_generation' && typeof extractImagePrompt === 'function') {
        console.log('🔍 圖像描述提取: 可用');
      }
      
      console.log(`✅ 測試完成`);
      
    } catch (error) {
      console.log(`❌ 測試失敗: ${error.message}`);
    }
  });
  
  console.log('\n🎉 用戶輸入模擬測試完成');
}

/**
 * 🚀 快速功能驗證
 */
function quickFeatureCheck() {
  console.log('🚀 === 快速功能驗證 ===');
  
  const checks = [
    { name: 'PromptManager.gs', func: 'callAIWithPrompt' },
    { name: 'GeminiAdvanced.gs', func: 'textToSpeechWithGemini' },
    { name: 'GeminiAdvanced.gs', func: 'generateImageWithGemini' },
    { name: 'TextProcessor_AIFirst.gs', func: 'handleAITTSRequest' },
    { name: 'TextProcessor_AIFirst.gs', func: 'handleAIImageGeneration' }
  ];
  
  let passedChecks = 0;
  
  checks.forEach(check => {
    if (typeof this[check.func] === 'function') {
      console.log(`✅ ${check.name} - ${check.func}`);
      passedChecks++;
    } else {
      console.log(`❌ ${check.name} - ${check.func} (缺失)`);
    }
  });
  
  console.log(`\n📊 功能檢查結果: ${passedChecks}/${checks.length}`);
  
  if (passedChecks === checks.length) {
    console.log('🎉 所有核心功能已就緒！');
    return true;
  } else {
    console.log('⚠️ 部分功能缺失，請檢查模組載入');
    return false;
  }
}

/**
 * 🎯 主測試函數 - 執行所有測試
 */
function testAllNewFeatures() {
  console.log('🎯 === 執行完整的新功能測試套件 ===\n');
  
  const results = {};
  
  // 1. 快速功能檢查
  results.quickCheck = quickFeatureCheck();
  
  // 2. 配置檢查
  results.configCheck = checkNewFeatureConfig();
  
  // 3. 完整功能測試
  results.fullTest = runNewFeatureTests();
  
  // 4. 用戶輸入模擬
  testUserInputSimulation();
  
  console.log('\n🏁 === 測試套件執行完成 ===');
  console.log('結果摘要:');
  console.log(`• 快速檢查: ${results.quickCheck ? '通過' : '失敗'}`);
  console.log(`• 配置檢查: ${results.configCheck}`);
  console.log(`• 完整測試: ${results.fullTest}`);
  
  return results;
}

/**
 * 🧪 測試 TTS 功能 (修復版)
 */
function testTTSFixed() {
  console.log('🧪 測試 TTS 功能 (修復版)...');

  const testText = '你好！這是一個測試語音轉換功能。';
  const result = textToSpeechWithGemini(testText);

  if (result.success) {
    console.log('✅ TTS 測試成功:', result);
    if (result.note) {
      console.log('📝 注意:', result.note);
    }
  } else {
    console.log('❌ TTS 測試失敗:', result.error);
  }

  return result;
}

/**
 * 🧪 測試圖像生成功能 (修復版)
 */
function testImageGenerationFixed() {
  console.log('🧪 測試圖像生成功能 (修復版)...');

  const testPrompt = '一隻可愛的貓咪坐在彩虹上';
  const result = generateImageWithGemini(testPrompt);

  if (result.success) {
    console.log('✅ 圖像生成測試成功:', result);
    if (result.note) {
      console.log('📝 注意:', result.note);
    }
  } else {
    console.log('❌ 圖像生成測試失敗:', result.error);
  }

  return result;
}

/**
 * 🧪 測試修復後的 TTS 和圖像生成功能
 */
function testFixedGeminiFeatures() {
  console.log('🧪 === 測試修復後的 Gemini 進階功能 ===');

  try {
    // 測試 TTS 功能
    console.log('\n🔊 測試修復後的 TTS 功能...');
    const ttsResult = textToSpeechWithGemini('你好！這是測試語音轉換功能。');

    if (ttsResult.success) {
      console.log('✅ TTS 測試成功:', ttsResult);
    } else {
      console.log('❌ TTS 測試失敗:', ttsResult.error);
    }

    // 測試圖像生成功能
    console.log('\n🎨 測試修復後的圖像生成功能...');
    const imageResult = generateImageWithGemini('一隻可愛的貓咪坐在彩虹上');

    if (imageResult.success) {
      console.log('✅ 圖像生成測試成功:', imageResult);
    } else {
      console.log('❌ 圖像生成測試失敗:', imageResult.error);
    }

    console.log('\n🎉 修復後功能測試完成！');

    return {
      tts: ttsResult,
      imageGeneration: imageResult
    };

  } catch (error) {
    console.error('❌ 測試過程中發生錯誤:', error);
    return {
      error: error.message
    };
  }
}
