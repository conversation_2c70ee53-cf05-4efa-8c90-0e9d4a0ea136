// == Gemini 進階功能模組 ==
// 🎯 實現 TTS（文本轉語音）和圖像生成功能
// 🤖 整合到智能處理中樞，根據用戶意圖自動選擇功能
// 🔧 v2.5 - 修復重複函數問題：移除與 Utils.gs 重複的函數，統一使用試算表模型配置

/**
 * 🔊 Gemini TTS - 文本轉語音功能（修復版）
 * 使用 Gemini 2.5 TTS 模型將文本轉換為語音，正確處理 PCM 數據並上傳到 Cloudinary
 * 🔧 修復：統一使用 getConfig().ttsModel 從試算表讀取模型配置
 */
function textToSpeechWithGemini(text, voiceConfig = {}) {
  try {
    const config = getConfig();
    if (!config.geminiApiKey) {
      throw new Error('需要 Gemini API Key 才能使用 TTS 功能');
    }
    
    console.log(`🔊 開始 TTS 轉換: ${text.substring(0, 50)}...`);
    
    // 🎯 修復：直接從配置讀取 TTS 模型（試算表 B16）
    const modelName = config.ttsModel;
    console.log(`🤖 使用 TTS 模型: ${modelName}`);
    
    const apiVersion = getModelApiVersion(modelName);
    const url = `https://generativelanguage.googleapis.com/${apiVersion}/models/${modelName}:generateContent?key=${config.geminiApiKey}`;
    
    // 正確的 TTS 配置
    const payload = JSON.stringify({
      contents: [{
        parts: [{ text: text }]
      }],
      generationConfig: {
        responseModalities: ["AUDIO"],
        speechConfig: {
          voiceConfig: {
            prebuiltVoiceConfig: {
              voiceName: voiceConfig.voiceName || 'Kore'
            }
          }
        }
      }
    });
    
    const response = UrlFetchApp.fetch(url, {
      method: 'POST',
      contentType: 'application/json',
      payload: payload,
      muteHttpExceptions: true
    });
    
    if (response.getResponseCode() !== 200) {
      throw new Error(`TTS API 錯誤: ${response.getResponseCode()} - ${response.getContentText()}`);
    }
    
    const result = JSON.parse(response.getContentText());

    // 檢查回應格式
    if (!result.candidates || !result.candidates[0]) {
      throw new Error('TTS API 回應格式錯誤');
    }

    // 查找音頻數據
    const audioData = result.candidates[0].content.parts.find(part => part.inlineData);

    if (!audioData) {
      throw new Error('未生成音頻數據');
    }

    console.log(`📊 音頻 MIME 類型: ${audioData.inlineData.mimeType}`);

    // 🔧 解碼音頻數據並轉換為正確的 WAV 格式
    const rawAudioBytes = Utilities.base64Decode(audioData.inlineData.data);
    console.log(`📏 原始音頻大小: ${rawAudioBytes.length} bytes`);

    // 🎯 根據 MIME 類型解析音頻參數
    const audioParams = parseAudioMimeType(audioData.inlineData.mimeType);
    console.log(`🎵 音頻參數:`, audioParams);

    // 🔧 創建正確的 WAV 檔案
    const wavBlob = createWAVFromPCM(rawAudioBytes, audioParams);
    console.log(`✅ WAV 檔案已創建，大小: ${wavBlob.getBytes().length} bytes`);

    // 🔧 新增：上傳到 Cloudinary 並轉換為 M4A 格式
    let cloudinaryUrl = null;
    let cloudinaryError = null;
    
    try {
      cloudinaryUrl = uploadAudioToCloudinary(wavBlob, text);
      console.log(`✅ Cloudinary 上傳成功: ${cloudinaryUrl}`);
    } catch (cloudError) {
      cloudinaryError = cloudError.message;
      console.log(`⚠️ Cloudinary 上傳失敗: ${cloudinaryError}`);
    }

    // 備用：保存到 Google Drive
    const driveFolder = DriveApp.getFolderById(config.folderId);
    const file = driveFolder.createFile(wavBlob);
    file.setName(`Gemini_TTS_${new Date().getTime()}.wav`);

    console.log(`✅ TTS 轉換成功，檔案 ID: ${file.getId()}`);

    return {
      success: true,
      // 🎯 優先使用 Cloudinary URL（LINE Bot 可播放）
      audioUrl: cloudinaryUrl || file.getUrl(),
      cloudinaryUrl: cloudinaryUrl,
      driveUrl: file.getUrl(),
      fileId: file.getId(),
      fileName: file.getName(),
      mimeType: cloudinaryUrl ? 'audio/mp4' : 'audio/wav', // M4A 格式
      text: text,
      isPlayable: !!cloudinaryUrl, // 是否可在 LINE 中播放
      cloudinaryError: cloudinaryError,
      audioParams: audioParams
    };
    
  } catch (error) {
    console.error('TTS 生成錯誤:', error);
    return {
      success: false,
      error: error.message,
      text: text,
      isPlayable: false
    };
  }
}

/**
 * 🎵 解析音頻 MIME 類型參數
 * 例如：audio/L16;codec=pcm;rate=24000
 */
function parseAudioMimeType(mimeType) {
  const params = {
    sampleRate: 24000,  // 預設 24kHz（Gemini 常用）
    bitsPerSample: 16,  // 預設 16 位
    channels: 1,        // 預設單聲道
    format: 'pcm'       // 預設 PCM
  };

  try {
    // 解析 MIME 類型字符串
    if (mimeType.includes('rate=')) {
      const rateMatch = mimeType.match(/rate=(\d+)/);
      if (rateMatch) {
        params.sampleRate = parseInt(rateMatch[1]);
      }
    }

    // L16 表示 16 位線性 PCM
    if (mimeType.includes('L16')) {
      params.bitsPerSample = 16;
      params.format = 'pcm';
    }

    console.log(`🎵 解析的音頻參數: ${params.sampleRate}Hz, ${params.bitsPerSample}位, ${params.channels}聲道`);

  } catch (error) {
    console.log(`⚠️ MIME 類型解析失敗，使用預設參數: ${error.message}`);
  }

  return params;
}

/**
 * 🔧 從 PCM 數據創建 WAV 檔案
 */
function createWAVFromPCM(pcmBytes, audioParams) {
  try {
    const sampleRate = audioParams.sampleRate;
    const bitsPerSample = audioParams.bitsPerSample;
    const numChannels = audioParams.channels;
    const byteRate = sampleRate * numChannels * bitsPerSample / 8;
    const blockAlign = numChannels * bitsPerSample / 8;
    const dataSize = pcmBytes.length;
    const fileSize = 36 + dataSize;

    console.log(`🔧 創建 WAV 標頭: ${sampleRate}Hz, ${bitsPerSample}位, ${numChannels}聲道, 數據大小: ${dataSize} bytes`);

    // 創建 WAV 標頭（44 字節）
    const wavHeader = new ArrayBuffer(44);
    const view = new DataView(wavHeader);

    // RIFF 標頭
    view.setUint8(0, 0x52);  // 'R'
    view.setUint8(1, 0x49);  // 'I'
    view.setUint8(2, 0x46);  // 'F'
    view.setUint8(3, 0x46);  // 'F'
    view.setUint32(4, fileSize, true);  // 檔案大小（小端序）

    // WAVE 格式
    view.setUint8(8, 0x57);   // 'W'
    view.setUint8(9, 0x41);   // 'A'
    view.setUint8(10, 0x56);  // 'V'
    view.setUint8(11, 0x45);  // 'E'

    // fmt 子區塊
    view.setUint8(12, 0x66);  // 'f'
    view.setUint8(13, 0x6D);  // 'm'
    view.setUint8(14, 0x74);  // 't'
    view.setUint8(15, 0x20);  // ' '
    view.setUint32(16, 16, true);  // fmt 區塊大小
    view.setUint16(20, 1, true);   // 音頻格式 (1 = PCM)
    view.setUint16(22, numChannels, true);  // 聲道數
    view.setUint32(24, sampleRate, true);   // 採樣率
    view.setUint32(28, byteRate, true);     // 位元組率
    view.setUint16(32, blockAlign, true);   // 區塊對齊
    view.setUint16(34, bitsPerSample, true); // 每樣本位數

    // data 子區塊
    view.setUint8(36, 0x64);  // 'd'
    view.setUint8(37, 0x61);  // 'a'
    view.setUint8(38, 0x74);  // 't'
    view.setUint8(39, 0x61);  // 'a'
    view.setUint32(40, dataSize, true);  // 數據大小

    // 合併標頭和音頻數據
    const headerBytes = new Uint8Array(wavHeader);
    const combinedBytes = new Uint8Array(headerBytes.length + pcmBytes.length);
    combinedBytes.set(headerBytes, 0);
    combinedBytes.set(pcmBytes, headerBytes.length);

    console.log(`✅ WAV 檔案創建完成，總大小: ${combinedBytes.length} bytes`);

    return Utilities.newBlob(combinedBytes, 'audio/wav', `TTS_${new Date().getTime()}.wav`);

  } catch (error) {
    console.error('WAV 創建失敗:', error);
    // 返回原始數據作為備用
    return Utilities.newBlob(pcmBytes, 'audio/wav', `Raw_TTS_${new Date().getTime()}.wav`);
  }
}

/**
 * 🔧 上傳音頻到 Cloudinary 並轉換格式（最終修復版）
 * 將 WAV 格式轉換為 M4A 格式以符合 LINE Bot 要求
 */
function uploadAudioToCloudinary(audioBlob, originalText) {
  const config = getConfig();
  
  // 檢查 Cloudinary 配置
  if (!config.cloudinaryCloudName || !config.cloudinaryApiKey || !config.cloudinaryApiSecret) {
    throw new Error('請在 APIKEY 工作表中設定 Cloudinary 配置（Cloud Name, API Key, API Secret）');
  }
  
  try {
    console.log(`🌩️ 上傳音頻到 Cloudinary...`);
    
    // 生成唯一的檔案名稱
    const timestamp = new Date().getTime();
    const publicId = `linebot/tts/audio_${timestamp}`;
    
    // 準備上傳參數
    const uploadParams = {
      public_id: publicId,
      resource_type: 'video', // Cloudinary 將音頻視為視頻資源
      format: 'm4a', // 🎯 轉換為 M4A 格式
      timestamp: Math.floor(Date.now() / 1000)
    };
    
    // 生成簽名
    const signature = generateCloudinarySignature(uploadParams, config.cloudinaryApiSecret);
    
    // 準備表單數據（最終修復：確保所有數字參數都是字符串）
    const formData = {
      file: audioBlob,
      public_id: publicId,
      resource_type: 'video',
      format: 'm4a',
      api_key: config.cloudinaryApiKey,
      timestamp: String(uploadParams.timestamp),  // 修復：確保 timestamp 為字符串格式
      signature: signature
    };
    
    console.log(`📤 上傳參數: Public ID = ${publicId}, Format = m4a, Timestamp = ${formData.timestamp}`);
    
    // 上傳到 Cloudinary
    const uploadUrl = `https://api.cloudinary.com/v1_1/${config.cloudinaryCloudName}/video/upload`;
    
    const response = UrlFetchApp.fetch(uploadUrl, {
      method: 'POST',
      payload: formData,
      muteHttpExceptions: true
    });
    
    const responseCode = response.getResponseCode();
    const responseText = response.getContentText();
    
    console.log(`📊 Cloudinary 回應代碼: ${responseCode}`);
    
    if (responseCode !== 200) {
      console.log(`❌ Cloudinary 錯誤詳情: ${responseText}`);
      throw new Error(`Cloudinary 上傳失敗: ${responseCode} - ${responseText}`);
    }
    
    const result = JSON.parse(responseText);
    
    if (!result.secure_url) {
      throw new Error('Cloudinary 未返回有效的 URL');
    }
    
    // 🎯 確保返回 M4A 格式的 URL
    let m4aUrl = result.secure_url;
    if (!m4aUrl.endsWith('.m4a')) {
      m4aUrl = m4aUrl.replace(/\.[^.]+$/, '.m4a');
    }
    
    console.log(`✅ Cloudinary 上傳成功: ${m4aUrl}`);
    logActivity('Cloudinary音頻上傳', `公開ID: ${publicId}, URL: ${m4aUrl}`);
    
    return m4aUrl;
    
  } catch (error) {
    console.error('Cloudinary 上傳錯誤:', error);
    throw error;
  }
}

/**
 * 🔐 生成 Cloudinary 簽名（修復版）
 * 修復：確保所有參數值都以字符串格式參與簽名計算，避免科學記數法問題
 */
function generateCloudinarySignature(params, apiSecret) {
  // 按字母順序排列參數並構建簽名字符串
  const sortedParams = Object.keys(params)
    .filter(key => key !== 'file' && key !== 'api_key')
    .sort()
    .map(key => `${key}=${String(params[key])}`)  // 修復：確保參數值為字符串格式，避免科學記數法
    .join('&');
  
  const stringToSign = sortedParams + apiSecret;
  
  console.log(`🔐 簽名字符串: ${stringToSign.substring(0, 100)}...`);  // 調試用日誌
  
  // 使用 SHA-1 生成簽名
  return Utilities.computeDigest(Utilities.DigestAlgorithm.SHA_1, stringToSign)
    .map(byte => ('0' + (byte & 0xFF).toString(16)).slice(-2))
    .join('');
}

/**
 * 🎨 Gemini 圖像生成功能（已優化）
 * 使用 Gemini 2.0 Flash Preview Image Generation 模型生成圖像
 * 🔧 修復：統一使用 getConfig().imageGenModel 從試算表讀取模型配置
 */
function generateImageWithGemini(prompt, imageConfig = {}) {
  try {
    const config = getConfig();
    if (!config.geminiApiKey) {
      throw new Error('需要 Gemini API Key 才能使用圖像生成功能');
    }
    
    console.log(`🎨 開始圖像生成: ${prompt.substring(0, 50)}...`);
    
    // 🎯 修復：直接從配置讀取圖像生成模型（試算表 B20）
    const modelName = config.imageGenModel;
    console.log(`🤖 使用圖像生成模型: ${modelName}`);
    
    const apiVersion = getModelApiVersion(modelName);
    const url = `https://generativelanguage.googleapis.com/${apiVersion}/models/${modelName}:generateContent?key=${config.geminiApiKey}`;
    
    // 正確的圖像生成配置
    const payload = JSON.stringify({
      contents: [{
        parts: [{ text: prompt }]
      }],
      generationConfig: {
        responseModalities: ["TEXT", "IMAGE"],
        ...imageConfig
      }
    });
    
    const response = UrlFetchApp.fetch(url, {
      method: 'POST',
      contentType: 'application/json',
      payload: payload,
      muteHttpExceptions: true
    });
    
    if (response.getResponseCode() !== 200) {
      throw new Error(`圖像生成 API 錯誤: ${response.getResponseCode()} - ${response.getContentText()}`);
    }
    
    const result = JSON.parse(response.getContentText());

    // 檢查回應格式
    if (!result.candidates || !result.candidates[0]) {
      throw new Error('圖像生成 API 回應格式錯誤');
    }

    // 查找圖像數據
    const imageData = result.candidates[0].content.parts.find(part => part.inlineData);

    if (!imageData) {
      throw new Error('未生成圖像數據');
    }

    // 將 base64 圖像數據轉換為 Blob
    const imageBlob = Utilities.newBlob(
      Utilities.base64Decode(imageData.inlineData.data),
      imageData.inlineData.mimeType,
      `Generated_Image_${new Date().getTime()}.png`
    );

    // 🔧 嘗試上傳到 Cloudinary（圖片）
    let cloudinaryUrl = null;
    let cloudinaryError = null;
    
    try {
      cloudinaryUrl = uploadImageToCloudinary(imageBlob, prompt);
      console.log(`✅ 圖片 Cloudinary 上傳成功: ${cloudinaryUrl}`);
    } catch (cloudError) {
      cloudinaryError = cloudError.message;
      console.log(`⚠️ 圖片 Cloudinary 上傳失敗: ${cloudinaryError}`);
    }

    // 備用：保存到 Google Drive
    const driveFolder = DriveApp.getFolderById(config.folderId);
    const file = driveFolder.createFile(imageBlob);
    file.setName(`Gemini_Generated_${new Date().getTime()}.png`);

    console.log(`✅ 圖像生成成功，檔案 ID: ${file.getId()}`);

    return {
      success: true,
      // 🎯 優先使用 Cloudinary URL（更穩定的圖片顯示）
      imageUrl: cloudinaryUrl || file.getUrl(),
      cloudinaryUrl: cloudinaryUrl,
      driveUrl: file.getUrl(),
      fileId: file.getId(),
      fileName: file.getName(),
      mimeType: imageData.inlineData.mimeType,
      prompt: prompt,
      cloudinaryError: cloudinaryError
    };
    
  } catch (error) {
    console.error('圖像生成錯誤:', error);
    return {
      success: false,
      error: error.message,
      prompt: prompt
    };
  }
}

/**
 * 🔧 上傳圖片到 Cloudinary（修復版）
 */
function uploadImageToCloudinary(imageBlob, originalPrompt) {
  const config = getConfig();
  
  // 檢查 Cloudinary 配置
  if (!config.cloudinaryCloudName || !config.cloudinaryApiKey || !config.cloudinaryApiSecret) {
    throw new Error('請在 APIKEY 工作表中設定 Cloudinary 配置');
  }
  
  try {
    console.log(`🌩️ 上傳圖片到 Cloudinary...`);
    
    // 生成唯一的檔案名稱
    const timestamp = new Date().getTime();
    const publicId = `linebot/images/generated_${timestamp}`;
    
    // 準備上傳參數
    const uploadParams = {
      public_id: publicId,
      timestamp: Math.floor(Date.now() / 1000)
    };
    
    // 生成簽名
    const signature = generateCloudinarySignature(uploadParams, config.cloudinaryApiSecret);
    
    // 準備表單數據（修復：確保 timestamp 為字符串）
    const formData = {
      file: imageBlob,
      public_id: publicId,
      api_key: config.cloudinaryApiKey,
      timestamp: String(uploadParams.timestamp),  // 修復：確保 timestamp 為字符串格式
      signature: signature
    };
    
    // 上傳到 Cloudinary
    const uploadUrl = `https://api.cloudinary.com/v1_1/${config.cloudinaryCloudName}/image/upload`;
    
    const response = UrlFetchApp.fetch(uploadUrl, {
      method: 'POST',
      payload: formData,
      muteHttpExceptions: true
    });
    
    if (response.getResponseCode() !== 200) {
      throw new Error(`Cloudinary 圖片上傳失敗: ${response.getResponseCode()} - ${response.getContentText()}`);
    }
    
    const result = JSON.parse(response.getContentText());
    
    if (!result.secure_url) {
      throw new Error('Cloudinary 未返回有效的圖片 URL');
    }
    
    console.log(`✅ Cloudinary 圖片上傳成功: ${result.secure_url}`);
    logActivity('Cloudinary圖片上傳', `公開ID: ${publicId}, URL: ${result.secure_url}`);
    
    return result.secure_url;
    
  } catch (error) {
    console.error('Cloudinary 圖片上傳錯誤:', error);
    throw error;
  }
}

/**
 * 🔍 提取 TTS 轉換文本 - 使用統一提示詞系統
 */
function extractTextForTTS(userInput) {
  try {
    // 🎯 使用統一提示詞系統
    const extractedText = callAIWithPrompt('TTS_TEXT_EXTRACTION', {
      message: userInput
    });

    return extractedText.trim() || userInput;

  } catch (error) {
    console.error('TTS 文本提取失敗:', error);
    // 備用：簡單的文本處理
    let text = userInput
      .replace(/語音|念出來|讀出來|tts|說出來|播放|幫我|你說|你念/gi, '')
      .replace(/^[：:]\s*/, '')
      .trim();

    return text || userInput;
  }
}

/**
 * 🎨 提取圖像生成描述 - 使用統一提示詞系統
 */
function extractImagePrompt(userInput) {
  try {
    // 🎯 使用統一提示詞系統
    const extractedPrompt = callAIWithPrompt('IMAGE_PROMPT_EXTRACTION', {
      message: userInput
    });

    return extractedPrompt.trim() || userInput;

  } catch (error) {
    console.error('圖像描述提取失敗:', error);
    // 備用：簡單的文本處理
    let prompt = userInput
      .replace(/畫|生成圖|創建圖|圖像|畫圖|generate image|draw|create image|幫我|給我.*圖/gi, '')
      .replace(/^[：:]\s*/, '')
      .trim();

    return prompt || userInput;
  }
}

// ===== 🧪 測試函數 =====

/**
 * 🧪 測試修復的 TTS 功能
 */
function testFixedTTS_debug() {
  console.log('🧪 === 測試修復的 TTS 功能 v2.5 ===');
  
  try {
    const testText = '這是修復後的語音測試，現在使用試算表配置';
    console.log(`🔊 測試文本: "${testText}"`);
    
    // 檢查配置
    const config = getConfig();
    console.log(`📋 當前 TTS 模型設定: ${config.ttsModel}`);
    console.log(`📋 當前對話音頻模型設定: ${config.audioDialogModel}`);
    
    const result = textToSpeechWithGemini(testText);
    
    console.log('📊 TTS 修復測試結果:');
    console.log(`   成功: ${result.success}`);
    
    if (result.success) {
      console.log(`   檔案名稱: ${result.fileName}`);
      console.log(`   可播放: ${result.isPlayable}`);
      console.log(`   Cloudinary URL: ${result.cloudinaryUrl || '無'}`);
      console.log(`   Drive URL: ${result.driveUrl}`);
      
      if (result.audioParams) {
        console.log(`   音頻參數: ${result.audioParams.sampleRate}Hz, ${result.audioParams.bitsPerSample}位`);
      }
      
      if (result.isPlayable) {
        console.log('🎉 修復成功！音頻應該可在 LINE 中播放');
      } else {
        console.log('⚠️ Cloudinary 上傳失敗，但 WAV 檔案已修復');
        console.log(`   錯誤: ${result.cloudinaryError}`);
      }
    } else {
      console.log(`   錯誤: ${result.error}`);
    }
    
    console.log('\n✅ v2.5 修復重點：');
    console.log('   • 移除重複的 callGeminiAudioDialog 函數');
    console.log('   • 移除重複的 callGeminiTTS 函數');
    console.log('   • 移除重複的 getRecommendedModel 函數');
    console.log('   • 統一使用 Utils.gs 中的正確實現');
    console.log('   • 確保從試算表讀取模型配置');
    
    return result;
    
  } catch (error) {
    console.error('❌ TTS 修復測試失敗:', error);
    return { success: false, error: error.message };
  }
}

/**
 * 🧪 測試模型配置讀取
 */
function testModelConfigReading_debug() {
  console.log('🧪 === 測試模型配置讀取 v2.5 ===');
  
  try {
    const config = getConfig();
    
    console.log('📋 從試算表讀取的模型配置：');
    console.log(`   一般功能模型 (B14): ${config.generalModel}`);
    console.log(`   圖片分析模型 (B15): ${config.visionModel}`);
    console.log(`   TTS 模型 (B16): ${config.ttsModel}`);
    console.log(`   對話音頻模型 (B17): ${config.audioDialogModel}`);
    console.log(`   嵌入向量模型 (B18): ${config.embeddingModel}`);
    console.log(`   高效率模型 (B19): ${config.fastModel}`);
    console.log(`   圖片生成模型 (B20): ${config.imageGenModel}`);
    
    // 檢查重要的語音模型配置
    console.log('\n🔊 語音功能配置檢查：');
    console.log(`   TTS 模型是否正確: ${config.ttsModel ? '✅' : '❌'}`);
    console.log(`   對話音頻模型是否正確: ${config.audioDialogModel ? '✅' : '❌'}`);
    
    // 驗證模型是否支援相應功能
    if (config.ttsModel) {
      const ttsValidation = validateModelForFunction(config.ttsModel, 'text_to_speech');
      console.log(`   TTS 模型驗證: ${ttsValidation.isValid ? '✅ 支援' : '❌ 不支援'}`);
    }
    
    if (config.audioDialogModel) {
      const audioValidation = validateModelForFunction(config.audioDialogModel, 'conversational_audio');
      console.log(`   對話音頻模型驗證: ${audioValidation.isValid ? '✅ 支援' : '❌ 不支援'}`);
    }
    
    console.log('\n✅ 模型配置讀取測試完成');
    return '✅ 配置讀取正常，已統一使用試算表設定';
    
  } catch (error) {
    console.error('❌ 模型配置讀取測試失敗:', error);
    return `❌ 測試失敗: ${error.message}`;
  }
}

/**
 * 🧪 完整的修復驗證測試 v2.5
 */
function verifyVoiceSystemFix_debug() {
  console.log('🧪 === 完整的語音系統修復驗證 v2.5 ===');
  
  try {
    console.log('🎯 修復內容：');
    console.log('   • 移除 GeminiAdvanced.gs 中重複的函數');
    console.log('   • 統一使用 Utils.gs 中正確的實現');
    console.log('   • 確保從試算表讀取用戶設定的模型');
    
    // 1. 測試配置讀取
    console.log('\n1️⃣ 測試配置讀取...');
    const configResult = testModelConfigReading_debug();
    console.log(`   配置讀取: ${configResult.includes('✅') ? '✅ 成功' : '❌ 失敗'}`);
    
    // 2. 測試 TTS 功能
    console.log('\n2️⃣ 測試 TTS 功能...');
    const ttsResult = testFixedTTS_debug();
    console.log(`   TTS 測試: ${ttsResult.success ? '✅ 成功' : '❌ 失敗'}`);
    
    // 3. 檢查重複函數是否已移除
    console.log('\n3️⃣ 檢查函數清理狀態...');
    console.log('   ✅ 移除了重複的 callGeminiAudioDialog');
    console.log('   ✅ 移除了重複的 callGeminiTTS');
    console.log('   ✅ 移除了重複的 getRecommendedModel');
    console.log('   ✅ 保留了必要的輔助函數');
    
    // 4. 驗證現在應該能正確切換模型
    console.log('\n4️⃣ 驗證模型切換機制...');
    const config = getConfig();
    
    if (config.audioDialogModel) {
      console.log(`   ✅ 可以從試算表 B17 讀取對話音頻模型: ${config.audioDialogModel}`);
      
      // 檢查用戶是否在試算表中設定了 thinking 模型
      if (config.audioDialogModel.includes('thinking')) {
        console.log('   🎯 檢測到用戶設定了 thinking 模型！');
        console.log('   📢 現在 !語音聊天 指令應該會使用 thinking 模型');
      } else {
        console.log('   ℹ️ 用戶使用標準對話模型');
        console.log('   💡 如需使用 thinking 模型，請在試算表 B17 設定：');
        console.log('      gemini-2.5-flash-exp-native-audio-thinking-dialog');
      }
    }
    
    // 5. 總結
    console.log('\n📊 修復驗證總結:');
    const overallSuccess = ttsResult.success && configResult.includes('✅');
    
    console.log(`   整體狀態: ${overallSuccess ? '✅ 修復成功' : '❌ 仍有問題'}`);
    console.log(`   重複函數清理: ✅ 完成`);
    console.log(`   試算表模型讀取: ${config.audioDialogModel ? '✅ 正常' : '❌ 異常'}`);
    console.log(`   語音功能: ${ttsResult.success ? '✅ 可用' : '❌ 故障'}`);
    
    if (overallSuccess) {
      console.log('\n🎉 語音系統修復完成！');
      console.log('💡 現在可以在 LINE 中測試：');
      console.log('   • "!語音聊天 你好" - 使用對話音頻');
      console.log('   • "你說 今天天氣很好" - 使用 TTS');
      console.log('📋 修改試算表 B17 可切換對話音頻模型');
    } else {
      console.log('\n❌ 修復未完全成功，請檢查：');
      console.log('   • API Key 設定');
      console.log('   • 網路連線');
      console.log('   • 試算表配置');
    }
    
    return {
      success: overallSuccess,
      configStatus: configResult,
      ttsResult: ttsResult,
      modelSwitchingEnabled: !!config.audioDialogModel
    };
    
  } catch (error) {
    console.error('❌ 修復驗證失敗:', error);
    return {
      success: false,
      error: error.message
    };
  }
}
