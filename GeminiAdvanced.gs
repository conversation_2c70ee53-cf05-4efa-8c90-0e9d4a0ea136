// == Gemini 進階功能模組 ==
// 🎯 實現 TTS（文本轉語音）和圖像生成功能
// 🤖 整合到智能處理中樞，根據用戶意圖自動選擇功能
// 🔧 v2.4 - 修復語音功能：添加缺失的函數實現

/**
 * 🎙️ Gemini Native Audio Dialog - 對話音頻功能
 * 使用 Native Audio Dialog 模型進行智能對話音頻生成
 */
function callGeminiAudioDialog(userMessage, options = {}) {
  try {
    console.log(`🎙️ 調用 Native Audio Dialog: ${userMessage.substring(0, 50)}...`);
    
    const config = getConfig();
    if (!config.geminiApiKey) {
      throw new Error('需要 Gemini API Key 才能使用對話音頻功能');
    }
    
    // 🎯 使用功能分類系統獲取對話音頻模型
    const modelName = getRecommendedModel('audio_dialog');
    console.log(`🤖 使用模型: ${modelName}`);
    
    const apiVersion = getModelApiVersion(modelName);
    const url = `https://generativelanguage.googleapis.com/${apiVersion}/models/${modelName}:generateContent?key=${config.geminiApiKey}`;
    
    // 構建對話音頻請求
    const payload = JSON.stringify({
      contents: [{
        parts: [{ text: userMessage }]
      }],
      generationConfig: {
        responseModalities: ["TEXT", "AUDIO"],
        speechConfig: {
          voiceConfig: {
            prebuiltVoiceConfig: {
              voiceName: options.voiceName || 'Kore'
            }
          }
        }
      }
    });
    
    const response = UrlFetchApp.fetch(url, {
      method: 'POST',
      contentType: 'application/json',
      payload: payload,
      muteHttpExceptions: true
    });
    
    if (response.getResponseCode() !== 200) {
      const errorText = response.getContentText();
      console.log(`❌ Native Audio Dialog API 錯誤: ${response.getResponseCode()} - ${errorText}`);
      throw new Error(`Native Audio Dialog API 錯誤: ${response.getResponseCode()} - ${errorText}`);
    }
    
    const result = JSON.parse(response.getContentText());
    
    // 檢查回應格式
    if (!result.candidates || !result.candidates[0]) {
      throw new Error('Native Audio Dialog API 回應格式錯誤');
    }
    
    const candidate = result.candidates[0];
    const parts = candidate.content.parts;
    
    // 尋找音頻和文字內容
    let audioData = null;
    let textResponse = '';
    
    parts.forEach(part => {
      if (part.inlineData && part.inlineData.mimeType.startsWith('audio/')) {
        audioData = part.inlineData;
      } else if (part.text) {
        textResponse += part.text;
      }
    });
    
    if (!audioData) {
      throw new Error('未生成音頻內容');
    }
    
    console.log(`📊 Native Audio Dialog 成功，音頻類型: ${audioData.mimeType}`);
    
    // 處理音頻數據（與 TTS 相同的處理流程）
    const rawAudioBytes = Utilities.base64Decode(audioData.data);
    const audioParams = parseAudioMimeType(audioData.mimeType);
    const wavBlob = createWAVFromPCM(rawAudioBytes, audioParams);
    
    // 上傳到 Cloudinary
    let cloudinaryUrl = null;
    let cloudinaryError = null;
    
    try {
      cloudinaryUrl = uploadAudioToCloudinary(wavBlob, userMessage);
      console.log(`✅ Native Audio Dialog Cloudinary 上傳成功: ${cloudinaryUrl}`);
    } catch (cloudError) {
      cloudinaryError = cloudError.message;
      console.log(`⚠️ Native Audio Dialog Cloudinary 上傳失敗: ${cloudinaryError}`);
    }
    
    // 備用：保存到 Google Drive
    const driveFolder = DriveApp.getFolderById(config.folderId);
    const file = driveFolder.createFile(wavBlob);
    file.setName(`Native_Audio_Dialog_${new Date().getTime()}.wav`);
    
    return {
      success: true,
      audioUrl: cloudinaryUrl || file.getUrl(),
      cloudinaryUrl: cloudinaryUrl,
      driveUrl: file.getUrl(),
      fileId: file.getId(),
      fileName: file.getName(),
      textResponse: textResponse,
      mimeType: cloudinaryUrl ? 'audio/mp4' : 'audio/wav',
      isPlayable: !!cloudinaryUrl,
      cloudinaryError: cloudinaryError,
      mode: 'native_audio_dialog'
    };
    
  } catch (error) {
    console.error('Native Audio Dialog 調用失敗:', error);
    return {
      success: false,
      error: error.message,
      mode: 'native_audio_dialog_failed'
    };
  }
}

/**
 * 🔊 Gemini TTS 簡化調用函數
 * 為了兼容現有代碼，提供簡化的 TTS 調用接口
 */
function callGeminiTTS(text, voiceConfig = {}) {
  try {
    // 直接使用現有的 TTS 函數
    return textToSpeechWithGemini(text, voiceConfig);
  } catch (error) {
    console.error('TTS 調用失敗:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 🔧 修復模型選擇邏輯
 * 確保與新的功能分類系統兼容
 */
function getRecommendedModel(category) {
  try {
    // 使用新的功能分類系統
    if (typeof getDefaultModelForCategory === 'function') {
      // 映射舊的類別名稱到新的功能分類
      const categoryMapping = {
        'tts': 'tts',
        'audio_dialog': 'audio_dialog',
        'image_generation': 'image_generation',
        'general': 'general',
        'vision': 'vision',
        'embedding': 'embedding',
        'cost_effective': 'cost_effective'
      };
      
      const mappedCategory = categoryMapping[category] || category;
      const recommendedModel = getDefaultModelForCategory(mappedCategory);
      
      if (recommendedModel) {
        console.log(`🤖 推薦模型 (${category}): ${recommendedModel}`);
        return recommendedModel;
      }
    }
    
    // 備用：使用硬編碼的預設值
    const fallbackModels = {
      'tts': 'gemini-2.5-flash-preview-tts',
      'audio_dialog': 'gemini-2.5-flash-preview-native-audio-dialog',
      'image_generation': 'gemini-2.0-flash-preview-image-generation',
      'general': 'gemini-2.5-flash',
      'vision': 'gemini-2.5-pro',
      'embedding': 'gemini-embedding-exp',
      'cost_effective': 'gemini-2.5-flash-lite-preview-06-17'
    };
    
    const fallbackModel = fallbackModels[category] || 'gemini-2.5-flash';
    console.log(`🔄 使用備用模型 (${category}): ${fallbackModel}`);
    return fallbackModel;
    
  } catch (error) {
    console.error('模型選擇失敗:', error);
    return 'gemini-2.5-flash'; // 最終備用
  }
}

/**
 * 🔊 Gemini TTS - 文本轉語音功能（修復版）
 * 使用 Gemini 2.5 TTS 模型將文本轉換為語音，正確處理 PCM 數據並上傳到 Cloudinary
 */
function textToSpeechWithGemini(text, voiceConfig = {}) {
  try {
    const config = getConfig();
    if (!config.geminiApiKey) {
      throw new Error('需要 Gemini API Key 才能使用 TTS 功能');
    }
    
    console.log(`🔊 開始 TTS 轉換: ${text.substring(0, 50)}...`);
    
    // 選擇 TTS 模型（使用修復的模型選擇函數）
    const modelName = getRecommendedModel('tts');
    const apiVersion = getModelApiVersion(modelName);
    const url = `https://generativelanguage.googleapis.com/${apiVersion}/models/${modelName}:generateContent?key=${config.geminiApiKey}`;
    
    // 正確的 TTS 配置
    const payload = JSON.stringify({
      contents: [{
        parts: [{ text: text }]
      }],
      generationConfig: {
        responseModalities: ["AUDIO"],
        speechConfig: {
          voiceConfig: {
            prebuiltVoiceConfig: {
              voiceName: voiceConfig.voiceName || 'Kore'
            }
          }
        }
      }
    });
    
    const response = UrlFetchApp.fetch(url, {
      method: 'POST',
      contentType: 'application/json',
      payload: payload,
      muteHttpExceptions: true
    });
    
    if (response.getResponseCode() !== 200) {
      throw new Error(`TTS API 錯誤: ${response.getResponseCode()} - ${response.getContentText()}`);
    }
    
    const result = JSON.parse(response.getContentText());

    // 檢查回應格式
    if (!result.candidates || !result.candidates[0]) {
      throw new Error('TTS API 回應格式錯誤');
    }

    // 查找音頻數據
    const audioData = result.candidates[0].content.parts.find(part => part.inlineData);

    if (!audioData) {
      throw new Error('未生成音頻數據');
    }

    console.log(`📊 音頻 MIME 類型: ${audioData.inlineData.mimeType}`);

    // 🔧 解碼音頻數據並轉換為正確的 WAV 格式
    const rawAudioBytes = Utilities.base64Decode(audioData.inlineData.data);
    console.log(`📏 原始音頻大小: ${rawAudioBytes.length} bytes`);

    // 🎯 根據 MIME 類型解析音頻參數
    const audioParams = parseAudioMimeType(audioData.inlineData.mimeType);
    console.log(`🎵 音頻參數:`, audioParams);

    // 🔧 創建正確的 WAV 檔案
    const wavBlob = createWAVFromPCM(rawAudioBytes, audioParams);
    console.log(`✅ WAV 檔案已創建，大小: ${wavBlob.getBytes().length} bytes`);

    // 🔧 新增：上傳到 Cloudinary 並轉換為 M4A 格式
    let cloudinaryUrl = null;
    let cloudinaryError = null;
    
    try {
      cloudinaryUrl = uploadAudioToCloudinary(wavBlob, text);
      console.log(`✅ Cloudinary 上傳成功: ${cloudinaryUrl}`);
    } catch (cloudError) {
      cloudinaryError = cloudError.message;
      console.log(`⚠️ Cloudinary 上傳失敗: ${cloudinaryError}`);
    }

    // 備用：保存到 Google Drive
    const driveFolder = DriveApp.getFolderById(config.folderId);
    const file = driveFolder.createFile(wavBlob);
    file.setName(`Gemini_TTS_${new Date().getTime()}.wav`);

    console.log(`✅ TTS 轉換成功，檔案 ID: ${file.getId()}`);

    return {
      success: true,
      // 🎯 優先使用 Cloudinary URL（LINE Bot 可播放）
      audioUrl: cloudinaryUrl || file.getUrl(),
      cloudinaryUrl: cloudinaryUrl,
      driveUrl: file.getUrl(),
      fileId: file.getId(),
      fileName: file.getName(),
      mimeType: cloudinaryUrl ? 'audio/mp4' : 'audio/wav', // M4A 格式
      text: text,
      isPlayable: !!cloudinaryUrl, // 是否可在 LINE 中播放
      cloudinaryError: cloudinaryError,
      audioParams: audioParams
    };
    
  } catch (error) {
    console.error('TTS 生成錯誤:', error);
    return {
      success: false,
      error: error.message,
      text: text,
      isPlayable: false
    };
  }
}

/**
 * 🎵 解析音頻 MIME 類型參數
 * 例如：audio/L16;codec=pcm;rate=24000
 */
function parseAudioMimeType(mimeType) {
  const params = {
    sampleRate: 24000,  // 預設 24kHz（Gemini 常用）
    bitsPerSample: 16,  // 預設 16 位
    channels: 1,        // 預設單聲道
    format: 'pcm'       // 預設 PCM
  };

  try {
    // 解析 MIME 類型字符串
    if (mimeType.includes('rate=')) {
      const rateMatch = mimeType.match(/rate=(\d+)/);
      if (rateMatch) {
        params.sampleRate = parseInt(rateMatch[1]);
      }
    }

    // L16 表示 16 位線性 PCM
    if (mimeType.includes('L16')) {
      params.bitsPerSample = 16;
      params.format = 'pcm';
    }

    console.log(`🎵 解析的音頻參數: ${params.sampleRate}Hz, ${params.bitsPerSample}位, ${params.channels}聲道`);

  } catch (error) {
    console.log(`⚠️ MIME 類型解析失敗，使用預設參數: ${error.message}`);
  }

  return params;
}

/**
 * 🔧 從 PCM 數據創建 WAV 檔案
 */
function createWAVFromPCM(pcmBytes, audioParams) {
  try {
    const sampleRate = audioParams.sampleRate;
    const bitsPerSample = audioParams.bitsPerSample;
    const numChannels = audioParams.channels;
    const byteRate = sampleRate * numChannels * bitsPerSample / 8;
    const blockAlign = numChannels * bitsPerSample / 8;
    const dataSize = pcmBytes.length;
    const fileSize = 36 + dataSize;

    console.log(`🔧 創建 WAV 標頭: ${sampleRate}Hz, ${bitsPerSample}位, ${numChannels}聲道, 數據大小: ${dataSize} bytes`);

    // 創建 WAV 標頭（44 字節）
    const wavHeader = new ArrayBuffer(44);
    const view = new DataView(wavHeader);

    // RIFF 標頭
    view.setUint8(0, 0x52);  // 'R'
    view.setUint8(1, 0x49);  // 'I'
    view.setUint8(2, 0x46);  // 'F'
    view.setUint8(3, 0x46);  // 'F'
    view.setUint32(4, fileSize, true);  // 檔案大小（小端序）

    // WAVE 格式
    view.setUint8(8, 0x57);   // 'W'
    view.setUint8(9, 0x41);   // 'A'
    view.setUint8(10, 0x56);  // 'V'
    view.setUint8(11, 0x45);  // 'E'

    // fmt 子區塊
    view.setUint8(12, 0x66);  // 'f'
    view.setUint8(13, 0x6D);  // 'm'
    view.setUint8(14, 0x74);  // 't'
    view.setUint8(15, 0x20);  // ' '
    view.setUint32(16, 16, true);  // fmt 區塊大小
    view.setUint16(20, 1, true);   // 音頻格式 (1 = PCM)
    view.setUint16(22, numChannels, true);  // 聲道數
    view.setUint32(24, sampleRate, true);   // 採樣率
    view.setUint32(28, byteRate, true);     // 位元組率
    view.setUint16(32, blockAlign, true);   // 區塊對齊
    view.setUint16(34, bitsPerSample, true); // 每樣本位數

    // data 子區塊
    view.setUint8(36, 0x64);  // 'd'
    view.setUint8(37, 0x61);  // 'a'
    view.setUint8(38, 0x74);  // 't'
    view.setUint8(39, 0x61);  // 'a'
    view.setUint32(40, dataSize, true);  // 數據大小

    // 合併標頭和音頻數據
    const headerBytes = new Uint8Array(wavHeader);
    const combinedBytes = new Uint8Array(headerBytes.length + pcmBytes.length);
    combinedBytes.set(headerBytes, 0);
    combinedBytes.set(pcmBytes, headerBytes.length);

    console.log(`✅ WAV 檔案創建完成，總大小: ${combinedBytes.length} bytes`);

    return Utilities.newBlob(combinedBytes, 'audio/wav', `TTS_${new Date().getTime()}.wav`);

  } catch (error) {
    console.error('WAV 創建失敗:', error);
    // 返回原始數據作為備用
    return Utilities.newBlob(pcmBytes, 'audio/wav', `Raw_TTS_${new Date().getTime()}.wav`);
  }
}

/**
 * 🔧 上傳音頻到 Cloudinary 並轉換格式（最終修復版）
 * 將 WAV 格式轉換為 M4A 格式以符合 LINE Bot 要求
 */
function uploadAudioToCloudinary(audioBlob, originalText) {
  const config = getConfig();
  
  // 檢查 Cloudinary 配置
  if (!config.cloudinaryCloudName || !config.cloudinaryApiKey || !config.cloudinaryApiSecret) {
    throw new Error('請在 APIKEY 工作表中設定 Cloudinary 配置（Cloud Name, API Key, API Secret）');
  }
  
  try {
    console.log(`🌩️ 上傳音頻到 Cloudinary...`);
    
    // 生成唯一的檔案名稱
    const timestamp = new Date().getTime();
    const publicId = `linebot/tts/audio_${timestamp}`;
    
    // 準備上傳參數
    const uploadParams = {
      public_id: publicId,
      resource_type: 'video', // Cloudinary 將音頻視為視頻資源
      format: 'm4a', // 🎯 轉換為 M4A 格式
      timestamp: Math.floor(Date.now() / 1000)
    };
    
    // 生成簽名
    const signature = generateCloudinarySignature(uploadParams, config.cloudinaryApiSecret);
    
    // 準備表單數據（最終修復：確保所有數字參數都是字符串）
    const formData = {
      file: audioBlob,
      public_id: publicId,
      resource_type: 'video',
      format: 'm4a',
      api_key: config.cloudinaryApiKey,
      timestamp: String(uploadParams.timestamp),  // 修復：確保 timestamp 為字符串格式
      signature: signature
    };
    
    console.log(`📤 上傳參數: Public ID = ${publicId}, Format = m4a, Timestamp = ${formData.timestamp}`);
    
    // 上傳到 Cloudinary
    const uploadUrl = `https://api.cloudinary.com/v1_1/${config.cloudinaryCloudName}/video/upload`;
    
    const response = UrlFetchApp.fetch(uploadUrl, {
      method: 'POST',
      payload: formData,
      muteHttpExceptions: true
    });
    
    const responseCode = response.getResponseCode();
    const responseText = response.getContentText();
    
    console.log(`📊 Cloudinary 回應代碼: ${responseCode}`);
    
    if (responseCode !== 200) {
      console.log(`❌ Cloudinary 錯誤詳情: ${responseText}`);
      throw new Error(`Cloudinary 上傳失敗: ${responseCode} - ${responseText}`);
    }
    
    const result = JSON.parse(responseText);
    
    if (!result.secure_url) {
      throw new Error('Cloudinary 未返回有效的 URL');
    }
    
    // 🎯 確保返回 M4A 格式的 URL
    let m4aUrl = result.secure_url;
    if (!m4aUrl.endsWith('.m4a')) {
      m4aUrl = m4aUrl.replace(/\.[^.]+$/, '.m4a');
    }
    
    console.log(`✅ Cloudinary 上傳成功: ${m4aUrl}`);
    logActivity('Cloudinary音頻上傳', `公開ID: ${publicId}, URL: ${m4aUrl}`);
    
    return m4aUrl;
    
  } catch (error) {
    console.error('Cloudinary 上傳錯誤:', error);
    throw error;
  }
}

/**
 * 🔐 生成 Cloudinary 簽名（修復版）
 * 修復：確保所有參數值都以字符串格式參與簽名計算，避免科學記數法問題
 */
function generateCloudinarySignature(params, apiSecret) {
  // 按字母順序排列參數並構建簽名字符串
  const sortedParams = Object.keys(params)
    .filter(key => key !== 'file' && key !== 'api_key')
    .sort()
    .map(key => `${key}=${String(params[key])}`)  // 修復：確保參數值為字符串格式，避免科學記數法
    .join('&');
  
  const stringToSign = sortedParams + apiSecret;
  
  console.log(`🔐 簽名字符串: ${stringToSign.substring(0, 100)}...`);  // 調試用日誌
  
  // 使用 SHA-1 生成簽名
  return Utilities.computeDigest(Utilities.DigestAlgorithm.SHA_1, stringToSign)
    .map(byte => ('0' + (byte & 0xFF).toString(16)).slice(-2))
    .join('');
}

/**
 * 🎨 Gemini 圖像生成功能（已優化）
 * 使用 Gemini 2.0 Flash Preview Image Generation 模型生成圖像
 */
function generateImageWithGemini(prompt, imageConfig = {}) {
  try {
    const config = getConfig();
    if (!config.geminiApiKey) {
      throw new Error('需要 Gemini API Key 才能使用圖像生成功能');
    }
    
    console.log(`🎨 開始圖像生成: ${prompt.substring(0, 50)}...`);
    
    // 使用圖像生成模型
    const modelName = getRecommendedModel('image_generation');
    const apiVersion = getModelApiVersion(modelName);
    const url = `https://generativelanguage.googleapis.com/${apiVersion}/models/${modelName}:generateContent?key=${config.geminiApiKey}`;
    
    // 正確的圖像生成配置
    const payload = JSON.stringify({
      contents: [{
        parts: [{ text: prompt }]
      }],
      generationConfig: {
        responseModalities: ["TEXT", "IMAGE"],
        ...imageConfig
      }
    });
    
    const response = UrlFetchApp.fetch(url, {
      method: 'POST',
      contentType: 'application/json',
      payload: payload,
      muteHttpExceptions: true
    });
    
    if (response.getResponseCode() !== 200) {
      throw new Error(`圖像生成 API 錯誤: ${response.getResponseCode()} - ${response.getContentText()}`);
    }
    
    const result = JSON.parse(response.getContentText());

    // 檢查回應格式
    if (!result.candidates || !result.candidates[0]) {
      throw new Error('圖像生成 API 回應格式錯誤');
    }

    // 查找圖像數據
    const imageData = result.candidates[0].content.parts.find(part => part.inlineData);

    if (!imageData) {
      throw new Error('未生成圖像數據');
    }

    // 將 base64 圖像數據轉換為 Blob
    const imageBlob = Utilities.newBlob(
      Utilities.base64Decode(imageData.inlineData.data),
      imageData.inlineData.mimeType,
      `Generated_Image_${new Date().getTime()}.png`
    );

    // 🔧 嘗試上傳到 Cloudinary（圖片）
    let cloudinaryUrl = null;
    let cloudinaryError = null;
    
    try {
      cloudinaryUrl = uploadImageToCloudinary(imageBlob, prompt);
      console.log(`✅ 圖片 Cloudinary 上傳成功: ${cloudinaryUrl}`);
    } catch (cloudError) {
      cloudinaryError = cloudError.message;
      console.log(`⚠️ 圖片 Cloudinary 上傳失敗: ${cloudinaryError}`);
    }

    // 備用：保存到 Google Drive
    const driveFolder = DriveApp.getFolderById(config.folderId);
    const file = driveFolder.createFile(imageBlob);
    file.setName(`Gemini_Generated_${new Date().getTime()}.png`);

    console.log(`✅ 圖像生成成功，檔案 ID: ${file.getId()}`);

    return {
      success: true,
      // 🎯 優先使用 Cloudinary URL（更穩定的圖片顯示）
      imageUrl: cloudinaryUrl || file.getUrl(),
      cloudinaryUrl: cloudinaryUrl,
      driveUrl: file.getUrl(),
      fileId: file.getId(),
      fileName: file.getName(),
      mimeType: imageData.inlineData.mimeType,
      prompt: prompt,
      cloudinaryError: cloudinaryError
    };
    
  } catch (error) {
    console.error('圖像生成錯誤:', error);
    return {
      success: false,
      error: error.message,
      prompt: prompt
    };
  }
}

/**
 * 🔧 上傳圖片到 Cloudinary（修復版）
 */
function uploadImageToCloudinary(imageBlob, originalPrompt) {
  const config = getConfig();
  
  // 檢查 Cloudinary 配置
  if (!config.cloudinaryCloudName || !config.cloudinaryApiKey || !config.cloudinaryApiSecret) {
    throw new Error('請在 APIKEY 工作表中設定 Cloudinary 配置');
  }
  
  try {
    console.log(`🌩️ 上傳圖片到 Cloudinary...`);
    
    // 生成唯一的檔案名稱
    const timestamp = new Date().getTime();
    const publicId = `linebot/images/generated_${timestamp}`;
    
    // 準備上傳參數
    const uploadParams = {
      public_id: publicId,
      timestamp: Math.floor(Date.now() / 1000)
    };
    
    // 生成簽名
    const signature = generateCloudinarySignature(uploadParams, config.cloudinaryApiSecret);
    
    // 準備表單數據（修復：確保 timestamp 為字符串）
    const formData = {
      file: imageBlob,
      public_id: publicId,
      api_key: config.cloudinaryApiKey,
      timestamp: String(uploadParams.timestamp),  // 修復：確保 timestamp 為字符串格式
      signature: signature
    };
    
    // 上傳到 Cloudinary
    const uploadUrl = `https://api.cloudinary.com/v1_1/${config.cloudinaryCloudName}/image/upload`;
    
    const response = UrlFetchApp.fetch(uploadUrl, {
      method: 'POST',
      payload: formData,
      muteHttpExceptions: true
    });
    
    if (response.getResponseCode() !== 200) {
      throw new Error(`Cloudinary 圖片上傳失敗: ${response.getResponseCode()} - ${response.getContentText()}`);
    }
    
    const result = JSON.parse(response.getContentText());
    
    if (!result.secure_url) {
      throw new Error('Cloudinary 未返回有效的圖片 URL');
    }
    
    console.log(`✅ Cloudinary 圖片上傳成功: ${result.secure_url}`);
    logActivity('Cloudinary圖片上傳', `公開ID: ${publicId}, URL: ${result.secure_url}`);
    
    return result.secure_url;
    
  } catch (error) {
    console.error('Cloudinary 圖片上傳錯誤:', error);
    throw error;
  }
}

/**
 * 🔍 提取 TTS 轉換文本 - 使用統一提示詞系統
 */
function extractTextForTTS(userInput) {
  try {
    // 🎯 使用統一提示詞系統
    const extractedText = callAIWithPrompt('TTS_TEXT_EXTRACTION', {
      message: userInput
    });

    return extractedText.trim() || userInput;

  } catch (error) {
    console.error('TTS 文本提取失敗:', error);
    // 備用：簡單的文本處理
    let text = userInput
      .replace(/語音|念出來|讀出來|tts|說出來|播放|幫我|你說|你念/gi, '')
      .replace(/^[：:]\s*/, '')
      .trim();

    return text || userInput;
  }
}

/**
 * 🎨 提取圖像生成描述 - 使用統一提示詞系統
 */
function extractImagePrompt(userInput) {
  try {
    // 🎯 使用統一提示詞系統
    const extractedPrompt = callAIWithPrompt('IMAGE_PROMPT_EXTRACTION', {
      message: userInput
    });

    return extractedPrompt.trim() || userInput;

  } catch (error) {
    console.error('圖像描述提取失敗:', error);
    // 備用：簡單的文本處理
    let prompt = userInput
      .replace(/畫|生成圖|創建圖|圖像|畫圖|generate image|draw|create image|幫我|給我.*圖/gi, '')
      .replace(/^[：:]\s*/, '')
      .trim();

    return prompt || userInput;
  }
}

// ===== 🧪 測試函數 =====

/**
 * 🧪 測試語音功能修復
 */
function testVoiceFunctionsFix_debug() {
  console.log('🧪 === 測試語音功能修復 ===');
  
  try {
    // 1. 測試模型選擇修復
    console.log('1️⃣ 測試模型選擇...');
    const ttsModel = getRecommendedModel('tts');
    const audioDialogModel = getRecommendedModel('audio_dialog');
    
    console.log(`   TTS 模型: ${ttsModel}`);
    console.log(`   對話音頻模型: ${audioDialogModel}`);
    
    // 2. 測試 TTS 功能
    console.log('\n2️⃣ 測試 TTS 功能...');
    const ttsResult = callGeminiTTS('這是修復測試');
    console.log(`   TTS 結果: ${ttsResult.success ? '✅ 成功' : '❌ 失敗'}`);
    if (!ttsResult.success) {
      console.log(`   TTS 錯誤: ${ttsResult.error}`);
    }
    
    // 3. 測試對話音頻功能
    console.log('\n3️⃣ 測試對話音頻功能...');
    const audioDialogResult = callGeminiAudioDialog('你好，今天天氣如何？');
    console.log(`   對話音頻結果: ${audioDialogResult.success ? '✅ 成功' : '❌ 失敗'}`);
    if (!audioDialogResult.success) {
      console.log(`   對話音頻錯誤: ${audioDialogResult.error}`);
    }
    
    // 4. 檢查配置
    console.log('\n4️⃣ 檢查配置...');
    const config = getConfig();
    console.log(`   Gemini API Key: ${config.geminiApiKey ? '✅ 已設定' : '❌ 未設定'}`);
    console.log(`   Cloudinary: ${config.cloudinaryCloudName ? '✅ 已設定' : '❌ 未設定'}`);
    
    // 5. 總結
    console.log('\n📊 修復總結:');
    const overallSuccess = ttsResult.success || audioDialogResult.success;
    console.log(`   整體狀態: ${overallSuccess ? '✅ 至少一個功能正常' : '❌ 都有問題'}`);
    
    if (overallSuccess) {
      console.log('🎉 語音功能修復成功！請在 LINE 中測試 "!語音聊天 你好"');
    } else {
      console.log('❌ 語音功能仍有問題，請檢查 API Key 和網路連線');
    }
    
    return {
      success: overallSuccess,
      ttsResult: ttsResult,
      audioDialogResult: audioDialogResult,
      models: {
        tts: ttsModel,
        audioDialog: audioDialogModel
      }
    };
    
  } catch (error) {
    console.error('❌ 語音功能測試失敗:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 🧪 測試修復的 TTS 功能
 */
function testFixedTTS_debug() {
  console.log('🧪 === 測試修復的 TTS 功能 ===');
  
  try {
    const testText = '這是修復後的語音測試';
    console.log(`🔊 測試文本: "${testText}"`);
    
    const result = textToSpeechWithGemini(testText);
    
    console.log('📊 TTS 修復測試結果:');
    console.log(`   成功: ${result.success}`);
    
    if (result.success) {
      console.log(`   檔案名稱: ${result.fileName}`);
      console.log(`   可播放: ${result.isPlayable}`);
      console.log(`   Cloudinary URL: ${result.cloudinaryUrl || '無'}`);
      console.log(`   Drive URL: ${result.driveUrl}`);
      
      if (result.audioParams) {
        console.log(`   音頻參數: ${result.audioParams.sampleRate}Hz, ${result.audioParams.bitsPerSample}位`);
      }
      
      if (result.isPlayable) {
        console.log('🎉 修復成功！音頻應該可在 LINE 中播放');
      } else {
        console.log('⚠️ Cloudinary 上傳失敗，但 WAV 檔案已修復');
        console.log(`   錯誤: ${result.cloudinaryError}`);
      }
    } else {
      console.log(`   錯誤: ${result.error}`);
    }
    
    return result;
    
  } catch (error) {
    console.error('❌ TTS 修復測試失敗:', error);
    return { success: false, error: error.message };
  }
}

/**
 * 🧪 完整的修復驗證測試
 */
function verifyTTSFix_debug() {
  console.log('🧪 === 完整的 TTS 修復驗證 ===');
  
  try {
    // 1. 測試修復的 TTS
    console.log('1️⃣ 測試修復的 TTS 功能...');
    const ttsResult = testFixedTTS_debug();
    
    // 2. 測試 Cloudinary
    console.log('\n2️⃣ 驗證 Cloudinary 功能...');
    const config = getConfig();
    const hasCloudinary = config.cloudinaryCloudName && config.cloudinaryApiKey && config.cloudinaryApiSecret;
    console.log(`   Cloudinary 配置: ${hasCloudinary ? '✅ 完整' : '❌ 不完整'}`);
    
    // 3. 總結
    console.log('\n📊 修復驗證總結:');
    console.log(`   TTS API: ${ttsResult.success ? '✅ 正常' : '❌ 失敗'}`);
    console.log(`   WAV 修復: ✅ 已實現`);
    console.log(`   Cloudinary: ${hasCloudinary ? '✅ 已配置' : '❌ 待配置'}`);
    console.log(`   音頻播放: ${ttsResult.isPlayable ? '✅ 可播放' : '⚠️ 需要 Cloudinary'}`);
    
    if (ttsResult.success && ttsResult.isPlayable) {
      console.log('\n🎉 修復完成！語音功能應該完全正常');
      console.log('💡 現在可以在 LINE 中測試「你說 你好嗎」');
    } else if (ttsResult.success) {
      console.log('\n⚠️ 部分修復完成，WAV 檔案可播放但需要下載');
      console.log('💡 設定 Cloudinary 可實現 LINE 中直接播放');
    } else {
      console.log('\n❌ 修復未完成，請檢查錯誤訊息');
    }
    
    return ttsResult;
    
  } catch (error) {
    console.error('❌ 修復驗證失敗:', error);
    return { success: false, error: error.message };
  }
}
