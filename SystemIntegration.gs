// == 系統整合：AI-First 升級方案 ==
// 將現有系統平滑升級到 AI-First 架構

/**
 * 🚀 主要整合函數：選擇使用新舊系統
 * 提供漸進式升級路徑
 */
function handleTextMessage(text, replyToken, userId, sourceType) {
  try {
    const config = getConfig();
    
    // 🤖 如果有 Gemini API Key，使用 AI-First 系統
    if (config.geminiApiKey && isAIFirstEnabled()) {
      console.log('🧠 使用 AI-First 文字處理系統');
      return handleTextMessageAIFirst(text, replyToken, userId, sourceType);
    } else {
      console.log('⚡ 使用傳統命令匹配系統（備用）');
      return handleTextMessageLegacy(text, replyToken, userId, sourceType);
    }
    
  } catch (error) {
    console.error('文字處理路由錯誤:', error);
    // 錯誤時回退到簡單回應
    if (replyToken) {
      replyMessage(replyToken, '🤖 抱歉，處理訊息時遇到問題。請稍後再試或輸入「幫助」查看功能說明。');
    }
  }
}

/**
 * 🔧 檢查是否啟用 AI-First 模式
 */
function isAIFirstEnabled() {
  try {
    // 可以通過 Google Sheets 中的設定來控制
    const sheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName('APIKEY');
    if (sheet) {
      // 檢查是否有「AI-First 模式」設定（可選）
      const aiFirstSetting = sheet.getRange('B16').getValue(); // 假設在 B16
      if (aiFirstSetting !== undefined && aiFirstSetting !== '') {
        return aiFirstSetting.toString().toLowerCase() === 'true' || aiFirstSetting === '✅';
      }
    }
    
    // 預設啟用 AI-First（如果有 Gemini API）
    return true;
    
  } catch (error) {
    console.error('檢查 AI-First 設定錯誤:', error);
    return true; // 預設啟用
  }
}

/**
 * ⚡ 傳統系統（重新命名現有函數作為備用）
 */
function handleTextMessageLegacy(text, replyToken, userId, sourceType) {
  // 這裡放入您現有的 TextProcessor.gs 邏輯作為備用
  // 當 AI 系統不可用時使用
  
  try {
    let responseText = '';
    const normalizedText = normalizeText(text);
    const commandText = normalizeCommand(normalizedText);
    const cleanText = commandText.startsWith('!') ? commandText.slice(1) : commandText;
    
    // 簡化的備用路由
    if (normalizedText.includes('drive.google.com') || normalizedText.includes('docs.google.com')) {
      responseText = handleGoogleDriveLink(normalizedText, userId, sourceType);
    } else if (cleanText.startsWith('記錄 ') || commandText.startsWith('!記錄 ')) {
      responseText = handleNoteCommand(cleanText, commandText, userId, sourceType);
    } else if (cleanText === '幫助' || cleanText === 'help') {
      responseText = '🤖 智能助理功能\\n\\n✨ 請直接用自然語言告訴我您的需求！\\n• 想記錄：「幫我記住...」\\n• 想查詢：直接提問\\n• 檔案分析：直接上傳檔案\\n\\n💡 我會用 AI 理解您的意圖，不需要特定命令格式！';
    } else if (cleanText === '測試' || cleanText === 'test') {
      responseText = generateTestResponse(sourceType);
    } else if (config.geminiApiKey) {
      // 即使在備用模式，也嘗試使用 AI 回答一般問題
      responseText = callGeminiWithSmartSearch(cleanText);
    } else {
      responseText = '🤖 我是您的智能助理！\\n\\n💡 請直接告訴我您想做什麼：\\n• 記錄筆記\\n• 查詢資訊\\n• 分析檔案\\n\\n📝 或者上傳檔案讓我幫您分析！';
    }
    
    // 記錄對話
    memoryMiddleware('record_conversation', {
      userId: userId,
      originalMessage: normalizedText,
      botResponse: responseText,
      sourceType: sourceType,
      mode: 'legacy'
    });
    
    if (replyToken) {
      replyMessage(replyToken, responseText);
      logActivity('備用模式回覆', `用戶${userId}: ${cleanText.substring(0, 30)}...`);
    }
    
    return responseText;
    
  } catch (error) {
    console.error('備用模式處理錯誤:', error);
    return '🤖 抱歉，處理訊息時遇到問題。請稍後再試。';
  }
}

/**
 * 🧪 測試新舊系統切換
 */
function testSystemSwitching() {
  console.log('=== 測試 AI-First 與傳統系統切換 ===');
  
  const testMessages = [
    '幫我記住買牛奶',
    '今天天氣如何',
    '記錄 開會時間',
    'help'
  ];
  
  testMessages.forEach((message, index) => {
    console.log(`\\n測試訊息 ${index + 1}: "${message}"`);
    
    try {
      // 測試 AI-First 模式
      console.log('🧠 AI-First 模式:');
      handleTextMessageAIFirst(message, null, 'test-user', 'user');
      
      // 測試傳統模式
      console.log('⚡ 傳統模式:');
      handleTextMessageLegacy(message, null, 'test-user', 'user');
      
    } catch (error) {
      console.log(`測試失敗: ${error.message}`);
    }
  });
  
  console.log('\\n✅ 系統切換測試完成');
}

/**
 * 🔧 在 APIKEY 工作表中添加 AI-First 設定
 */
function addAIFirstSetting() {
  try {
    const sheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName('APIKEY');
    if (!sheet) {
      throw new Error('找不到 APIKEY 工作表');
    }
    
    // 在第16行添加 AI-First 模式設定
    sheet.getRange('A16').setValue('AI-First 模式');
    sheet.getRange('B16').setValue('✅');
    sheet.getRange('C16').setValue('啟用 AI 智能理解（推薦）');
    
    // 設定格式
    sheet.getRange('A16').setFontWeight('bold');
    sheet.getRange('B16').setBackground('#f0f8ff');
    
    // 添加下拉選單
    const aiFirstRule = SpreadsheetApp.newDataValidation()
      .requireValueInList(['✅', '❌'], true)
      .setAllowInvalid(false)
      .setHelpText('✅ 啟用 AI-First 智能理解\\n❌ 使用傳統命令匹配（備用）')
      .build();
    
    sheet.getRange('B16').setDataValidation(aiFirstRule);
    
    console.log('✅ 已添加 AI-First 模式設定到 APIKEY 工作表');
    logActivity('系統設定', '已添加 AI-First 模式開關');
    
    return '✅ AI-First 模式設定已添加';
    
  } catch (error) {
    console.error('添加 AI-First 設定失敗:', error);
    return `❌ 設定失敗: ${error.message}`;
  }
}

/**
 * 📊 比較新舊系統的回應差異
 */
function compareSystemResponses() {
  console.log('=== 比較 AI-First 與傳統系統回應 ===');
  
  const testCases = [
    { input: '我想記住明天的會議', expected_intent: 'note_taking' },
    { input: '幫我記錄一下重要事項', expected_intent: 'note_taking' },
    { input: '記錄 買牛奶', expected_intent: 'note_taking' },
    { input: '今天台北天氣如何？', expected_intent: 'general_question' },
    { input: '我們剛才聊了什麼？', expected_intent: 'conversation_review' }
  ];
  
  testCases.forEach((testCase, index) => {
    console.log(`\\n測試案例 ${index + 1}: "${testCase.input}"`);
    console.log(`預期意圖: ${testCase.expected_intent}`);
    
    try {
      // AI-First 系統分析
      analyzeUserIntentWithAI(testCase.input, 'user', 'test-user').then(intent => {
        console.log(`🧠 AI 分析結果: ${intent.primary_intent} (信心度: ${intent.confidence}%)`);
        console.log(`🎯 AI 理解: ${intent.natural_language_summary}`);
      });
      
    } catch (error) {
      console.log(`分析失敗: ${error.message}`);
    }
  });
  
  console.log('\\n✅ 系統比較完成');
}
