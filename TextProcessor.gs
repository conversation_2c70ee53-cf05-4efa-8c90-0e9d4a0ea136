// == 文字處理與命令解析模組 ==
// 專門處理文字訊息的解析、命令識別和回應生成
// ✨ AI First 設計：充分利用 Gemini AI 理解用戶意圖

// 1. 處理文字訊息主函數
function handleTextMessage(text, replyToken, userId, sourceType) {
  try {
    let responseText = '';
    let relatedFileId = null;
    
    // 統一處理全形半形符號和多語言指令
    const normalizedText = normalizeText(text);
    const commandText = normalizeCommand(normalizedText);
    
    // 移除群組中的 "!" 前綴進行處理
    const cleanText = commandText.startsWith('!') ? commandText.slice(1) : commandText;
    
    // 命令路由：按照優先級處理不同類型的命令
    responseText = routeTextCommand(cleanText, normalizedText, commandText, userId, sourceType);
    
    // 記錄對話歷史（使用 Memory_System.gs 的記憶中間件）
    memoryMiddleware('record_conversation', {
      userId: userId,
      originalMessage: normalizedText,
      botResponse: responseText,
      relatedFileId: relatedFileId,
      sourceType: sourceType
    });
    
    if (replyToken) {
      replyMessage(replyToken, responseText);
      logActivity('文字回覆', `回覆給${userId}(${sourceType}): ${responseText.substring(0, 50)}...`);
    }
    
  } catch (error) {
    console.error('處理文字訊息錯誤:', error);
    logActivity('文字訊息錯誤', error.toString());
    
    if (replyToken) {
      replyMessage(replyToken, '⚠️ 處理訊息時發生錯誤，請稍後再試。輸入「help」查看使用說明。');
    }
  }
}

// 2. 🤖 AI驅動的文字命令路由器
function routeTextCommand(cleanText, normalizedText, commandText, userId, sourceType) {
  try {
    // 1. Google Drive 連結檢測（最高優先級）
    if (normalizedText.includes('drive.google.com') || normalizedText.includes('docs.google.com')) {
      return handleGoogleDriveLink(normalizedText, userId, sourceType);
    }
    
    // 2. 筆記功能（高優先級）
    if (cleanText.startsWith('記錄 ') || commandText.startsWith('!記錄 ')) {
      return handleNoteCommand(cleanText, commandText, userId, sourceType);
    }
    
    // 3. 檔案記憶相關指令
    if (cleanText.startsWith('分析 ') || commandText.startsWith('!分析 ') || 
        cleanText.startsWith('查看 ') || commandText.startsWith('!查看 ')) {
      const query = cleanText.replace(/^(分析|查看) /, '');
      return handleFileMemoryQuery(userId, query, sourceType);
    }
    
    // 4. 對話回顧檢測（使用內建函數）
    if (detectConversationReviewRequest(cleanText)) {
      logActivity('對話回顧', `用戶${userId}(${sourceType})請求對話總結: ${cleanText}`);
      return handleConversationReview(userId, cleanText, sourceType);
    }
    
    // 5. 🤖 AI驅動的群組用戶查詢功能（只在群組中啟用）
    if (sourceType === 'group' || sourceType === 'room') {
      // ✨ AI First：讓 Gemini 判斷是否為用戶查詢請求
      const isUserQuery = detectUserQueryWithAI(cleanText);
      if (isUserQuery) {
        logActivity('群組用戶查詢', `用戶${userId}(${sourceType})查詢: ${cleanText}`);
        return handleUserQueryCommand(cleanText, replyToken, userId, sourceType);
      }
    }
    
    // 6. 檢測自然語言檔案引用（使用 Memory_System.js 的函數）
    if (detectFileReference(cleanText)) {
      return handleFileReference(userId, cleanText, sourceType);
    }
    
    // 7. Threads 發文功能
    if (cleanText.startsWith('t ') || commandText.startsWith('!t ')) {
      return handleThreadsCommand(cleanText, commandText, userId, sourceType);
    }
    
    // 8. 幫助系統
    if (cleanText === '幫助' || cleanText === 'help' || commandText === '!幫助' || commandText === '!help') {
      return generateHelpMessage(sourceType);
    }
    
    // 9. 測試功能
    if (cleanText === '測試' || cleanText === 'test' || commandText === '!測試' || commandText === '!test') {
      return generateTestResponse(sourceType);
    }
    
    // 10. 非指令問題 - 呼叫 AI 智能回答（使用 AI_Features.gs）
    return handleGeneralQuery(cleanText, sourceType);
    
  } catch (error) {
    console.error('文字命令路由錯誤:', error);
    logActivity('命令路由錯誤', error.toString());
    return generateFallbackMessage(sourceType, cleanText);
  }
}

// 🤖 AI驅動的用戶查詢檢測
function detectUserQueryWithAI(text) {
  try {
    const config = getConfig();
    
    // 如果有 Gemini API，使用 AI 判斷
    if (config.geminiApiKey) {
      try {
        const prompt = `判斷以下文字是否為「查詢群組成員發言記錄」的請求：

用戶輸入：「${text}」

這是否為以下類型的查詢請求？
1. 詢問特定用戶說了什麼
2. 詢問群組聊天內容
3. 要求分析某人的發言
4. 詢問群組對話記錄

請只回答：是 或 否`;

        const aiResponse = callGemini(prompt, 'general');
        const isQuery = aiResponse.toLowerCase().includes('是');
        
        console.log(`AI判斷「${text}」是否為用戶查詢: ${isQuery ? '是' : '否'}`);
        return isQuery;
        
      } catch (error) {
        console.error('AI查詢檢測錯誤:', error);
        // AI 失敗時使用簡單關鍵字檢測作為備用
        return detectUserQueryFallback(text);
      }
    } else {
      // 沒有 API 時使用簡單關鍵字檢測
      return detectUserQueryFallback(text);
    }
    
  } catch (error) {
    console.error('用戶查詢檢測錯誤:', error);
    return false;
  }
}

// 📝 簡化的備用檢測（僅關鍵字）
function detectUserQueryFallback(text) {
  const queryKeywords = [
    '講了', '說了', '聊了', '討論', '發言', '有道理', '對不對', '說得對',
    '都講', '都說', '都聊', '在聊', '在說', '在討論', '覺得', '分析'
  ];
  
  return queryKeywords.some(keyword => text.includes(keyword));
}

// 3. 處理筆記命令
function handleNoteCommand(cleanText, commandText, userId, sourceType) {
  try {
    const content = cleanText.startsWith('記錄 ') ? cleanText.slice(3) : commandText.slice(4);
    
    if (content.trim() === '') {
      return `❌ 筆記內容不能為空！\n\n📝 正確格式：\n• 記錄 您的筆記內容\n• note 您的筆記內容\n• !記錄 您的筆記內容（群組中）\n\n💡 範例：\n記錄 今天要買牛奶\nnote 會議重點`;
    }
    
    saveTextNote(userId, content, sourceType);
    return `✅ 筆記已儲存！\n\n📝 內容：${content.substring(0, 50)}${content.length > 50 ? '...' : ''}\n💾 已儲存到「筆記暫存」工作表`;
  } catch (error) {
    console.error('處理筆記命令錯誤:', error);
    return '❌ 儲存筆記時發生錯誤，請稍後再試';
  }
}

// 4. 處理 Threads 發文命令
function handleThreadsCommand(cleanText, commandText, userId, sourceType) {
  try {
    const content = cleanText.startsWith('t ') ? cleanText.slice(2) : cleanText.slice(3);
    
    if (content.trim() === '') {
      return `❌ 發文內容不能為空！\n\n📝 正確格式：\n• t 您要發布的內容\n• threads 您要發布的內容\n• !t 您要發布的內容（群組中）`;
    }
    
    logActivity('Threads發文', `用戶${userId}(${sourceType}): ${content}`);
    return `✅ 已收到 Threads 發文指令：${content}\n📝 功能開發中，敬請期待...`;
  } catch (error) {
    console.error('處理Threads命令錯誤:', error);
    return '❌ 處理發文命令時發生錯誤';
  }
}

// 5. 生成測試回應
function generateTestResponse(sourceType) {
  try {
    const config = getConfig();
    const versionInfo = getVersionInfo(); // 獲取版本資訊
    
    return `✅ 系統運作正常！\n🤖 您的 LINE Bot 已成功連接！\n🏷️ 版本：${versionInfo.version}\n📍 來源類型：${sourceType === 'group' ? '群組' : sourceType === 'room' ? '聊天室' : '個人對話'}\n🧠 記憶系統：已啟用\n🔍 Google搜尋：${config.googleSearchApiKey ? '已啟用' : '未設定'}\n🤖 AI模型：一般功能=${config.generalModel}, 圖片分析=${config.visionModel}`;
  } catch (error) {
    console.error('生成測試回應錯誤:', error);
    return '✅ 系統基本運作正常！\n🤖 LINE Bot 已連接';
  }
}

// 6. 處理一般查詢
function handleGeneralQuery(cleanText, sourceType) {
  try {
    const config = getConfig();
    
    if (config.geminiApiKey) {
      try {
        return callGeminiWithSmartSearch(cleanText);   // 使用AI_Features.gs的智能搜尋
      } catch (error) {
        console.error('Gemini 回答失敗:', error);
        return generateFallbackMessage(sourceType, cleanText);
      }
    } else {
      return generateFallbackMessage(sourceType, cleanText);
    }
  } catch (error) {
    console.error('處理一般查詢錯誤:', error);
    return generateFallbackMessage(sourceType, cleanText);
  }
}

// 7. 生成幫助訊息
function generateHelpMessage(sourceType) {
  try {
    const isGroup = sourceType === 'group' || sourceType === 'room';
    const prefix = isGroup ? '!' : '';
    const config = getConfig();
    
    let helpMessage = `🤖 LINE Bot 功能指南\n\n` +
      `📝 **筆記功能**\n` +
      `• ${prefix}記錄 內容 - 儲存筆記\n` +
      `• ${prefix}note 內容 - 儲存筆記（英文）\n\n` +
      `📱 **Threads 發文**\n` +
      `• ${prefix}t 內容 - 發布到 Threads\n` +
      `• ${prefix}threads 內容 - 發布到 Threads\n\n` +
      `📄 **檔案處理**\n` +
      `• 上傳圖片/檔案 - AI 智慧分析\n` +
      `• 上傳 TXT 檔案 - 內容分析\n` +
      `• 分享 Google Drive 連結 - 讀取檔案\n\n` +
      `🧠 **檔案記憶功能** 🆕\n` +
      `• 剛才的圖片如何？ - 自然語言檔案引用\n` +
      `• ${prefix}分析 檔案名稱 - 延遲分析檔案\n` +
      `• ${prefix}查看 最新圖片 - 查看最近檔案\n\n` +
      `🧠 **對話記憶功能** 🆕\n` +
      `• 我們聊了什麼？ - 自動對話回顧\n` +
      `• 總結一下 - AI智能對話總結\n` +
      `• 回顧對話 - 查看聊天歷史\n\n`;
    
    // 🆕 群組專用功能
    if (isGroup) {
      helpMessage += 
        `🔍 **群組查詢功能** 🆕 (AI智能識別)\n` +
        `• ${prefix}張三都講了些什麼？ - 查看成員發言\n` +
        `• ${prefix}你覺得李四說的有道理嗎？ - AI分析發言\n` +
        `• ${prefix}我們剛才在聊什麼？ - 查看群組對話\n` +
        `• ${prefix}大家討論了什麼？ - 群組討論摘要\n` +
        `• 🤖 AI自動理解各種表達方式\n\n`;
    }
    
    helpMessage += 
      `🔧 **系統功能**\n` +
      `• ${prefix}測試 / ${prefix}test - 檢查狀態\n` +
      `• ${prefix}幫助 / ${prefix}help - 顯示此說明\n\n` +
      `🧠 **智能問答** 🆕\n` +
      `• 直接提問 - AI 智能回答\n` +
      `• 自動Google搜尋 - AI自動判斷搜尋需求\n` +
      `• 例如：今天台股走勢如何？\n\n` +
      `🤖 **AI模型**：一般功能=${config.generalModel || 'gemini-1.5-flash'}, 圖片分析=${config.visionModel || 'gemini-1.5-pro'}\n` +
      `💡 **多語言支援**：支援繁體、簡體、英文指令\n` +
      `✨ **AI First 設計**：智能理解您的意圖，無需記憶複雜指令\n` +
      `${isGroup ? '📍 群組中所有指令都要加「!」前綴，會自動記錄所有發言' : '📍 個人對話中可省略「!」前綴'}`;
    
    return helpMessage;
  } catch (error) {
    console.error('生成幫助訊息錯誤:', error);
    return '🤖 LINE Bot 功能指南\n\n• 輸入「測試」檢查系統狀態\n• 輸入「記錄 內容」儲存筆記\n• 上傳檔案進行AI分析\n• 直接提問獲得AI回答';
  }
}

// 8. 後備回應訊息
function generateFallbackMessage(sourceType, userText) {
  try {
    const isGroup = sourceType === 'group' || sourceType === 'room';
    const prefix = isGroup ? '!' : '';
    
    // 🤖 AI驅動的智慧建議系統
    let suggestion = '';
    const config = getConfig();
    
    if (config.geminiApiKey && userText) {
      try {
        const prompt = `用戶輸入：「${userText}」

請推測用戶可能想要使用以下哪種功能：
1. 筆記記錄 - 記錄想法或待辦事項
2. 檔案查詢 - 詢問之前上傳的檔案
3. 對話回顧 - 查看聊天歷史
4. 群組查詢 - 詢問群組成員發言 (僅群組)
5. 一般問答 - 需要AI回答的問題

請提供簡潔的建議，格式：「💡 您是要...嗎？正確格式：...」`;

        const aiSuggestion = callGemini(prompt, 'general');
        suggestion = `\n\n${aiSuggestion}`;
        
      } catch (error) {
        console.error('AI建議生成失敗:', error);
        // 使用傳統關鍵字建議作為備用
        suggestion = generateTraditionalSuggestion(userText, prefix, isGroup);
      }
    } else {
      suggestion = generateTraditionalSuggestion(userText, prefix, isGroup);
    }
    
    return `🤖 我是您的智能助理！\n\n🔧 可用功能：\n• ${prefix}help - 查看完整功能說明\n• ${prefix}記錄 內容 - 儲存筆記\n• ${prefix}t 內容 - 發布到 Threads\n• 上傳檔案 - AI 分析\n• 分享 Google Drive 連結\n• 直接提問 - AI 智能回答（含自動搜尋）\n• 我們聊了什麼？ - 對話回顧${isGroup ? '\n• 自然語言查詢群組成員發言' : ''}${suggestion}`;
  } catch (error) {
    console.error('生成後備訊息錯誤:', error);
    return '🤖 我是您的智能助理！輸入「help」查看功能說明。';
  }
}

// 9. 傳統關鍵字建議（備用）
function generateTraditionalSuggestion(userText, prefix, isGroup) {
  if (!userText) return '';
  
  if (userText.includes('記錄') || userText.includes('筆記') || userText.includes('note')) {
    return `\n\n💡 您是要使用筆記功能嗎？\n正確格式：${prefix}記錄 內容`;
  } else if (userText.includes('threads') || userText.includes('發文')) {
    return `\n\n💡 您是要發布到 Threads 嗎？\n正確格式：${prefix}t 內容`;
  } else if (userText.includes('聊了') || userText.includes('總結')) {
    return `\n\n💡 您是要回顧對話嗎？\n試試：「總結一下」或「我們聊了什麼？」`;
  } else if (isGroup && (userText.includes('講了') || userText.includes('說什麼'))) {
    return `\n\n💡 您是要查詢群組成員發言嗎？\n🤖 AI會自動理解您的問法，直接用自然語言詢問即可！`;
  }
  
  return '';
}

// 10. 檢測對話回顧請求
function detectConversationReviewRequest(text) {
  try {
    const reviewKeywords = [
      '我們聊了什麼', '聊了什麼', '對話回顧', '回顧對話', '總結一下', '總結對話',
      '聊天記錄', '聊天歷史', '對話歷史', '對話總結', '回顧一下',
      'what did we talk', 'conversation summary', 'chat history', 'review conversation'
    ];
    
    const textLower = text.toLowerCase();
    return reviewKeywords.some(keyword => 
      textLower.includes(keyword.toLowerCase())
    );
  } catch (error) {
    console.error('檢測對話回顧請求錯誤:', error);
    return false;
  }
}

// 11. 處理對話回顧
function handleConversationReview(userId, query, sourceType) {
  try {
    // 獲取用戶最近的對話歷史
    const recentHistory = getRecentConversationHistory(userId, 10);
    
    if (recentHistory.length === 0) {
      return '🤔 暫時沒有找到對話歷史記錄。\n\n💡 開始和我對話後，我就能為您回顧對話內容了！';
    }
    
    // 構建對話總結
    const config = getConfig();
    if (config.geminiApiKey) {
      try {
        const conversationText = recentHistory.map(conv => 
          `用戶: ${conv.message}\n助理: ${conv.response}`
        ).join('\n---\n');
        
        const prompt = `請總結以下對話內容，用繁體中文回答，重點說明討論的主題和重要資訊：

對話記錄：
${conversationText}

請提供：
1. 主要討論話題
2. 重要資訊摘要
3. 用戶關心的重點

總結：`;

        const summary = callGemini(prompt, 'general');
        return `🧠 對話回顧總結\n\n${summary}\n\n📊 統計：共 ${recentHistory.length} 條對話記錄`;
        
      } catch (error) {
        console.error('AI對話總結錯誤:', error);
        // 回退到簡單總結
      }
    }
    
    // 簡單對話回顧（無AI時的備用方案）
    const recentTopics = recentHistory.slice(-3).map((conv, index) => 
      `${index + 1}. ${conv.message.substring(0, 30)}...`
    ).join('\n');
    
    return `🧠 對話回顧\n\n📋 最近討論的話題：\n${recentTopics}\n\n📊 共 ${recentHistory.length} 條對話記錄\n\n💡 設定 Gemini API Key 可啟用智能對話總結`;
    
  } catch (error) {
    console.error('處理對話回顧錯誤:', error);
    return '⚠️ 對話回顧功能暫時無法使用，請稍後再試';
  }
}

// 12. 獲取用戶最近對話歷史（從Memory_System.gs中複製的函數）
function getRecentConversationHistory(userId, limit = 10) {
  try {
    const sheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName('用戶對話歷史');
    if (!sheet) return [];
    
    const data = sheet.getDataRange().getValues();
    const userConversations = data.slice(1) // 跳過標題行
      .filter(row => row[1] === userId) // 篩選該用戶
      .sort((a, b) => new Date(b[0]) - new Date(a[0])) // 按時間倒序
      .slice(0, limit) // 取前N條
      .map(row => ({
        timestamp: row[0],
        message: row[3], // 原始訊息
        response: row[4]  // 處理結果
      }));
    
    return userConversations.reverse(); // 恢復時間順序
  } catch (error) {
    console.error('獲取對話歷史錯誤:', error);
    return [];
  }
}

// 🧪 測試AI查詢檢測功能
function testAIQueryDetection() {
  console.log('=== 測試 AI 驅動的查詢檢測 ===');
  
  const testQueries = [
    '我們剛才在聊什麼',
    '你覺得Taoist-Shih說的有道理嗎',
    '蚵仔煎都講了些什麼',
    '今天天氣如何',
    '記錄 買牛奶',
    '幫我分析張三的發言',
    '大家討論了什麼？'
  ];
  
  testQueries.forEach((query, index) => {
    try {
      const isUserQuery = detectUserQueryWithAI(query);
      console.log(`測試 ${index + 1}: "${query}"`);
      console.log(`  AI判斷結果: ${isUserQuery ? '✅ 是用戶查詢' : '❌ 不是用戶查詢'}`);
      console.log('---');
    } catch (error) {
      console.log(`❌ 測試 ${index + 1} 失敗: ${error.message}`);
    }
  });
  
  console.log('✅ AI 查詢檢測測試完成');
}
